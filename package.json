{"name": "github-monitor", "version": "1.0.0", "description": "GitHub项目更新监控桌面应用", "main": "dist/main/main/index.js", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "tsc -p tsconfig.main.json && electron dist/main/main/index.js", "dev:renderer": "vite", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "vite build", "package": "electron-builder"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "concurrently": "^8.0.0", "electron": "^27.0.0", "electron-builder": "^24.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.0.0", "typescript": "^5.0.0", "vite": "^5.0.0"}, "dependencies": {"@octokit/rest": "^22.0.0", "@reduxjs/toolkit": "^2.0.0", "@vitejs/plugin-react": "^4.7.0", "antd": "^5.0.0", "axios": "^1.0.0", "chart.js": "^4.0.0", "i18next": "^23.0.0", "react": "^18.0.0", "react-chartjs-2": "^5.0.0", "react-dom": "^18.0.0", "react-i18next": "^13.0.0", "react-redux": "^9.0.0"}}