/**
 * 应用菜单模块
 * 负责创建和管理应用菜单
 */

import { Menu, MenuItem, shell, dialog, app } from 'electron';
import { getMainWindow } from './window';

/**
 * 创建应用菜单
 */
export function createApplicationMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: '文件',
      submenu: [
        {
          label: '导入数据',
          accelerator: 'CmdOrCtrl+I',
          click: async () => {
            const mainWindow = getMainWindow();
            if (mainWindow) {
              mainWindow.webContents.send('menu:import-data');
            }
          },
        },
        {
          label: '导出数据',
          accelerator: 'CmdOrCtrl+E',
          click: async () => {
            const mainWindow = getMainWindow();
            if (mainWindow) {
              mainWindow.webContents.send('menu:export-data');
            }
          },
        },
        { type: 'separator' },
        {
          label: '设置',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            const mainWindow = getMainWindow();
            if (mainWindow) {
              mainWindow.webContents.send('menu:open-settings');
            }
          },
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          },
        },
      ],
    },
    {
      label: '编辑',
      submenu: [
        { role: 'undo', label: '撤销' },
        { role: 'redo', label: '重做' },
        { type: 'separator' },
        { role: 'cut', label: '剪切' },
        { role: 'copy', label: '复制' },
        { role: 'paste', label: '粘贴' },
        { role: 'selectAll', label: '全选' },
      ],
    },
    {
      label: '视图',
      submenu: [
        { role: 'reload', label: '重新加载' },
        { role: 'forceReload', label: '强制重新加载' },
        { role: 'toggleDevTools', label: '开发者工具' },
        { type: 'separator' },
        { role: 'resetZoom', label: '实际大小' },
        { role: 'zoomIn', label: '放大' },
        { role: 'zoomOut', label: '缩小' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: '全屏' },
      ],
    },
    {
      label: '窗口',
      submenu: [
        { role: 'minimize', label: '最小化' },
        { role: 'close', label: '关闭' },
      ],
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: async () => {
            const mainWindow = getMainWindow();
            if (mainWindow) {
              await dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: '关于 GitHub Monitor',
                message: 'GitHub Monitor',
                detail: `版本: ${app.getVersion()}\n\nGitHub项目更新监控桌面应用\n基于 Electron + React + TypeScript 开发`,
                buttons: ['确定'],
              });
            }
          },
        },
        {
          label: '项目主页',
          click: async () => {
            await shell.openExternal('https://github.com/your-username/github-monitor');
          },
        },
        { type: 'separator' },
        {
          label: '报告问题',
          click: async () => {
            await shell.openExternal('https://github.com/your-username/github-monitor/issues');
          },
        },
      ],
    },
  ];

  // macOS 特殊处理
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about', label: '关于 GitHub Monitor' },
        { type: 'separator' },
        { role: 'services', label: '服务' },
        { type: 'separator' },
        { role: 'hide', label: '隐藏 GitHub Monitor' },
        { role: 'hideOthers', label: '隐藏其他' },
        { role: 'unhide', label: '显示全部' },
        { type: 'separator' },
        { role: 'quit', label: '退出 GitHub Monitor' },
      ],
    });

    // 窗口菜单
    (template[4].submenu as Electron.MenuItemConstructorOptions[]).push(
      { type: 'separator' },
      { role: 'front', label: '前置所有窗口' }
    );
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

/**
 * 创建右键菜单
 */
export function createContextMenu(): Menu {
  return Menu.buildFromTemplate([
    { role: 'cut', label: '剪切' },
    { role: 'copy', label: '复制' },
    { role: 'paste', label: '粘贴' },
    { type: 'separator' },
    { role: 'selectAll', label: '全选' },
  ]);
}
