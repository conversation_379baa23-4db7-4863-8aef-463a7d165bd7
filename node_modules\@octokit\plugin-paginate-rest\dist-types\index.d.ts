import type { Octokit } from "@octokit/core";
import type { PaginateInterface } from "./types.js";
export type { PaginateInterface, PaginatingEndpoints } from "./types.js";
export { composePaginateRest } from "./compose-paginate.js";
export { isPaginatingEndpoint, paginatingEndpoints, } from "./paginating-endpoints.js";
/**
 * @param octokit Octokit instance
 * @param options Options passed to Octokit constructor
 */
export declare function paginateRest(octokit: Octokit): {
    paginate: PaginateInterface;
};
export declare namespace paginateRest {
    var VERSION: string;
}
