/**
 * 分类状态管理
 * 管理分类相关的状态
 */

import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { Category, CreateCategoryRequest, UpdateCategoryRequest, CategoryType } from '@/shared/types';

// 状态接口
interface CategoryState {
  categories: Category[];
  selectedCategoryId?: string;
  isLoading: boolean;
  error?: string;
  lastUpdated?: string;
}

// 初始状态
const initialState: CategoryState = {
  categories: [],
  selectedCategoryId: undefined,
  isLoading: false,
  error: undefined,
  lastUpdated: undefined,
};

// 异步thunks
export const loadCategories = createAsyncThunk(
  'category/loadCategories',
  async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.data.getCategories();
        if (response && !response.success) {
          throw new Error(response.error || 'Failed to load categories');
        }
        return response?.data || [];
      }
      return [];
    } catch (error) {
      console.error('Failed to load categories:', error);
      return [];
    }
  }
);

export const createCategory = createAsyncThunk(
  'category/createCategory',
  async (categoryData: CreateCategoryRequest) => {
    // 生成新分类
    const newCategory: Category = {
      id: Date.now().toString(),
      name: categoryData.name,
      description: categoryData.description,
      color: categoryData.color || '#1890ff',
      icon: categoryData.icon || 'folder',
      weight: categoryData.weight || 0,
      parentId: categoryData.parentId,
      type: categoryData.type, // 添加分类类型
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        authorCount: 0,
        projectCount: 0,
        tags: categoryData.tags || [],
        notes: categoryData.notes,
      },
    };

    return newCategory;
  }
);

export const updateCategory = createAsyncThunk(
  'category/updateCategory',
  async (updateData: UpdateCategoryRequest) => {
    return updateData;
  }
);

export const deleteCategory = createAsyncThunk(
  'category/deleteCategory',
  async (categoryId: string) => {
    return categoryId;
  }
);

export const saveCategories = createAsyncThunk(
  'category/saveCategories',
  async (categories: Category[]) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.data.setCategories(categories);
        if (response && !response.success) {
          throw new Error(response.error || 'Failed to save categories');
        }
      }
      return categories;
    } catch (error) {
      console.error('Failed to save categories:', error);
      throw error;
    }
  }
);

// 创建slice
const categorySlice = createSlice({
  name: 'category',
  initialState,
  reducers: {
    // 设置选中的分类
    setSelectedCategory: (state, action: PayloadAction<string | undefined>) => {
      state.selectedCategoryId = action.payload;
    },

    // 清除错误
    clearError: (state) => {
      state.error = undefined;
    },

    // 重置分类状态
    resetCategoryState: (state) => {
      state.categories = [];
      state.selectedCategoryId = undefined;
      state.isLoading = false;
      state.error = undefined;
      state.lastUpdated = undefined;
    },

    // 更新分类元数据
    updateCategoryMetadata: (state, action: PayloadAction<{ categoryId: string; metadata: Partial<Category['metadata']> }>) => {
      const { categoryId, metadata } = action.payload;
      const category = state.categories.find(c => c.id === categoryId);
      if (category) {
        category.metadata = { ...category.metadata, ...metadata };
        category.updatedAt = new Date().toISOString();
      }
    },
  },
  extraReducers: (builder) => {
    // 加载分类
    builder
      .addCase(loadCategories.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(loadCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(loadCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // 创建分类
    builder
      .addCase(createCategory.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(createCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories.push(action.payload);
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(createCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // 更新分类
    builder
      .addCase(updateCategory.fulfilled, (state, action) => {
        const { id, ...updateData } = action.payload;
        const categoryIndex = state.categories.findIndex(c => c.id === id);
        if (categoryIndex !== -1) {
          state.categories[categoryIndex] = {
            ...state.categories[categoryIndex],
            ...updateData,
            updatedAt: new Date().toISOString(),
          };
        }
        state.lastUpdated = new Date().toISOString();
      });

    // 删除分类
    builder
      .addCase(deleteCategory.fulfilled, (state, action) => {
        state.categories = state.categories.filter(c => c.id !== action.payload);
        if (state.selectedCategoryId === action.payload) {
          state.selectedCategoryId = undefined;
        }
        state.lastUpdated = new Date().toISOString();
      });

    // 保存分类
    builder
      .addCase(saveCategories.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(saveCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(saveCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

// 导出actions
export const {
  setSelectedCategory,
  clearError,
  resetCategoryState,
  updateCategoryMetadata,
} = categorySlice.actions;

// 导出selectors
export const selectCategories = (state: { category: CategoryState }) => state.category.categories;
export const selectSelectedCategoryId = (state: { category: CategoryState }) => state.category.selectedCategoryId;
export const selectSelectedCategory = (state: { category: CategoryState }) => 
  state.category.categories.find(c => c.id === state.category.selectedCategoryId);
export const selectCategoryById = (state: { category: CategoryState }, categoryId: string) =>
  state.category.categories.find(c => c.id === categoryId);
export const selectCategoriesLoading = (state: { category: CategoryState }) => state.category.isLoading;
export const selectCategoriesError = (state: { category: CategoryState }) => state.category.error;
export const selectActiveCategoriesCount = (state: { category: CategoryState }) => 
  state.category.categories.filter(c => c.isActive).length;

// 导出reducer
export default categorySlice.reducer;
