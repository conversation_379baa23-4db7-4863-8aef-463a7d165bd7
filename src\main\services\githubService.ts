/**
 * GitHub API 服务
 * 负责处理GitHub API相关的操作
 */

// 使用动态导入来避免ES模块问题
// import { Octokit } from '@octokit/rest';

export interface GitHubRepoInfo {
  owner: string;
  name: string;
  fullName: string;
  description: string | null;
  url: string;
  homepage: string | null;
  language: string | null;
  stars: number;
  forks: number;
  watchers: number;
  isPrivate: boolean;
  createdAt: string;
  updatedAt: string;
  pushedAt: string;
  defaultBranch: string;
  topics: string[];
}

export class GitHubService {
  private octokit: any;
  private token?: string;

  constructor(token?: string) {
    // 暂时使用fetch API替代Octokit，避免ES模块问题
    this.token = token;
    console.log('GitHubService initialized with token:', token ? 'Yes' : 'No');
  }
  /**
   * 从配置文件加载GitHub Token
   */
  private loadTokenFromConfig() {
    try {
      const { dataManager } = require('./dataManager');
      const config = dataManager.getConfig();
      const configToken = config?.github?.token;

      if (configToken && configToken.trim()) {
        this.token = configToken.trim();
        console.log('Loaded GitHub token from config');
      }
    } catch (error) {
      console.error('Failed to load GitHub token from config:', error);
    }
  }

  /**
   * 更新GitHub Token
   */
  public updateToken(token?: string) {
    this.token = token;
    console.log(`GitHub token updated: ${token ? 'Yes' : 'No'}`);
  }

  /**
   * 从GitHub URL解析仓库信息
   */
  parseGitHubUrl(url: string): { owner: string; repo: string } | null {
    try {
      const cleanUrl = url.trim();
      console.log('Parsing GitHub URL:', cleanUrl);

      // 支持多种GitHub URL格式
      const patterns = [
        // 完整的GitHub URL: https://github.com/owner/repo
        /^https?:\/\/github\.com\/([^\/\s]+)\/([^\/\s]+?)(?:\.git)?(?:\/.*)?$/,
        // 简短格式: owner/repo
        /^([^\/\s]+)\/([^\/\s]+?)(?:\.git)?$/,
      ];

      for (let i = 0; i < patterns.length; i++) {
        const pattern = patterns[i];
        const match = cleanUrl.match(pattern);
        console.log(`Pattern ${i + 1} match:`, match);

        if (match && match[1] && match[2]) {
          const owner = match[1];
          const repo = match[2];

          // 验证owner和repo不为空且不包含特殊字符
          if (owner && repo && owner !== '.' && repo !== '.') {
            const result = {
              owner: owner,
              repo: repo,
            };
            console.log('Parsed result:', result);
            return result;
          }
        }
      }

      console.log('No pattern matched');
      return null;
    } catch (error) {
      console.error('Error parsing GitHub URL:', error);
      return null;
    }
  }

  /**
   * 获取仓库信息
   */
  async getRepositoryInfo(owner: string, repo: string): Promise<GitHubRepoInfo | null> {
    try {
      console.log(`Getting repository info for: ${owner}/${repo}`);
      const url = `https://api.github.com/repos/${owner}/${repo}`;
      const headers: Record<string, string> = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'GitHub-Monitor/1.0.0',
      };

      if (this.token) {
        headers['Authorization'] = `token ${this.token}`;
        console.log('Using GitHub token for authentication');
      } else {
        console.log('No GitHub token available, using unauthenticated requests');
      }

      console.log('Fetching from URL:', url);
      const response = await fetch(url, { headers });

      if (!response.ok) {
        console.error(`GitHub API error: ${response.status} ${response.statusText}`);
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Repository data received:', data.name, data.owner.login);

      return {
        owner: data.owner.login,
        name: data.name,
        fullName: data.full_name,
        description: data.description,
        url: data.html_url,
        homepage: data.homepage,
        language: data.language,
        stars: data.stargazers_count,
        forks: data.forks_count,
        watchers: data.watchers_count,
        isPrivate: data.private,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        pushedAt: data.pushed_at,
        defaultBranch: data.default_branch,
        topics: data.topics || [],
      };
    } catch (error) {
      console.error('Error fetching repository info:', error);
      return null;
    }
  }

  /**
   * 解析GitHub用户URL
   */
  parseGitHubUserUrl(url: string): string | null {
    try {
      const cleanUrl = url.trim();
      console.log('Parsing GitHub user URL:', cleanUrl);

      // 支持多种GitHub用户URL格式
      const patterns = [
        // 完整的GitHub用户URL: https://github.com/username
        /^https?:\/\/github\.com\/([^\/\s]+)(?:\/.*)?$/,
        // 简短格式: username
        /^([^\/\s]+)$/,
      ];

      for (let i = 0; i < patterns.length; i++) {
        const pattern = patterns[i];
        const match = cleanUrl.match(pattern);
        console.log(`User pattern ${i + 1} match:`, match);

        if (match && match[1]) {
          const username = match[1];

          // 验证username不为空且不包含特殊字符
          if (username && username !== '.' && !username.includes('/')) {
            console.log('Parsed username:', username);
            return username;
          }
        }
      }

      console.log('No user pattern matched');
      return null;
    } catch (error) {
      console.error('Error parsing GitHub user URL:', error);
      return null;
    }
  }

  /**
   * 从URL获取仓库信息
   */
  async getRepositoryInfoFromUrl(url: string): Promise<GitHubRepoInfo | null> {
    const parsed = this.parseGitHubUrl(url);
    if (!parsed) {
      return null;
    }

    return this.getRepositoryInfo(parsed.owner, parsed.repo);
  }

  /**
   * 从URL获取用户信息
   */
  async getUserInfoFromUrl(url: string) {
    const username = this.parseGitHubUserUrl(url);
    if (!username) {
      return null;
    }

    return this.getUserInfo(username);
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(username: string) {
    try {
      console.log(`Getting user info for: ${username}`);
      const url = `https://api.github.com/users/${username}`;
      const headers: Record<string, string> = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'GitHub-Monitor/1.0.0',
      };

      if (this.token) {
        headers['Authorization'] = `token ${this.token}`;
        console.log('Using GitHub token for user authentication');
      } else {
        console.log('No GitHub token available for user request');
      }

      console.log('Fetching user from URL:', url);
      const response = await fetch(url, { headers });

      if (!response.ok) {
        console.error(`GitHub API error for user: ${response.status} ${response.statusText}`);
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('User data received:', data.login, data.name);

      return {
        login: data.login,
        name: data.name,
        bio: data.bio,
        company: data.company,
        location: data.location,
        email: data.email,
        blog: data.blog,
        avatarUrl: data.avatar_url,
        htmlUrl: data.html_url,
        followers: data.followers,
        following: data.following,
        publicRepos: data.public_repos,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      };
    } catch (error) {
      console.error('Error fetching user info:', error);
      return null;
    }
  }

  /**
   * 获取用户的仓库列表
   */
  async getUserRepositories(username: string, page = 1, perPage = 100): Promise<any[]> {
    try {
      console.log(`Getting repositories for user: ${username}`);

      // 注意：速率限制检查将在响应中处理

      const url = `https://api.github.com/users/${username}/repos?sort=updated&per_page=${perPage}&page=${page}`;
      const headers: Record<string, string> = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'GitHub-Monitor/1.0.0',
      };

      if (this.token) {
        headers['Authorization'] = `token ${this.token}`;
      }

      const response = await fetch(url, { headers });

      if (!response.ok) {
        if (response.status === 403) {
          const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
          const rateLimitReset = response.headers.get('X-RateLimit-Reset');

          if (rateLimitRemaining === '0' || rateLimitRemaining === null) {
            const resetTime = new Date(parseInt(rateLimitReset || '0') * 1000);
            const resetTimeStr = resetTime.toISOString();

            console.error(`GitHub API rate limit exceeded. Remaining: ${rateLimitRemaining}, Reset at: ${resetTimeStr}`);

            // 返回包含速率限制信息的错误
            const error = new Error(`GitHub API rate limit exceeded. Reset at: ${resetTimeStr}`);
            (error as any).isRateLimit = true;
            (error as any).resetTime = resetTime;
            (error as any).resetTimeStr = resetTimeStr;
            throw error;
          }
        }
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
      }

      const repos = await response.json();
      console.log(`Found ${repos.length} repositories for ${username}`);
      return repos;
    } catch (error) {
      console.error('Error fetching user repositories:', error);

      // 如果是速率限制错误，返回空数组而不是抛出异常
      if (error instanceof Error && error.message.includes('rate limit')) {
        console.warn(`Returning empty array due to rate limit for user: ${username}`);
        return [];
      }

      return [];
    }
  }

  /**
   * 搜索仓库
   */
  async searchRepositories(query: string, page = 1, perPage = 30) {
    try {
      // 暂时返回空结果，后续实现
      return { total: 0, items: [] };
    } catch (error) {
      console.error('Error searching repositories:', error);
      return { total: 0, items: [] };
    }
  }


}

// 导出单例实例
export const githubService = new GitHubService();
