/**
 * GitHub API 服务
 * 负责处理GitHub API相关的操作
 */

import { Octokit } from '@octokit/rest';

export interface GitHubRepoInfo {
  owner: string;
  name: string;
  fullName: string;
  description: string | null;
  url: string;
  homepage: string | null;
  language: string | null;
  stars: number;
  forks: number;
  watchers: number;
  isPrivate: boolean;
  createdAt: string;
  updatedAt: string;
  pushedAt: string;
  topics: string[];
}

export class GitHubService {
  private octokit: Octokit;

  constructor(token?: string) {
    this.octokit = new Octokit({
      auth: token,
    });
  }

  /**
   * 从GitHub URL解析仓库信息
   */
  parseGitHubUrl(url: string): { owner: string; repo: string } | null {
    try {
      // 支持多种GitHub URL格式
      const patterns = [
        /^https?:\/\/github\.com\/([^\/]+)\/([^\/]+)(?:\/.*)?$/,
        /^https?:\/\/github\.com\/([^\/]+)$/,
        /^([^\/]+)\/([^\/]+)$/,
        /^([^\/]+)$/,
      ];

      for (const pattern of patterns) {
        const match = url.trim().match(pattern);
        if (match) {
          if (match[2]) {
            // owner/repo 格式
            return {
              owner: match[1],
              repo: match[2].replace(/\.git$/, ''), // 移除.git后缀
            };
          } else {
            // 只有owner，返回null让用户手动输入repo
            return null;
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Error parsing GitHub URL:', error);
      return null;
    }
  }

  /**
   * 获取仓库信息
   */
  async getRepositoryInfo(owner: string, repo: string): Promise<GitHubRepoInfo | null> {
    try {
      const { data } = await this.octokit.rest.repos.get({
        owner,
        repo,
      });

      return {
        owner: data.owner.login,
        name: data.name,
        fullName: data.full_name,
        description: data.description,
        url: data.html_url,
        homepage: data.homepage,
        language: data.language,
        stars: data.stargazers_count,
        forks: data.forks_count,
        watchers: data.watchers_count,
        isPrivate: data.private,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        pushedAt: data.pushed_at,
        topics: data.topics || [],
      };
    } catch (error) {
      console.error('Error fetching repository info:', error);
      return null;
    }
  }

  /**
   * 从URL获取仓库信息
   */
  async getRepositoryInfoFromUrl(url: string): Promise<GitHubRepoInfo | null> {
    const parsed = this.parseGitHubUrl(url);
    if (!parsed) {
      return null;
    }

    return this.getRepositoryInfo(parsed.owner, parsed.repo);
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(username: string) {
    try {
      const { data } = await this.octokit.rest.users.getByUsername({
        username,
      });

      return {
        login: data.login,
        name: data.name,
        bio: data.bio,
        company: data.company,
        location: data.location,
        email: data.email,
        blog: data.blog,
        avatarUrl: data.avatar_url,
        htmlUrl: data.html_url,
        followers: data.followers,
        following: data.following,
        publicRepos: data.public_repos,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      };
    } catch (error) {
      console.error('Error fetching user info:', error);
      return null;
    }
  }

  /**
   * 获取用户的仓库列表
   */
  async getUserRepositories(username: string, page = 1, perPage = 30) {
    try {
      const { data } = await this.octokit.rest.repos.listForUser({
        username,
        page,
        per_page: perPage,
        sort: 'updated',
        direction: 'desc',
      });

      return data.map(repo => ({
        owner: repo.owner.login,
        name: repo.name,
        fullName: repo.full_name,
        description: repo.description,
        url: repo.html_url,
        homepage: repo.homepage,
        language: repo.language,
        stars: repo.stargazers_count,
        forks: repo.forks_count,
        watchers: repo.watchers_count,
        isPrivate: repo.private,
        createdAt: repo.created_at,
        updatedAt: repo.updated_at,
        pushedAt: repo.pushed_at,
        topics: repo.topics || [],
      }));
    } catch (error) {
      console.error('Error fetching user repositories:', error);
      return [];
    }
  }

  /**
   * 搜索仓库
   */
  async searchRepositories(query: string, page = 1, perPage = 30) {
    try {
      const { data } = await this.octokit.rest.search.repos({
        q: query,
        page,
        per_page: perPage,
        sort: 'stars',
        order: 'desc',
      });

      return {
        total: data.total_count,
        items: data.items.map(repo => ({
          owner: repo.owner.login,
          name: repo.name,
          fullName: repo.full_name,
          description: repo.description,
          url: repo.html_url,
          homepage: repo.homepage,
          language: repo.language,
          stars: repo.stargazers_count,
          forks: repo.forks_count,
          watchers: repo.watchers_count,
          isPrivate: repo.private,
          createdAt: repo.created_at,
          updatedAt: repo.updated_at,
          pushedAt: repo.pushed_at,
          topics: repo.topics || [],
        })),
      };
    } catch (error) {
      console.error('Error searching repositories:', error);
      return { total: 0, items: [] };
    }
  }

  /**
   * 更新token
   */
  updateToken(token: string) {
    this.octokit = new Octokit({
      auth: token,
    });
  }
}

// 导出单例实例
export const githubService = new GitHubService();
