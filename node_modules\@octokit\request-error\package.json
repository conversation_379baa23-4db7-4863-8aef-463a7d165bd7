{"name": "@octokit/request-error", "version": "7.0.0", "publishConfig": {"access": "public", "provenance": true}, "type": "module", "description": "Error class for Octokit request errors", "repository": "github:octokit/request-error.js", "keywords": ["octokit", "github", "api", "error"], "author": "<PERSON> (https://github.com/gr2m)", "license": "MIT", "dependencies": {"@octokit/types": "^14.0.0"}, "devDependencies": {"@octokit/tsconfig": "^4.0.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^3.0.0", "esbuild": "^0.25.0", "glob": "^11.0.0", "prettier": "3.5.3", "tinybench": "^4.0.0", "typescript": "^5.0.0", "vitest": "^3.0.0"}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "./dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-src/index.js", "default": "./dist-src/index.js"}}, "sideEffects": false, "unpkg": "dist-src/index.js"}