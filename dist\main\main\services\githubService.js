"use strict";
/**
 * GitHub API 服务
 * 负责处理GitHub API相关的操作
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.githubService = exports.GitHubService = void 0;
class GitHubService {
    constructor(token) {
        // 暂时使用fetch API替代Octokit，避免ES模块问题
        this.token = token;
    }
    /**
     * 从GitHub URL解析仓库信息
     */
    parseGitHubUrl(url) {
        try {
            // 支持多种GitHub URL格式
            const patterns = [
                /^https?:\/\/github\.com\/([^\/]+)\/([^\/]+)(?:\/.*)?$/,
                /^https?:\/\/github\.com\/([^\/]+)$/,
                /^([^\/]+)\/([^\/]+)$/,
                /^([^\/]+)$/,
            ];
            for (const pattern of patterns) {
                const match = url.trim().match(pattern);
                if (match) {
                    if (match[2]) {
                        // owner/repo 格式
                        return {
                            owner: match[1],
                            repo: match[2].replace(/\.git$/, ''), // 移除.git后缀
                        };
                    }
                    else {
                        // 只有owner，返回null让用户手动输入repo
                        return null;
                    }
                }
            }
            return null;
        }
        catch (error) {
            console.error('Error parsing GitHub URL:', error);
            return null;
        }
    }
    /**
     * 获取仓库信息
     */
    async getRepositoryInfo(owner, repo) {
        try {
            const url = `https://api.github.com/repos/${owner}/${repo}`;
            const headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'GitHub-Monitor/1.0.0',
            };
            if (this.token) {
                headers['Authorization'] = `token ${this.token}`;
            }
            const response = await fetch(url, { headers });
            if (!response.ok) {
                throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            return {
                owner: data.owner.login,
                name: data.name,
                fullName: data.full_name,
                description: data.description,
                url: data.html_url,
                homepage: data.homepage,
                language: data.language,
                stars: data.stargazers_count,
                forks: data.forks_count,
                watchers: data.watchers_count,
                isPrivate: data.private,
                createdAt: data.created_at,
                updatedAt: data.updated_at,
                pushedAt: data.pushed_at,
                topics: data.topics || [],
            };
        }
        catch (error) {
            console.error('Error fetching repository info:', error);
            return null;
        }
    }
    /**
     * 从URL获取仓库信息
     */
    async getRepositoryInfoFromUrl(url) {
        const parsed = this.parseGitHubUrl(url);
        if (!parsed) {
            return null;
        }
        return this.getRepositoryInfo(parsed.owner, parsed.repo);
    }
    /**
     * 获取用户信息
     */
    async getUserInfo(username) {
        try {
            const url = `https://api.github.com/users/${username}`;
            const headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'GitHub-Monitor/1.0.0',
            };
            if (this.token) {
                headers['Authorization'] = `token ${this.token}`;
            }
            const response = await fetch(url, { headers });
            if (!response.ok) {
                throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            return {
                login: data.login,
                name: data.name,
                bio: data.bio,
                company: data.company,
                location: data.location,
                email: data.email,
                blog: data.blog,
                avatarUrl: data.avatar_url,
                htmlUrl: data.html_url,
                followers: data.followers,
                following: data.following,
                publicRepos: data.public_repos,
                createdAt: data.created_at,
                updatedAt: data.updated_at,
            };
        }
        catch (error) {
            console.error('Error fetching user info:', error);
            return null;
        }
    }
    /**
     * 获取用户的仓库列表
     */
    async getUserRepositories(username, page = 1, perPage = 30) {
        try {
            // 暂时返回空数组，后续实现
            return [];
        }
        catch (error) {
            console.error('Error fetching user repositories:', error);
            return [];
        }
    }
    /**
     * 搜索仓库
     */
    async searchRepositories(query, page = 1, perPage = 30) {
        try {
            // 暂时返回空结果，后续实现
            return { total: 0, items: [] };
        }
        catch (error) {
            console.error('Error searching repositories:', error);
            return { total: 0, items: [] };
        }
    }
    /**
     * 更新token
     */
    updateToken(token) {
        this.token = token;
    }
}
exports.GitHubService = GitHubService;
// 导出单例实例
exports.githubService = new GitHubService();
