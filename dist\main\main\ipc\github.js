"use strict";
/**
 * GitHub API IPC处理器
 * 负责处理GitHub API相关的IPC通信
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerGitHubHandlers = registerGitHubHandlers;
const electron_1 = require("electron");
const index_1 = require("./index");
const githubService_1 = require("../services/githubService");
/**
 * GitHub API基础URL
 */
const GITHUB_API_BASE = 'https://api.github.com';
/**
 * 发送HTTP请求
 */
async function fetchGitHubAPI(endpoint, token) {
    const url = `${GITHUB_API_BASE}${endpoint}`;
    const headers = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'GitHub-Monitor/1.0.0',
    };
    if (token) {
        headers['Authorization'] = `token ${token}`;
    }
    try {
        const response = await fetch(url, { headers });
        if (!response.ok) {
            throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    }
    catch (error) {
        console.error(`GitHub API request failed for ${endpoint}:`, error);
        throw error;
    }
}
/**
 * 获取GitHub Token（从配置中读取）
 */
async function getGitHubToken() {
    try {
        // 这里应该从配置文件中读取token
        // 暂时返回undefined，后续会从数据管理服务中获取
        return undefined;
    }
    catch (error) {
        console.error('Failed to get GitHub token:', error);
        return undefined;
    }
}
/**
 * 注册GitHub API IPC处理器
 */
function registerGitHubHandlers() {
    // 检查API速率限制
    electron_1.ipcMain.handle('github:checkRateLimit', async () => {
        try {
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI('/rate_limit', token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:checkRateLimit', error);
        }
    });
    // 获取用户信息
    electron_1.ipcMain.handle('github:getUser', async (_, username) => {
        try {
            if (!username) {
                throw new Error('Username is required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/users/${username}`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getUser', error);
        }
    });
    // 获取仓库信息
    electron_1.ipcMain.handle('github:getRepository', async (_, owner, repo) => {
        try {
            if (!owner || !repo) {
                throw new Error('Owner and repo are required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/repos/${owner}/${repo}`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepository', error);
        }
    });
    // 获取用户的仓库列表
    electron_1.ipcMain.handle('github:getUserRepositories', async (_, username) => {
        try {
            if (!username) {
                throw new Error('Username is required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/users/${username}/repos?sort=updated&per_page=100`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getUserRepositories', error);
        }
    });
    // 获取仓库的提交信息
    electron_1.ipcMain.handle('github:getRepositoryCommits', async (_, owner, repo, options = {}) => {
        try {
            if (!owner || !repo) {
                throw new Error('Owner and repo are required');
            }
            const { since, until, per_page = 30 } = options;
            let endpoint = `/repos/${owner}/${repo}/commits?per_page=${per_page}`;
            if (since) {
                endpoint += `&since=${since}`;
            }
            if (until) {
                endpoint += `&until=${until}`;
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(endpoint, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepositoryCommits', error);
        }
    });
    // 获取仓库的发布信息
    electron_1.ipcMain.handle('github:getRepositoryReleases', async (_, owner, repo) => {
        try {
            if (!owner || !repo) {
                throw new Error('Owner and repo are required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/repos/${owner}/${repo}/releases?per_page=10`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepositoryReleases', error);
        }
    });
    // 解析GitHub URL
    electron_1.ipcMain.handle('github:parseUrl', async (_, url) => {
        try {
            const data = githubService_1.githubService.parseGitHubUrl(url);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:parseUrl', error);
        }
    });
    // 从URL获取仓库信息
    electron_1.ipcMain.handle('github:getRepoInfoFromUrl', async (_, url) => {
        try {
            const token = await getGitHubToken();
            if (token) {
                githubService_1.githubService.updateToken(token);
            }
            const data = await githubService_1.githubService.getRepositoryInfoFromUrl(url);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepoInfoFromUrl', error);
        }
    });
    // 从URL获取用户信息
    electron_1.ipcMain.handle('github:getUserInfoFromUrl', async (_, url) => {
        try {
            const token = await getGitHubToken();
            if (token) {
                githubService_1.githubService.updateToken(token);
            }
            const data = await githubService_1.githubService.getUserInfoFromUrl(url);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getUserInfoFromUrl', error);
        }
    });
    console.log('✓ GitHub IPC handlers registered');
}
