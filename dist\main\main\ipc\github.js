"use strict";
/**
 * GitHub API IPC处理器
 * 负责处理GitHub API相关的IPC通信
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerGitHubHandlers = registerGitHubHandlers;
const electron_1 = require("electron");
const index_1 = require("./index");
const githubService_1 = require("../services/githubService");
const updateDetectionService_1 = require("../services/updateDetectionService");
const dataManager_1 = require("../services/dataManager");
/**
 * GitHub API基础URL
 */
const GITHUB_API_BASE = 'https://api.github.com';
/**
 * 发送HTTP请求
 */
async function fetchGitHubAPI(endpoint, token) {
    const url = `${GITHUB_API_BASE}${endpoint}`;
    const headers = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'GitHub-Monitor/1.0.0',
    };
    if (token) {
        headers['Authorization'] = `token ${token}`;
    }
    try {
        const response = await fetch(url, { headers });
        if (!response.ok) {
            throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    }
    catch (error) {
        console.error(`GitHub API request failed for ${endpoint}:`, error);
        throw error;
    }
}
/**
 * 获取GitHub Token（从配置中读取）
 */
async function getGitHubToken() {
    try {
        // 这里应该从配置文件中读取token
        // 暂时返回undefined，后续会从数据管理服务中获取
        console.log('GitHub token not configured, using unauthenticated requests');
        return undefined;
    }
    catch (error) {
        console.error('Failed to get GitHub token:', error);
        return undefined;
    }
}
/**
 * 注册GitHub API IPC处理器
 */
function registerGitHubHandlers() {
    // 初始化更新检测服务
    const updateDetectionService = new updateDetectionService_1.UpdateDetectionService(githubService_1.githubService, dataManager_1.dataManager);
    // 检查API速率限制
    electron_1.ipcMain.handle('github:checkRateLimit', async () => {
        try {
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI('/rate_limit', token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:checkRateLimit', error);
        }
    });
    // 获取用户信息
    electron_1.ipcMain.handle('github:getUser', async (_, username) => {
        try {
            if (!username) {
                throw new Error('Username is required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/users/${username}`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getUser', error);
        }
    });
    // 获取仓库信息
    electron_1.ipcMain.handle('github:getRepository', async (_, owner, repo) => {
        try {
            if (!owner || !repo) {
                throw new Error('Owner and repo are required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/repos/${owner}/${repo}`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepository', error);
        }
    });
    // 获取用户的仓库列表
    electron_1.ipcMain.handle('github:getUserRepositories', async (_, username) => {
        try {
            if (!username) {
                throw new Error('Username is required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/users/${username}/repos?sort=updated&per_page=100`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getUserRepositories', error);
        }
    });
    // 获取仓库的提交信息
    electron_1.ipcMain.handle('github:getRepositoryCommits', async (_, owner, repo, options = {}) => {
        try {
            if (!owner || !repo) {
                throw new Error('Owner and repo are required');
            }
            const { since, until, per_page = 30 } = options;
            let endpoint = `/repos/${owner}/${repo}/commits?per_page=${per_page}`;
            if (since) {
                endpoint += `&since=${since}`;
            }
            if (until) {
                endpoint += `&until=${until}`;
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(endpoint, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepositoryCommits', error);
        }
    });
    // 获取仓库的发布信息
    electron_1.ipcMain.handle('github:getRepositoryReleases', async (_, owner, repo) => {
        try {
            if (!owner || !repo) {
                throw new Error('Owner and repo are required');
            }
            const token = await getGitHubToken();
            const data = await fetchGitHubAPI(`/repos/${owner}/${repo}/releases?per_page=10`, token);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepositoryReleases', error);
        }
    });
    // 解析GitHub URL
    electron_1.ipcMain.handle('github:parseUrl', async (_, url) => {
        try {
            const data = githubService_1.githubService.parseGitHubUrl(url);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:parseUrl', error);
        }
    });
    // 从URL获取仓库信息
    electron_1.ipcMain.handle('github:getRepoInfoFromUrl', async (_, url) => {
        try {
            const token = await getGitHubToken();
            if (token) {
                githubService_1.githubService.updateToken(token);
            }
            const data = await githubService_1.githubService.getRepositoryInfoFromUrl(url);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getRepoInfoFromUrl', error);
        }
    });
    // 从URL获取用户信息
    electron_1.ipcMain.handle('github:getUserInfoFromUrl', async (_, url) => {
        try {
            const token = await getGitHubToken();
            if (token) {
                githubService_1.githubService.updateToken(token);
            }
            const data = await githubService_1.githubService.getUserInfoFromUrl(url);
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getUserInfoFromUrl', error);
        }
    });
    // 检测项目更新
    electron_1.ipcMain.handle('github:checkProjectUpdate', async (_, projectId) => {
        try {
            const projects = dataManager_1.dataManager.getProjects();
            const project = projects.find(p => p.id === projectId);
            if (!project) {
                throw new Error('Project not found');
            }
            const hasUpdate = await updateDetectionService.checkProjectUpdate(project);
            return (0, index_1.handleIPCSuccess)({ hasUpdate });
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:checkProjectUpdate', error);
        }
    });
    // 检测作者更新
    electron_1.ipcMain.handle('github:checkAuthorUpdate', async (_, authorId) => {
        try {
            const authors = dataManager_1.dataManager.getAuthors();
            const author = authors.find(a => a.id === authorId);
            if (!author) {
                throw new Error('Author not found');
            }
            const hasUpdate = await updateDetectionService.checkAuthorUpdate(author);
            return (0, index_1.handleIPCSuccess)({ hasUpdate });
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:checkAuthorUpdate', error);
        }
    });
    // 获取作者的新项目
    electron_1.ipcMain.handle('github:getAuthorNewProjects', async (_, authorId) => {
        try {
            const newProjects = await updateDetectionService.getAuthorNewProjects(authorId);
            return (0, index_1.handleIPCSuccess)(newProjects);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getAuthorNewProjects', error);
        }
    });
    // 获取作者的更新项目
    electron_1.ipcMain.handle('github:getAuthorUpdatedProjects', async (_, authorId) => {
        try {
            const updatedProjects = await updateDetectionService.getAuthorUpdatedProjects(authorId);
            return (0, index_1.handleIPCSuccess)(updatedProjects);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:getAuthorUpdatedProjects', error);
        }
    });
    // 批量检测所有项目更新
    electron_1.ipcMain.handle('github:checkAllProjectUpdates', async () => {
        try {
            await updateDetectionService.checkAllProjectUpdates();
            return (0, index_1.handleIPCSuccess)({ message: 'All project updates checked' });
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:checkAllProjectUpdates', error);
        }
    });
    // 批量检测所有作者更新
    electron_1.ipcMain.handle('github:checkAllAuthorUpdates', async () => {
        try {
            await updateDetectionService.checkAllAuthorUpdates();
            return (0, index_1.handleIPCSuccess)({ message: 'All author updates checked' });
        }
        catch (error) {
            return (0, index_1.handleIPCError)('github:checkAllAuthorUpdates', error);
        }
    });
    console.log('✓ GitHub IPC handlers registered');
}
