# 开发指南

## 开发规范

### 核心原则
- **代码行数限制**: 每个文件严格控制在1000行以内
- **单一职责**: 每个文件只负责一个明确的功能模块
- **模块化设计**: 功能独立，接口清晰，便于维护和测试
- **类型安全**: 全面使用TypeScript，确保类型安全

### 文件命名规范
- **组件文件**: PascalCase (如: `CategoryItem.tsx`)
- **工具文件**: camelCase (如: `dataManager.ts`)
- **类型文件**: camelCase (如: `category.ts`)
- **常量文件**: camelCase (如: `constants.ts`)

## 开发流程

### 1. 环境准备
```bash
# 安装依赖
npm install

# 开发模式启动
npm run dev

# 构建应用
npm run build

# 打包应用
npm run package
```

### 2. 开发顺序建议
1. **基础架构搭建** (主进程、渲染进程、IPC通信)
2. **数据层实现** (JSON存储、数据管理)
3. **UI框架搭建** (布局、主题、基础组件)
4. **核心功能实现** (分类管理、项目/作者管理)
5. **GitHub API集成** (API封装、更新检查)
6. **高级功能** (搜索、统计、设置)
7. **优化和测试** (性能优化、错误处理)

### 3. 代码质量保证
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 编写单元测试
- 代码审查机制

## 文件实现要点

### 每个文件必须包含
1. 文件头注释 (功能说明、作者、创建时间)
2. 导入语句整理 (按类型分组)
3. 类型定义 (接口、类型别名)
4. 主要功能实现
5. 导出语句

### 代码拆分策略
当文件接近1000行时，考虑以下拆分方式：
1. **功能拆分**: 将不同功能拆分到不同文件
2. **组件拆分**: 将大组件拆分为多个子组件
3. **逻辑提取**: 将复杂逻辑提取为独立的工具函数
4. **常量分离**: 将常量定义移到专门的常量文件
5. **类型分离**: 将类型定义移到专门的类型文件

## 性能优化建议

1. **懒加载**: 对大组件使用React.lazy进行懒加载
2. **虚拟化**: 对长列表使用虚拟滚动
3. **缓存策略**: 合理使用缓存减少重复计算
4. **防抖节流**: 对频繁操作使用防抖和节流
5. **内存管理**: 及时清理不需要的数据和事件监听器

## 技术难点解决方案

### 1. GitHub API限制处理
```typescript
// 速率限制器实现
class RateLimiter {
  private queue: Array<() => Promise<any>> = [];
  private processing = false;
  private lastRequest = 0;
  private minInterval = 1000; // 1秒间隔

  async execute<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const now = Date.now();
          const timeSinceLastRequest = now - this.lastRequest;
          if (timeSinceLastRequest < this.minInterval) {
            await new Promise(resolve => 
              setTimeout(resolve, this.minInterval - timeSinceLastRequest)
            );
          }
          this.lastRequest = Date.now();
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      this.processQueue();
    });
  }
}
```

### 2. 多线程更新检查
```typescript
// Web Worker消息处理
self.onmessage = async (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'CHECK_UPDATES':
      const results = await checkUpdates(payload);
      self.postMessage({ type: 'UPDATE_RESULTS', payload: results });
      break;
    case 'UPDATE_PROGRESS':
      self.postMessage({ type: 'PROGRESS_UPDATE', payload });
      break;
  }
};
```

### 3. 数据持久化
```typescript
// 原子性写入
async function atomicWrite(filePath: string, data: any): Promise<void> {
  const tempPath = `${filePath}.tmp`;
  try {
    await fs.writeFile(tempPath, JSON.stringify(data, null, 2));
    await fs.rename(tempPath, filePath);
  } catch (error) {
    await fs.unlink(tempPath).catch(() => {});
    throw error;
  }
}
```

## 测试策略

### 单元测试
- 使用Jest进行单元测试
- 测试覆盖率要求80%以上
- 重点测试业务逻辑和工具函数

### 集成测试
- 测试IPC通信
- 测试数据持久化
- 测试API集成

### E2E测试
- 使用Playwright进行端到端测试
- 测试主要用户流程
- 测试跨平台兼容性

## 部署和分发

### 构建配置
```javascript
// electron-builder配置
module.exports = {
  appId: 'com.github.monitor',
  productName: 'GitHub Monitor',
  directories: {
    output: 'dist/packaged'
  },
  files: [
    'dist/main/**/*',
    'dist/renderer/**/*',
    'data/**/*',
    'public/**/*'
  ],
  mac: {
    category: 'public.app-category.developer-tools'
  },
  win: {
    target: 'nsis'
  },
  linux: {
    target: 'AppImage'
  }
};
```

### 发布流程
1. 版本号管理
2. 构建和测试
3. 打包和签名
4. 发布到GitHub Releases
5. 自动更新推送
