import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ExclamationOutlinedSvg from "@ant-design/icons-svg/es/asn/ExclamationOutlined";
import AntdIcon from "../components/AntdIcon";
var ExclamationOutlined = function ExclamationOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ExclamationOutlinedSvg
  }));
};

/**![exclamation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ0OCA4MDRhNjQgNjQgMCAxMDEyOCAwIDY0IDY0IDAgMTAtMTI4IDB6bTMyLTE2OGg2NGM0LjQgMCA4LTMuNiA4LThWMTY0YzAtNC40LTMuNi04LTgtOGgtNjRjLTQuNCAwLTggMy42LTggOHY0NjRjMCA0LjQgMy42IDggOCA4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ExclamationOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ExclamationOutlined';
}
export default RefIcon;