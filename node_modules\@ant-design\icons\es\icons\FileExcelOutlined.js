import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FileExcelOutlinedSvg from "@ant-design/icons-svg/es/asn/FileExcelOutlined";
import AntdIcon from "../components/AntdIcon";
var FileExcelOutlined = function FileExcelOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FileExcelOutlinedSvg
  }));
};

/**![file-excel](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC42TDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTc5MC4yIDMyNkg2MDJWMTM3LjhMNzkwLjIgMzI2em0xLjggNTYySDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHpNNTE0LjEgNTgwLjFsLTYxLjgtMTAyLjRjLTIuMi0zLjYtNi4xLTUuOC0xMC4zLTUuOGgtMzguNGMtMi4zIDAtNC41LjYtNi40IDEuOS01LjYgMy41LTcuMyAxMC45LTMuNyAxNi42bDgyLjMgMTMwLjQtODMuNCAxMzIuOGExMi4wNCAxMi4wNCAwIDAwMTAuMiAxOC40aDM0LjVjNC4yIDAgOC0yLjIgMTAuMi01LjdMNTEwIDY2NC44bDYyLjMgMTAxLjRjMi4yIDMuNiA2LjEgNS43IDEwLjIgNS43SDYyMGMyLjMgMCA0LjUtLjcgNi41LTEuOSA1LjYtMy42IDcuMi0xMSAzLjYtMTYuNmwtODQtMTMwLjQgODUuMy0xMzIuNWExMi4wNCAxMi4wNCAwIDAwLTEwLjEtMTguNWgtMzUuN2MtNC4yIDAtOC4xIDIuMi0xMC4zIDUuOGwtNjEuMiAxMDIuM3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileExcelOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileExcelOutlined';
}
export default RefIcon;