#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 项目目录结构
const directories = [
  'src/main/ipc',
  'src/main/services',
  'src/renderer/components/Layout',
  'src/renderer/components/Search',
  'src/renderer/components/Category',
  'src/renderer/components/Author',
  'src/renderer/components/Project',
  'src/renderer/components/Statistics',
  'src/renderer/components/Settings',
  'src/renderer/components/Common',
  'src/renderer/components/Browser',
  'src/renderer/pages',
  'src/renderer/hooks',
  'src/renderer/store/slices',
  'src/renderer/store/middleware',
  'src/renderer/utils/api',
  'src/renderer/utils/data',
  'src/renderer/utils/ui',
  'src/renderer/utils/common',
  'src/shared/types',
  'src/shared/constants',
  'src/shared/utils',
  'src/workers',
  'public/locales',
  'data',
  'scripts',
  'docs'
];

// 创建目录结构
function createDirectories() {
  console.log('Creating directory structure...');
  directories.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✓ Created directory: ${dir}`);
    } else {
      console.log(`- Directory already exists: ${dir}`);
    }
  });
}

// 创建基础文件
function createBaseFiles() {
  console.log('\nCreating base files...');
  
  // package.json
  const packageJson = {
    name: "github-monitor",
    version: "1.0.0",
    description: "GitHub项目更新监控桌面应用",
    main: "dist/main/index.js",
    homepage: "./",
    scripts: {
      "dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"",
      "dev:main": "tsc -p tsconfig.main.json -w",
      "dev:renderer": "vite",
      "dev:electron": "wait-on dist/main/index.js && electron .",
      "build": "npm run build:main && npm run build:renderer",
      "build:main": "tsc -p tsconfig.main.json",
      "build:renderer": "vite build",
      "package": "electron-builder",
      "package:win": "electron-builder --win",
      "package:mac": "electron-builder --mac",
      "package:linux": "electron-builder --linux",
      "lint": "eslint src --ext .ts,.tsx",
      "lint:fix": "eslint src --ext .ts,.tsx --fix",
      "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""
    },
    devDependencies: {
      "@types/node": "^20.10.0",
      "@types/react": "^18.2.45",
      "@types/react-dom": "^18.2.18",
      "@typescript-eslint/eslint-plugin": "^6.15.0",
      "@typescript-eslint/parser": "^6.15.0",
      "@vitejs/plugin-react": "^4.2.1",
      "concurrently": "^8.2.2",
      "electron": "^28.1.0",
      "electron-builder": "^24.9.1",
      "eslint": "^8.56.0",
      "eslint-plugin-react": "^7.33.2",
      "eslint-plugin-react-hooks": "^4.6.0",
      "prettier": "^3.1.1",
      "typescript": "^5.3.3",
      "vite": "^5.0.10",
      "vite-plugin-electron": "^0.28.1",
      "wait-on": "^7.2.0"
    },
    dependencies: {
      "react": "^18.2.0",
      "react-dom": "^18.2.0",
      "@reduxjs/toolkit": "^2.0.1",
      "react-redux": "^9.0.4",
      "antd": "^5.12.8",
      "axios": "^1.6.2",
      "chart.js": "^4.4.1",
      "react-chartjs-2": "^5.2.0",
      "react-i18next": "^13.5.0",
      "i18next": "^23.7.16",
      "tailwindcss": "^3.4.0",
      "autoprefixer": "^10.4.16",
      "postcss": "^8.4.32"
    },
    build: {
      appId: "com.github.monitor",
      productName: "GitHub Monitor",
      directories: {
        output: "dist/packaged"
      },
      files: [
        "dist/main/**/*",
        "dist/renderer/**/*",
        "data/**/*",
        "public/**/*",
        "node_modules/**/*"
      ],
      mac: {
        category: "public.app-category.developer-tools",
        icon: "public/icon.icns"
      },
      win: {
        target: "nsis",
        icon: "public/icon.ico"
      },
      linux: {
        target: "AppImage",
        icon: "public/icon.png"
      }
    }
  };

  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  console.log('✓ Created package.json');

  // TypeScript配置文件
  const tsConfig = {
    compilerOptions: {
      target: "ES2020",
      lib: ["ES2020", "DOM", "DOM.Iterable"],
      allowJs: false,
      skipLibCheck: true,
      esModuleInterop: true,
      allowSyntheticDefaultImports: true,
      strict: true,
      forceConsistentCasingInFileNames: true,
      moduleResolution: "node",
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: false,
      jsx: "react-jsx",
      declaration: true,
      outDir: "dist/renderer",
      baseUrl: ".",
      paths: {
        "@/*": ["src/*"],
        "@/components/*": ["src/renderer/components/*"],
        "@/utils/*": ["src/renderer/utils/*"],
        "@/hooks/*": ["src/renderer/hooks/*"],
        "@/store/*": ["src/renderer/store/*"],
        "@/types/*": ["src/shared/types/*"]
      }
    },
    include: [
      "src/renderer/**/*",
      "src/shared/**/*"
    ],
    exclude: [
      "node_modules",
      "dist",
      "src/main/**/*"
    ]
  };

  fs.writeFileSync('tsconfig.json', JSON.stringify(tsConfig, null, 2));
  console.log('✓ Created tsconfig.json');

  // 主进程TypeScript配置
  const tsConfigMain = {
    extends: "./tsconfig.json",
    compilerOptions: {
      outDir: "dist/main",
      target: "ES2020",
      module: "CommonJS",
      lib: ["ES2020"],
      jsx: undefined
    },
    include: [
      "src/main/**/*",
      "src/shared/**/*"
    ],
    exclude: [
      "node_modules",
      "dist",
      "src/renderer/**/*"
    ]
  };

  fs.writeFileSync('tsconfig.main.json', JSON.stringify(tsConfigMain, null, 2));
  console.log('✓ Created tsconfig.main.json');

  // Vite配置
  const viteConfig = `import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'dist/renderer',
    emptyOutDir: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/renderer/components'),
      '@/utils': path.resolve(__dirname, 'src/renderer/utils'),
      '@/hooks': path.resolve(__dirname, 'src/renderer/hooks'),
      '@/store': path.resolve(__dirname, 'src/renderer/store'),
      '@/types': path.resolve(__dirname, 'src/shared/types'),
    },
  },
  server: {
    port: 3000,
  },
});`;

  fs.writeFileSync('vite.config.ts', viteConfig);
  console.log('✓ Created vite.config.ts');

  // ESLint配置
  const eslintConfig = `module.exports = {
  root: true,
  env: {
    browser: true,
    es2020: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
  ],
  ignorePatterns: ['dist', '.eslintrc.js'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
    '@typescript-eslint/no-unused-vars': 'warn',
    'react/react-in-jsx-scope': 'off',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
};`;

  fs.writeFileSync('.eslintrc.js', eslintConfig);
  console.log('✓ Created .eslintrc.js');

  // Prettier配置
  const prettierConfig = {
    semi: true,
    trailingComma: 'es5',
    singleQuote: true,
    printWidth: 80,
    tabWidth: 2,
    useTabs: false
  };

  fs.writeFileSync('.prettierrc', JSON.stringify(prettierConfig, null, 2));
  console.log('✓ Created .prettierrc');

  // Tailwind配置
  const tailwindConfig = `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/renderer/**/*.{js,ts,jsx,tsx}",
    "./src/renderer/index.html",
  ],
  theme: {
    extend: {
      colors: {
        dark: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
      },
    },
  },
  plugins: [],
  darkMode: 'class',
};`;

  fs.writeFileSync('tailwind.config.js', tailwindConfig);
  console.log('✓ Created tailwind.config.js');

  // PostCSS配置
  const postcssConfig = `module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};`;

  fs.writeFileSync('postcss.config.js', postcssConfig);
  console.log('✓ Created postcss.config.js');

  // 创建基础数据文件
  const defaultConfig = {
    version: "1.0.0",
    theme: "dark",
    language: "zh",
    github: {
      token: "",
      rateLimit: 5000,
      concurrent: 3
    },
    update: {
      interval: 3600000,
      autoCheck: true
    }
  };

  fs.writeFileSync('data/config.json', JSON.stringify(defaultConfig, null, 2));
  console.log('✓ Created data/config.json');

  // 创建空的数据文件
  const emptyDataFiles = [
    'data/categories.json',
    'data/authors.json',
    'data/projects.json',
    'data/cache.json',
    'data/history.json',
    'data/statistics.json'
  ];

  emptyDataFiles.forEach(file => {
    fs.writeFileSync(file, '[]');
    console.log(`✓ Created ${file}`);
  });

  // 创建语言文件
  const zhLang = {
    app: {
      title: "GitHub监控器",
      description: "监控GitHub项目和作者更新"
    },
    menu: {
      categories: "分类",
      statistics: "统计",
      settings: "设置"
    },
    common: {
      add: "添加",
      edit: "编辑",
      delete: "删除",
      save: "保存",
      cancel: "取消",
      search: "搜索"
    }
  };

  const enLang = {
    app: {
      title: "GitHub Monitor",
      description: "Monitor GitHub projects and authors updates"
    },
    menu: {
      categories: "Categories",
      statistics: "Statistics",
      settings: "Settings"
    },
    common: {
      add: "Add",
      edit: "Edit",
      delete: "Delete",
      save: "Save",
      cancel: "Cancel",
      search: "Search"
    }
  };

  fs.writeFileSync('public/locales/zh.json', JSON.stringify(zhLang, null, 2));
  fs.writeFileSync('public/locales/en.json', JSON.stringify(enLang, null, 2));
  console.log('✓ Created language files');
}

// 创建示例文件
function createExampleFiles() {
  console.log('\nCreating example files...');

  // 主进程入口示例
  const mainIndex = `/**
 * Electron主进程入口文件
 * 负责应用程序的生命周期管理和窗口创建
 */

import { app, BrowserWindow } from 'electron';
import * as path from 'path';
import { createWindow } from './window';
import { setupIPC } from './ipc';
import { initServices } from './services';

// 应用程序准备就绪时的处理
app.whenReady().then(async () => {
  // 初始化服务
  await initServices();
  
  // 设置IPC通信
  setupIPC();
  
  // 创建主窗口
  createWindow();
  
  // macOS特殊处理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// 所有窗口关闭时的处理
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 防止多实例运行
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    const windows = BrowserWindow.getAllWindows();
    if (windows.length > 0) {
      const mainWindow = windows[0];
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
`;

  fs.writeFileSync('src/main/index.ts', mainIndex);
  console.log('✓ Created src/main/index.ts');

  // 渲染进程入口示例
  const rendererMain = `/**
 * React应用程序入口文件
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store';
import App from './App';
import './index.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <ConfigProvider
        locale={zhCN}
        theme={{
          algorithm: theme.darkAlgorithm,
        }}
      >
        <App />
      </ConfigProvider>
    </Provider>
  </React.StrictMode>
);
`;

  fs.writeFileSync('src/renderer/main.tsx', rendererMain);
  console.log('✓ Created src/renderer/main.tsx');

  // HTML模板
  const htmlTemplate = `<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/icon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GitHub Monitor</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/renderer/main.tsx"></script>
  </body>
</html>`;

  fs.writeFileSync('src/renderer/index.html', htmlTemplate);
  console.log('✓ Created src/renderer/index.html');

  // CSS样式文件
  const indexCSS = `@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0f172a;
  color: #f8fafc;
}

#root {
  height: 100vh;
  overflow: hidden;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Ant Design暗黑主题覆盖 */
.ant-layout {
  background: #0f172a !important;
}

.ant-layout-sider {
  background: #1e293b !important;
}

.ant-menu-dark {
  background: #1e293b !important;
}
`;

  fs.writeFileSync('src/renderer/index.css', indexCSS);
  console.log('✓ Created src/renderer/index.css');
}

// 主函数
function init() {
  console.log('🚀 Initializing GitHub Monitor project...\n');
  
  try {
    createDirectories();
    createBaseFiles();
    createExampleFiles();
    
    console.log('\n✅ Project structure created successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. npm install                    # 安装依赖');
    console.log('2. npm run dev                    # 启动开发服务器');
    console.log('3. 开始开发，记住每个文件不超过1000行代码');
    console.log('\n📚 Documentation:');
    console.log('- README.md                       # 项目说明');
    console.log('- project-structure.md            # 项目结构');
    console.log('- development-guide.md            # 开发指南');
    
  } catch (error) {
    console.error('❌ Error creating project structure:', error);
    process.exit(1);
  }
}

// 检查是否直接运行此脚本
if (require.main === module) {
  init();
}

module.exports = { init };
