/**
 * GitHub API IPC处理器
 * 负责处理GitHub API相关的IPC通信
 */

import { ipcMain } from 'electron';
import { handleIPCError, handleIPCSuccess } from './index';
import { githubService } from '../services/githubService';
import { UpdateDetectionService } from '../services/updateDetectionService';
import { dataManager } from '../services/dataManager';

/**
 * GitHub API基础URL
 */
const GITHUB_API_BASE = 'https://api.github.com';

/**
 * 发送HTTP请求
 */
async function fetchGitHubAPI(endpoint: string, token?: string): Promise<any> {
  const url = `${GITHUB_API_BASE}${endpoint}`;
  const headers: Record<string, string> = {
    'Accept': 'application/vnd.github.v3+json',
    'User-Agent': 'GitHub-Monitor/1.0.0',
  };

  if (token) {
    headers['Authorization'] = `token ${token}`;
  }

  try {
    const response = await fetch(url, { headers });
    
    if (!response.ok) {
      throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`GitHub API request failed for ${endpoint}:`, error);
    throw error;
  }
}

/**
 * 获取GitHub Token（从配置中读取）
 */
async function getGitHubToken(): Promise<string | undefined> {
  try {
    // 这里应该从配置文件中读取token
    // 暂时返回undefined，后续会从数据管理服务中获取
    console.log('GitHub token not configured, using unauthenticated requests');
    return undefined;
  } catch (error) {
    console.error('Failed to get GitHub token:', error);
    return undefined;
  }
}

/**
 * 注册GitHub API IPC处理器
 */
export function registerGitHubHandlers(): void {
  // 初始化更新检测服务
  const updateDetectionService = new UpdateDetectionService(githubService, dataManager);

  // 检查API速率限制
  ipcMain.handle('github:checkRateLimit', async () => {
    try {
      const token = await getGitHubToken();
      const data = await fetchGitHubAPI('/rate_limit', token);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:checkRateLimit', error);
    }
  });

  // 获取用户信息
  ipcMain.handle('github:getUser', async (_, username: string) => {
    try {
      if (!username) {
        throw new Error('Username is required');
      }

      const token = await getGitHubToken();
      const data = await fetchGitHubAPI(`/users/${username}`, token);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:getUser', error);
    }
  });

  // 获取仓库信息
  ipcMain.handle('github:getRepository', async (_, owner: string, repo: string) => {
    try {
      if (!owner || !repo) {
        throw new Error('Owner and repo are required');
      }

      const token = await getGitHubToken();
      const data = await fetchGitHubAPI(`/repos/${owner}/${repo}`, token);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:getRepository', error);
    }
  });

  // 获取用户的仓库列表
  ipcMain.handle('github:getUserRepositories', async (_, username: string) => {
    try {
      if (!username) {
        throw new Error('Username is required');
      }

      const token = await getGitHubToken();
      const data = await fetchGitHubAPI(`/users/${username}/repos?sort=updated&per_page=100`, token);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:getUserRepositories', error);
    }
  });

  // 获取仓库的提交信息
  ipcMain.handle('github:getRepositoryCommits', async (_, owner: string, repo: string, options: any = {}) => {
    try {
      if (!owner || !repo) {
        throw new Error('Owner and repo are required');
      }

      const { since, until, per_page = 30 } = options;
      let endpoint = `/repos/${owner}/${repo}/commits?per_page=${per_page}`;
      
      if (since) {
        endpoint += `&since=${since}`;
      }
      if (until) {
        endpoint += `&until=${until}`;
      }

      const token = await getGitHubToken();
      const data = await fetchGitHubAPI(endpoint, token);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:getRepositoryCommits', error);
    }
  });

  // 获取仓库的发布信息
  ipcMain.handle('github:getRepositoryReleases', async (_, owner: string, repo: string) => {
    try {
      if (!owner || !repo) {
        throw new Error('Owner and repo are required');
      }

      const token = await getGitHubToken();
      const data = await fetchGitHubAPI(`/repos/${owner}/${repo}/releases?per_page=10`, token);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:getRepositoryReleases', error);
    }
  });

  // 解析GitHub URL
  ipcMain.handle('github:parseUrl', async (_, url: string) => {
    try {
      const data = githubService.parseGitHubUrl(url);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:parseUrl', error);
    }
  });

  // 从URL获取仓库信息
  ipcMain.handle('github:getRepoInfoFromUrl', async (_, url: string) => {
    try {
      const token = await getGitHubToken();
      if (token) {
        githubService.updateToken(token);
      }
      const data = await githubService.getRepositoryInfoFromUrl(url);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:getRepoInfoFromUrl', error);
    }
  });

  // 从URL获取用户信息
  ipcMain.handle('github:getUserInfoFromUrl', async (_, url: string) => {
    try {
      const token = await getGitHubToken();
      if (token) {
        githubService.updateToken(token);
      }
      const data = await githubService.getUserInfoFromUrl(url);
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('github:getUserInfoFromUrl', error);
    }
  });

  // 检测项目更新
  ipcMain.handle('github:checkProjectUpdate', async (_, projectId: string) => {
    try {
      const projects = dataManager.getProjects();
      const project = projects.find((p: any) => p.id === projectId);
      if (!project) {
        throw new Error('Project not found');
      }
      const hasUpdate = await updateDetectionService.checkProjectUpdate(project);
      return handleIPCSuccess({ hasUpdate });
    } catch (error) {
      return handleIPCError('github:checkProjectUpdate', error);
    }
  });

  // 标记项目为已读
  ipcMain.handle('github:markProjectAsRead', async (_, authorId: string, projectId: string) => {
    try {
      updateDetectionService.markProjectAsRead(authorId, projectId);
      return handleIPCSuccess({ success: true });
    } catch (error) {
      return handleIPCError('github:markProjectAsRead', error);
    }
  });

  // 检查作者是否有未读项目
  ipcMain.handle('github:checkAuthorHasUnreadProjects', async (_, authorId: string, readProjectIds: string[] = []) => {
    try {
      const hasUnread = await updateDetectionService.checkAuthorHasUnreadProjects(authorId, readProjectIds);
      return handleIPCSuccess({ hasUnread });
    } catch (error) {
      return handleIPCError('github:checkAuthorHasUnreadProjects', error);
    }
  });

  // 获取作者的所有项目
  ipcMain.handle('github:getAuthorProjects', async (_, authorId: string) => {
    try {
      const allProjects = await updateDetectionService.getAuthorAllProjects(authorId);
      return handleIPCSuccess(allProjects);
    } catch (error) {
      return handleIPCError('github:getAuthorProjects', error);
    }
  });

  // 检测作者更新
  ipcMain.handle('github:checkAuthorUpdate', async (_, authorId: string) => {
    try {
      const authors = dataManager.getAuthors();
      const author = authors.find((a: any) => a.id === authorId);
      if (!author) {
        throw new Error('Author not found');
      }
      const hasUpdate = await updateDetectionService.checkAuthorUpdate(author);
      return handleIPCSuccess({ hasUpdate });
    } catch (error) {
      return handleIPCError('github:checkAuthorUpdate', error);
    }
  });

  // 获取作者的新项目
  ipcMain.handle('github:getAuthorNewProjects', async (_, authorId: string, readProjectIds: string[] = []) => {
    try {
      const newProjects = await updateDetectionService.getAuthorNewProjects(authorId);
      // 过滤掉已读的项目
      const unreadNewProjects = newProjects.filter((project: any) =>
        !readProjectIds.includes(project.id.toString())
      );
      return handleIPCSuccess(unreadNewProjects);
    } catch (error) {
      return handleIPCError('github:getAuthorNewProjects', error);
    }
  });

  // 获取作者的更新项目（返回所有有实际更新的项目，不过滤已读状态）
  ipcMain.handle('github:getAuthorUpdatedProjects', async (_, authorId: string, readProjectIds: string[] = []) => {
    try {
      const updatedProjects = await updateDetectionService.getAuthorUpdatedProjects(authorId);
      // 不过滤已读项目，让前端处理已读状态的重置
      return handleIPCSuccess(updatedProjects);
    } catch (error) {
      return handleIPCError('github:getAuthorUpdatedProjects', error);
    }
  });

  // 批量检测所有项目更新
  ipcMain.handle('github:checkAllProjectUpdates', async () => {
    try {
      await updateDetectionService.checkAllProjectUpdates();
      return handleIPCSuccess({ message: 'All project updates checked' });
    } catch (error) {
      return handleIPCError('github:checkAllProjectUpdates', error);
    }
  });

  // 批量检测所有作者更新
  ipcMain.handle('github:checkAllAuthorUpdates', async () => {
    try {
      await updateDetectionService.checkAllAuthorUpdates();
      return handleIPCSuccess({ message: 'All author updates checked' });
    } catch (error) {
      return handleIPCError('github:checkAllAuthorUpdates', error);
    }
  });

  console.log('✓ GitHub IPC handlers registered');
}
