import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloseOutlinedSvg from "@ant-design/icons-svg/es/asn/CloseOutlined";
import AntdIcon from "../components/AntdIcon";
var CloseOutlined = function CloseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloseOutlinedSvg
  }));
};

/**![close](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloseOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloseOutlined';
}
export default RefIcon;