import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ExpandAltOutlinedSvg from "@ant-design/icons-svg/es/asn/ExpandAltOutlined";
import AntdIcon from "../components/AntdIcon";
var ExpandAltOutlined = function ExpandAltOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ExpandAltOutlinedSvg
  }));
};

/**![expand-alt](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NSAxNjAuMWwtMTg5LjIgMjMuNWMtNi42LjgtOS4zIDguOC00LjcgMTMuNWw1NC43IDU0LjctMTUzLjUgMTUzLjVhOC4wMyA4LjAzIDAgMDAwIDExLjNsNDUuMSA0NS4xYzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsMTUzLjYtMTUzLjYgNTQuNyA1NC43YTcuOTQgNy45NCAwIDAwMTMuNS00LjdMODYzLjkgMTY5YTcuOSA3LjkgMCAwMC04LjktOC45ek00MTYuNiA1NjIuM2E4LjAzIDguMDMgMCAwMC0xMS4zIDBMMjUxLjggNzE1LjlsLTU0LjctNTQuN2E3Ljk0IDcuOTQgMCAwMC0xMy41IDQuN0wxNjAuMSA4NTVjLS42IDUuMiAzLjcgOS41IDguOSA4LjlsMTg5LjItMjMuNWM2LjYtLjggOS4zLTguOCA0LjctMTMuNWwtNTQuNy01NC43IDE1My42LTE1My42YzMuMS0zLjEgMy4xLTguMiAwLTExLjNsLTQ1LjItNDV6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ExpandAltOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ExpandAltOutlined';
}
export default RefIcon;