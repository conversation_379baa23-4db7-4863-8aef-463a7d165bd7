{"name": "@octokit/openapi-types", "description": "Generated TypeScript definitions based on GitHub's OpenAPI spec for api.github.com", "repository": {"type": "git", "url": "https://github.com/octokit/openapi-types.ts.git", "directory": "packages/openapi-types"}, "publishConfig": {"access": "public", "provenance": true}, "version": "25.1.0", "main": "", "types": "types.d.ts", "author": "<PERSON> (https://twitter.com/gr2m)", "license": "MIT", "octokit": {"openapi-version": "19.1.0"}}