/**
 * 分类管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,

  message,
  Popconfirm,
  Tag,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import {
  selectCategories,
  selectCategoriesLoading,
  loadCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  saveCategories,
} from '@/renderer/store/slices/categorySlice';
import { Category, CreateCategoryRequest, UpdateCategoryRequest, CategoryType } from '@/shared/types';

const { Title } = Typography;
const { TextArea } = Input;

const Categories: React.FC = () => {
  const dispatch = useAppDispatch();
  const categories = useAppSelector(selectCategories);
  const loading = useAppSelector(selectCategoriesLoading);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [selectedType, setSelectedType] = useState<CategoryType>(CategoryType.PROJECT);
  const [form] = Form.useForm();

  useEffect(() => {
    dispatch(loadCategories());
  }, [dispatch]);

  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    form.setFieldsValue({
      name: category.name,
      description: category.description,
      color: category.color,
      icon: category.icon,
      weight: category.weight,
      parentId: category.parentId,
      type: category.type,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (categoryId: string) => {
    try {
      await dispatch(deleteCategory(categoryId));
      await dispatch(saveCategories(categories.filter(c => c.id !== categoryId)));
      message.success('分类删除成功');
    } catch (error) {
      message.error('分类删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingCategory) {
        // 更新分类
        const updateData: UpdateCategoryRequest = {
          id: editingCategory.id,
          ...values,
        };
        await dispatch(updateCategory(updateData));
      } else {
        // 创建分类
        const createData: CreateCategoryRequest = values;
        await dispatch(createCategory(createData));
      }
      
      await dispatch(saveCategories(categories));
      message.success(editingCategory ? '分类更新成功' : '分类创建成功');
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error(editingCategory ? '分类更新失败' : '分类创建失败');
    }
  };

  // 根据选择的类型过滤分类
  const filteredCategories = categories.filter(category => category.type === selectedType);

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Category) => (
        <Space>
          <FolderOutlined style={{ color: record.color }} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: CategoryType) => (
        <Tag color={type === CategoryType.PROJECT ? 'blue' : 'green'}>
          {type === CategoryType.PROJECT ? '项目分类' : '作者分类'}
        </Tag>
      ),
    },
    {
      title: '颜色',
      dataIndex: 'color',
      key: 'color',
      render: (color: string) => (
        <div
          style={{
            width: 20,
            height: 20,
            backgroundColor: color,
            borderRadius: 4,
            border: '1px solid #d9d9d9',
          }}
        />
      ),
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      sorter: (a: Category, b: Category) => a.weight - b.weight,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Category) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100vh', overflow: 'auto' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Title level={3} style={{ margin: 0 }}>分类管理</Title>
            <Select
              value={selectedType}
              onChange={setSelectedType}
              style={{ width: 120 }}
            >
              <Select.Option value={CategoryType.PROJECT}>项目分类</Select.Option>
              <Select.Option value={CategoryType.AUTHOR}>作者分类</Select.Option>
            </Select>
          </div>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加分类
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={filteredCategories}
          rowKey="id"
          loading={loading}
          scroll={{ y: 'calc(100vh - 300px)' }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            color: '#1890ff',
            icon: 'folder',
            weight: 0,
            type: selectedType,
            isActive: true,
          }}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="请输入分类描述" />
          </Form.Item>

          <Form.Item
            name="type"
            label="分类类型"
            rules={[{ required: true, message: '请选择分类类型' }]}
          >
            <Select placeholder="选择分类类型">
              <Select.Option value={CategoryType.PROJECT}>项目分类</Select.Option>
              <Select.Option value={CategoryType.AUTHOR}>作者分类</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="color" label="颜色">
            <Input type="color" />
          </Form.Item>

          <Form.Item name="weight" label="权重">
            <Input type="number" placeholder="权重（数字越大越靠前）" />
          </Form.Item>

          <Form.Item name="parentId" label="父分类">
            <Select placeholder="选择父分类（可选）" allowClear>
              {categories
                .filter(c => c.id !== editingCategory?.id && c.type === form.getFieldValue('type'))
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    {category.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCategory ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Categories;
