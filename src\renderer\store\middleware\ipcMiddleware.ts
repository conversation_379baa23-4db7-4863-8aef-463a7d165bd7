/**
 * IPC中间件
 * 处理与主进程的通信
 */

import { Middleware } from '@reduxjs/toolkit';

// IPC中间件
export const ipcMiddleware: Middleware = (store) => (next) => (action: any) => {
  // 处理需要与主进程通信的action
  if (action.type && typeof action.type === 'string' && (
      action.type.startsWith('category/save') ||
      action.type.startsWith('author/save') ||
      action.type.startsWith('project/save') ||
      action.type.startsWith('settings/save'))) {

    // 这里可以添加与主进程通信的逻辑
    console.log('IPC action:', action.type);
  }

  return next(action);
};
