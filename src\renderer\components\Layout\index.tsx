/**
 * 主布局组件
 * 负责应用的整体布局结构
 */

import React from 'react';
import { Layout as AntLayout } from 'antd';
import Sidebar from './Sidebar';
import Content from './Content';
import { useAppSelector } from '@/renderer/store';
import { selectCurrentView } from '@/renderer/store/slices/appSlice';

const { Sider, Content: AntContent } = AntLayout;

interface LayoutProps {
  children?: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const currentView = useAppSelector(selectCurrentView);

  console.log('Layout rendering with currentView:', currentView);

  return (
    <AntLayout className="app-layout" style={{ height: '100vh', overflow: 'hidden' }}>
      <Sider
        width={240}
        theme="dark"
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 100,
        }}
      >
        <Sidebar />
      </Sider>

      <AntLayout style={{ marginLeft: 240, height: '100vh' }}>
        <AntContent
          style={{
            margin: 0,
            height: '100vh',
            background: '#f0f2f5',
            overflow: 'auto',
          }}
        >
          <Content currentView={currentView}>
            {children}
          </Content>
        </AntContent>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
