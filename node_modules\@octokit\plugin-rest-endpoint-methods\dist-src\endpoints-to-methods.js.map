{"version": 3, "sources": ["../../src/endpoints-to-methods.ts"], "sourcesContent": ["import type { Octokit } from \"@octokit/core\";\nimport type { EndpointOptions, RequestParameters, Route } from \"@octokit/types\";\nimport ENDPOINTS from \"./generated/endpoints.js\";\nimport type { RestEndpointMethods } from \"./generated/method-types.js\";\nimport type { EndpointDecorations } from \"./types.js\";\n\n// The following code was refactored in: https://github.com/octokit/plugin-rest-endpoint-methods.js/pull/622\n// to optimise the runtime performance of Octokit initialization.\n//\n// This optimization involves two key changes:\n// 1. Pre-Computation: The endpoint methods are pre-computed once at module load\n//    time instead of each invocation of `endpointsToMethods()`.\n// 2. Lazy initialization and caching: We use a Proxy for each scope to only\n//    initialize methods that are actually called. This reduces runtime overhead\n//    as the initialization involves deep merging of objects. The initialized\n//    methods are then cached for future use.\n\nconst endpointMethodsMap = new Map();\nfor (const [scope, endpoints] of Object.entries(ENDPOINTS)) {\n  for (const [methodName, endpoint] of Object.entries(endpoints)) {\n    const [route, defaults, decorations] = endpoint;\n    const [method, url] = route.split(/ /);\n    const endpointDefaults = Object.assign(\n      {\n        method,\n        url,\n      },\n      defaults,\n    );\n\n    if (!endpointMethodsMap.has(scope)) {\n      endpointMethodsMap.set(scope, new Map());\n    }\n\n    endpointMethodsMap.get(scope).set(methodName, {\n      scope,\n      methodName,\n      endpointDefaults,\n      decorations,\n    });\n  }\n}\n\ntype ProxyTarget = {\n  octokit: Octokit;\n  scope: string;\n  cache: Record<string, (...args: any[]) => any>;\n};\n\nconst handler = {\n  has({ scope }: ProxyTarget, methodName: string) {\n    return endpointMethodsMap.get(scope).has(methodName);\n  },\n  getOwnPropertyDescriptor(target: ProxyTarget, methodName: string) {\n    return {\n      value: this.get(target, methodName), // ensures method is in the cache\n      configurable: true,\n      writable: true,\n      enumerable: true,\n    };\n  },\n  defineProperty(\n    target: ProxyTarget,\n    methodName: string,\n    descriptor: PropertyDescriptor,\n  ) {\n    Object.defineProperty(target.cache, methodName, descriptor);\n    return true;\n  },\n  deleteProperty(target: ProxyTarget, methodName: string) {\n    delete target.cache[methodName];\n    return true;\n  },\n  ownKeys({ scope }: ProxyTarget) {\n    return [...endpointMethodsMap.get(scope).keys()];\n  },\n  set(target: ProxyTarget, methodName: string, value: any) {\n    return (target.cache[methodName] = value);\n  },\n  get({ octokit, scope, cache }: ProxyTarget, methodName: string) {\n    if (cache[methodName]) {\n      return cache[methodName];\n    }\n\n    const method = endpointMethodsMap.get(scope).get(methodName);\n    if (!method) {\n      return undefined;\n    }\n\n    const { endpointDefaults, decorations } = method;\n\n    if (decorations) {\n      cache[methodName] = decorate(\n        octokit,\n        scope,\n        methodName,\n        endpointDefaults,\n        decorations,\n      );\n    } else {\n      cache[methodName] = octokit.request.defaults(endpointDefaults);\n    }\n\n    return cache[methodName];\n  },\n};\n\nexport function endpointsToMethods(octokit: Octokit): RestEndpointMethods {\n  const newMethods = {} as { [key: string]: object };\n\n  for (const scope of endpointMethodsMap.keys()) {\n    newMethods[scope] = new Proxy({ octokit, scope, cache: {} }, handler);\n  }\n\n  return newMethods as RestEndpointMethods;\n}\n\nfunction decorate(\n  octokit: Octokit,\n  scope: string,\n  methodName: string,\n  defaults: EndpointOptions,\n  decorations: EndpointDecorations,\n) {\n  const requestWithDefaults = octokit.request.defaults(defaults);\n\n  /* istanbul ignore next */\n  function withDecorations(\n    ...args: [Route, RequestParameters?] | [EndpointOptions]\n  ) {\n    // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n    let options = requestWithDefaults.endpoint.merge(...args);\n\n    // There are currently no other decorations than `.mapToData`\n    if (decorations.mapToData) {\n      options = Object.assign({}, options, {\n        data: options[decorations.mapToData],\n        [decorations.mapToData]: undefined,\n      });\n      return requestWithDefaults(options);\n    }\n\n    if (decorations.renamed) {\n      const [newScope, newMethodName] = decorations.renamed;\n      octokit.log.warn(\n        `octokit.${scope}.${methodName}() has been renamed to octokit.${newScope}.${newMethodName}()`,\n      );\n    }\n    if (decorations.deprecated) {\n      octokit.log.warn(decorations.deprecated);\n    }\n\n    if (decorations.renamedParameters) {\n      // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n      const options = requestWithDefaults.endpoint.merge(...args);\n\n      for (const [name, alias] of Object.entries(\n        decorations.renamedParameters,\n      )) {\n        if (name in options) {\n          octokit.log.warn(\n            `\"${name}\" parameter is deprecated for \"octokit.${scope}.${methodName}()\". Use \"${alias}\" instead`,\n          );\n          if (!(alias in options)) {\n            options[alias] = options[name];\n          }\n          delete options[name];\n        }\n      }\n      return requestWithDefaults(options);\n    }\n\n    // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n    return requestWithDefaults(...args);\n  }\n  return Object.assign(withDecorations, requestWithDefaults);\n}\n"], "mappings": "AAEA,OAAO,eAAe;AAetB,MAAM,qBAAqB,oBAAI,IAAI;AACnC,WAAW,CAAC,OAAO,SAAS,KAAK,OAAO,QAAQ,SAAS,GAAG;AAC1D,aAAW,CAAC,YAAY,QAAQ,KAAK,OAAO,QAAQ,SAAS,GAAG;AAC9D,UAAM,CAAC,OAAO,UAAU,WAAW,IAAI;AACvC,UAAM,CAAC,QAAQ,GAAG,IAAI,MAAM,MAAM,GAAG;AACrC,UAAM,mBAAmB,OAAO;AAAA,MAC9B;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,mBAAmB,IAAI,KAAK,GAAG;AAClC,yBAAmB,IAAI,OAAO,oBAAI,IAAI,CAAC;AAAA,IACzC;AAEA,uBAAmB,IAAI,KAAK,EAAE,IAAI,YAAY;AAAA,MAC5C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAQA,MAAM,UAAU;AAAA,EACd,IAAI,EAAE,MAAM,GAAgB,YAAoB;AAC9C,WAAO,mBAAmB,IAAI,KAAK,EAAE,IAAI,UAAU;AAAA,EACrD;AAAA,EACA,yBAAyB,QAAqB,YAAoB;AAChE,WAAO;AAAA,MACL,OAAO,KAAK,IAAI,QAAQ,UAAU;AAAA;AAAA,MAClC,cAAc;AAAA,MACd,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,eACE,QACA,YACA,YACA;AACA,WAAO,eAAe,OAAO,OAAO,YAAY,UAAU;AAC1D,WAAO;AAAA,EACT;AAAA,EACA,eAAe,QAAqB,YAAoB;AACtD,WAAO,OAAO,MAAM,UAAU;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,EAAE,MAAM,GAAgB;AAC9B,WAAO,CAAC,GAAG,mBAAmB,IAAI,KAAK,EAAE,KAAK,CAAC;AAAA,EACjD;AAAA,EACA,IAAI,QAAqB,YAAoB,OAAY;AACvD,WAAQ,OAAO,MAAM,UAAU,IAAI;AAAA,EACrC;AAAA,EACA,IAAI,EAAE,SAAS,OAAO,MAAM,GAAgB,YAAoB;AAC9D,QAAI,MAAM,UAAU,GAAG;AACrB,aAAO,MAAM,UAAU;AAAA,IACzB;AAEA,UAAM,SAAS,mBAAmB,IAAI,KAAK,EAAE,IAAI,UAAU;AAC3D,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,kBAAkB,YAAY,IAAI;AAE1C,QAAI,aAAa;AACf,YAAM,UAAU,IAAI;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,UAAU,IAAI,QAAQ,QAAQ,SAAS,gBAAgB;AAAA,IAC/D;AAEA,WAAO,MAAM,UAAU;AAAA,EACzB;AACF;AAEO,SAAS,mBAAmB,SAAuC;AACxE,QAAM,aAAa,CAAC;AAEpB,aAAW,SAAS,mBAAmB,KAAK,GAAG;AAC7C,eAAW,KAAK,IAAI,IAAI,MAAM,EAAE,SAAS,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO;AAAA,EACtE;AAEA,SAAO;AACT;AAEA,SAAS,SACP,SACA,OACA,YACA,UACA,aACA;AACA,QAAM,sBAAsB,QAAQ,QAAQ,SAAS,QAAQ;AAG7D,WAAS,mBACJ,MACH;AAEA,QAAI,UAAU,oBAAoB,SAAS,MAAM,GAAG,IAAI;AAGxD,QAAI,YAAY,WAAW;AACzB,gBAAU,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,QACnC,MAAM,QAAQ,YAAY,SAAS;AAAA,QACnC,CAAC,YAAY,SAAS,GAAG;AAAA,MAC3B,CAAC;AACD,aAAO,oBAAoB,OAAO;AAAA,IACpC;AAEA,QAAI,YAAY,SAAS;AACvB,YAAM,CAAC,UAAU,aAAa,IAAI,YAAY;AAC9C,cAAQ,IAAI;AAAA,QACV,WAAW,KAAK,IAAI,UAAU,kCAAkC,QAAQ,IAAI,aAAa;AAAA,MAC3F;AAAA,IACF;AACA,QAAI,YAAY,YAAY;AAC1B,cAAQ,IAAI,KAAK,YAAY,UAAU;AAAA,IACzC;AAEA,QAAI,YAAY,mBAAmB;AAEjC,YAAMA,WAAU,oBAAoB,SAAS,MAAM,GAAG,IAAI;AAE1D,iBAAW,CAAC,MAAM,KAAK,KAAK,OAAO;AAAA,QACjC,YAAY;AAAA,MACd,GAAG;AACD,YAAI,QAAQA,UAAS;AACnB,kBAAQ,IAAI;AAAA,YACV,IAAI,IAAI,0CAA0C,KAAK,IAAI,UAAU,aAAa,KAAK;AAAA,UACzF;AACA,cAAI,EAAE,SAASA,WAAU;AACvB,YAAAA,SAAQ,KAAK,IAAIA,SAAQ,IAAI;AAAA,UAC/B;AACA,iBAAOA,SAAQ,IAAI;AAAA,QACrB;AAAA,MACF;AACA,aAAO,oBAAoBA,QAAO;AAAA,IACpC;AAGA,WAAO,oBAAoB,GAAG,IAAI;AAAA,EACpC;AACA,SAAO,OAAO,OAAO,iBAAiB,mBAAmB;AAC3D;", "names": ["options"]}