/**
 * 主题Hook
 * 管理应用主题状态和切换
 */

import { useEffect, useMemo } from 'react';
import { theme } from 'antd';
import { useAppSelector } from '@/renderer/store';
import { selectSettings } from '@/renderer/store/slices/settingsSlice';

export const useTheme = () => {
  const settings = useAppSelector(selectSettings);
  const isDarkMode = settings?.ui?.theme?.mode === 'dark' || true; // 默认为深色模式

  // 获取Ant Design主题配置
  const antdTheme = useMemo(() => {
    const baseTheme = {
      token: {
        colorPrimary: settings?.ui?.theme?.primaryColor || '#1890ff',
        borderRadius: settings?.ui?.theme?.borderRadius || 6,
        fontSize: 14,
        fontFamily: settings?.ui?.typography?.fontFamily || 'system-ui',
      },
      components: {
        Layout: {
          siderBg: isDarkMode ? '#001529' : '#ffffff',
          bodyBg: isDarkMode ? '#141414' : '#f0f2f5',
        },
        Menu: {
          darkItemBg: '#001529',
          darkItemSelectedBg: '#1890ff',
          darkItemHoverBg: '#1890ff20',
        },
      },
    };

    return {
      ...baseTheme,
      algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
    };
  }, [settings, isDarkMode]);

  // 应用CSS变量
  useEffect(() => {
    const root = document.documentElement;
    
    if (isDarkMode) {
      root.style.setProperty('--bg-color', '#141414');
      root.style.setProperty('--text-color', '#ffffff');
      root.style.setProperty('--border-color', '#303030');
      root.style.setProperty('--card-bg', '#1f1f1f');
    } else {
      root.style.setProperty('--bg-color', '#ffffff');
      root.style.setProperty('--text-color', '#000000');
      root.style.setProperty('--border-color', '#d9d9d9');
      root.style.setProperty('--card-bg', '#ffffff');
    }

    root.style.setProperty('--primary-color', settings?.ui?.theme?.primaryColor || '#1890ff');
    root.style.setProperty('--accent-color', settings?.ui?.theme?.accentColor || '#52c41a');
  }, [settings, isDarkMode]);

  return {
    isDarkMode,
    antdTheme,
    primaryColor: settings?.ui?.theme?.primaryColor || '#1890ff',
    accentColor: settings?.ui?.theme?.accentColor || '#52c41a',
  };
};
