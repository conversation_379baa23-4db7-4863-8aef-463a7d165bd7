"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _UpSquareTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/UpSquareTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var UpSquareTwoTone = function UpSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _UpSquareTwoTone.default
  }));
};

/**![up-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMTQzLjUtMjI4LjdsMTc4LTI0NmMzLjItNC40IDkuNy00LjQgMTIuOSAwbDE3OCAyNDZjMy45IDUuMy4xIDEyLjctNi40IDEyLjdoLTQ2LjljLTEwLjIgMC0xOS45LTQuOS0yNS45LTEzLjJMNTEyIDQ2NS40IDQwNi44IDYxMC44Yy02IDguMy0xNS42IDEzLjItMjUuOSAxMy4ySDMzNGMtNi41IDAtMTAuMy03LjQtNi41LTEyLjd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik0zMzQgNjI0aDQ2LjljMTAuMyAwIDE5LjktNC45IDI1LjktMTMuMkw1MTIgNDY1LjRsMTA1LjIgMTQ1LjRjNiA4LjMgMTUuNyAxMy4yIDI1LjkgMTMuMkg2OTBjNi41IDAgMTAuMy03LjQgNi40LTEyLjdsLTE3OC0yNDZhNy45NSA3Ljk1IDAgMDAtMTIuOSAwbC0xNzggMjQ2Yy0zLjggNS4zIDAgMTIuNyA2LjUgMTIuN3oiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(UpSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpSquareTwoTone';
}
var _default = exports.default = RefIcon;