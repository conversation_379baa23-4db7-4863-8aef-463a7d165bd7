/**
 * 应用根组件 - 简化测试版本
 * 负责应用的整体布局和路由管理
 */

import React, { useState } from 'react';

const App: React.FC = () => {
  const [count, setCount] = useState(0);

  console.log('App rendering... count:', count);

  const handleClick = () => {
    console.log('Button clicked! Current count:', count);
    setCount(count + 1);
  };

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#1f1f1f',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ marginBottom: '20px' }}>GitHub Monitor</h1>
      <p style={{ marginBottom: '20px' }}>应用正在运行！</p>
      <p style={{ marginBottom: '20px' }}>点击次数: {count}</p>

      <button
        onClick={handleClick}
        style={{
          padding: '10px 20px',
          backgroundColor: '#1890ff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '16px'
        }}
      >
        测试按钮 (点击 {count} 次)
      </button>
    </div>
  );
};

export default App;
