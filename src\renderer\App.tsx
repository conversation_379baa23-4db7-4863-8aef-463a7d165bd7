/**
 * 应用根组件
 * 负责应用的整体布局和路由管理
 */

import React from 'react';
import { Provider } from 'react-redux';
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import store from './store';
import Layout from './components/Layout';
import { ErrorBoundary, Notification } from './components/Common';

const AppContent: React.FC = () => {
  console.log('AppContent rendering...');

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <ErrorBoundary>
        <Layout />
        <Notification />
      </ErrorBoundary>
    </ConfigProvider>
  );
};

const App: React.FC = () => {
  console.log('App rendering...');

  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
};

export default App;
