/**
 * 应用根组件 - 最简单的测试版本
 */

import React, { useState } from 'react';

const App: React.FC = () => {
  const [count, setCount] = useState(0);
  const [currentView, setCurrentView] = useState('home');

  console.log('App rendering... count:', count, 'currentView:', currentView);

  const handleClick = () => {
    console.log('Button clicked! Current count:', count);
    setCount(count + 1);
  };

  const handleMenuClick = (view: string) => {
    console.log('Menu clicked:', view);
    setCurrentView(view);
  };

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      backgroundColor: '#1f1f1f',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* 左侧菜单 */}
      <div style={{
        width: '240px',
        backgroundColor: '#001529',
        padding: '20px'
      }}>
        <h2 style={{ color: 'white', textAlign: 'center', marginBottom: '20px' }}>
          GitHub Monitor
        </h2>
        <div style={{ marginBottom: '10px', fontSize: '12px', color: '#666' }}>
          当前视图: {currentView}
        </div>
        <div style={{ marginBottom: '10px', fontSize: '12px', color: '#666' }}>
          点击次数: {count}
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
          <button
            onClick={() => handleMenuClick('home')}
            style={{
              padding: '10px',
              backgroundColor: currentView === 'home' ? '#1890ff' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            🏠 首页
          </button>
          <button
            onClick={() => handleMenuClick('search')}
            style={{
              padding: '10px',
              backgroundColor: currentView === 'search' ? '#1890ff' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            🔍 搜索
          </button>
          <button
            onClick={() => handleMenuClick('categories')}
            style={{
              padding: '10px',
              backgroundColor: currentView === 'categories' ? '#1890ff' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            📁 分类管理
          </button>
          <button
            onClick={() => handleMenuClick('authors')}
            style={{
              padding: '10px',
              backgroundColor: currentView === 'authors' ? '#1890ff' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            👥 作者管理
          </button>
          <button
            onClick={() => handleMenuClick('projects')}
            style={{
              padding: '10px',
              backgroundColor: currentView === 'projects' ? '#1890ff' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            📊 项目管理
          </button>
          <button
            onClick={() => handleMenuClick('statistics')}
            style={{
              padding: '10px',
              backgroundColor: currentView === 'statistics' ? '#1890ff' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            📈 统计分析
          </button>
          <button
            onClick={() => handleMenuClick('settings')}
            style={{
              padding: '10px',
              backgroundColor: currentView === 'settings' ? '#1890ff' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            ⚙️ 设置
          </button>
        </div>
      </div>

      {/* 右侧内容 */}
      <div style={{
        flex: 1,
        padding: '20px',
        backgroundColor: '#f0f2f5',
        color: '#333'
      }}>
        <div style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          background: '#1890ff',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '14px',
          zIndex: 1000
        }}>
          当前视图: {currentView} | 点击: {count}
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <h1>当前页面: {currentView}</h1>
          <p>这是 {currentView} 页面的内容</p>
          <p>点击次数: {count}</p>

          <button
            onClick={handleClick}
            style={{
              padding: '10px 20px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px',
              marginRight: '10px'
            }}
          >
            测试按钮 (点击 {count} 次)
          </button>

          <button
            onClick={() => {
              console.log('Alert button clicked!');
              alert('这是一个测试弹窗！');
            }}
            style={{
              padding: '10px 20px',
              backgroundColor: '#52c41a',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            弹窗测试
          </button>
        </div>
      </div>
    </div>
  );
};

export default App;
