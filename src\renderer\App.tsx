/**
 * 应用根组件
 * 负责应用的整体布局和路由管理
 */

import React from 'react';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import store from './store';
import Layout from './components/Layout';
import Home from './pages/Home';
import { ErrorBoundary, Notification } from './components/Common';
import { useTheme } from './hooks/useTheme';

const AppContent: React.FC = () => {
  const { antdTheme } = useTheme();

  return (
    <ConfigProvider locale={zhCN} theme={antdTheme}>
      <ErrorBoundary>
        <Layout>
          <Home />
        </Layout>
        <Notification />
      </ErrorBoundary>
    </ConfigProvider>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
};

export default App;
