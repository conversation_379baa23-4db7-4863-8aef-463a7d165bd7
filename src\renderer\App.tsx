/**
 * 应用根组件
 * 负责应用的整体布局和路由管理
 */

import React, { useState } from 'react';
import { Provider } from 'react-redux';
import { ConfigProvider, theme, Layout as AntLayout, <PERSON>u, But<PERSON>, Card, Typography } from 'antd';
import {
  HomeOutlined,
  FolderOutlined,
  UserOutlined,
  ProjectOutlined,
  BarChartOutlined,
  SettingOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import zhCN from 'antd/locale/zh_CN';
import store from './store';

const { Sider, Content } = AntLayout;
const { Title } = Typography;

const SimpleApp: React.FC = () => {
  const [currentView, setCurrentView] = useState('home');

  console.log('SimpleApp rendering with currentView:', currentView);

  const menuItems = [
    { key: 'home', icon: <HomeOutlined />, label: '首页' },
    { key: 'search', icon: <SearchOutlined />, label: '搜索' },
    { key: 'categories', icon: <FolderOutlined />, label: '分类管理' },
    { key: 'authors', icon: <UserOutlined />, label: '作者管理' },
    { key: 'projects', icon: <ProjectOutlined />, label: '项目管理' },
    { key: 'statistics', icon: <BarChartOutlined />, label: '统计分析' },
    { key: 'settings', icon: <SettingOutlined />, label: '设置' },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    console.log('Menu clicked:', key);
    setCurrentView(key);
  };

  const renderContent = () => {
    console.log('Rendering content for:', currentView);
    return (
      <div style={{ padding: '24px' }}>
        <Card>
          <Title level={2}>当前页面: {currentView}</Title>
          <p>这是 {currentView} 页面的内容</p>
          <Button onClick={() => console.log('Button clicked!')}>
            测试按钮
          </Button>
        </Card>
      </div>
    );
  };

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <AntLayout style={{ height: '100vh' }}>
        <Sider width={240} theme="dark">
          <div style={{ padding: '16px', textAlign: 'center' }}>
            <Title level={4} style={{ color: 'white', margin: 0 }}>
              GitHub Monitor
            </Title>
            <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
              当前视图: {currentView}
            </div>
          </div>

          <Menu
            theme="dark"
            mode="inline"
            selectedKeys={[currentView]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ border: 'none' }}
          />
        </Sider>

        <AntLayout style={{ marginLeft: 0 }}>
          <Content style={{ margin: 0, minHeight: '100vh', background: '#f0f2f5' }}>
            <div style={{
              position: 'fixed',
              top: '10px',
              right: '10px',
              background: '#1890ff',
              color: 'white',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              zIndex: 1000
            }}>
              当前视图: {currentView}
            </div>
            {renderContent()}
          </Content>
        </AntLayout>
      </AntLayout>
    </ConfigProvider>
  );
};

const App: React.FC = () => {
  console.log('App rendering...');

  return (
    <Provider store={store}>
      <SimpleApp />
    </Provider>
  );
};

export default App;
