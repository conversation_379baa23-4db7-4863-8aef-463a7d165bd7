/**
 * 首页组件
 */

import React from 'react';
import { Card, Typography, Space, Button, Row, Col, Statistic } from 'antd';
import {
  HomeOutlined,
  FolderOutlined,
  UserOutlined,
  ProjectOutlined,
  StarOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useAppDispatch } from '@/renderer/store';
import { setCurrentView } from '@/renderer/store/slices/appSlice';

const { Title, Paragraph } = Typography;

const Home: React.FC = () => {
  const dispatch = useAppDispatch();

  const handleNavigate = (view: string) => {
    dispatch(setCurrentView(view));
  };

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 欢迎区域 */}
        <Card>
          <div style={{ textAlign: 'center' }}>
            <HomeOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
            <Title level={2}>欢迎使用 GitHub Monitor</Title>
            <Paragraph>
              GitHub项目更新监控桌面应用，帮助您跟踪关注的项目和作者的最新动态。
            </Paragraph>
          </div>
        </Card>

        {/* 统计概览 */}
        <Card title="数据概览">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="分类数量"
                value={0}
                prefix={<FolderOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="作者数量"
                value={0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="项目数量"
                value={0}
                prefix={<ProjectOutlined />}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="总星标数"
                value={0}
                prefix={<StarOutlined />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 快速操作 */}
        <Card title="快速开始">
          <Space wrap size="large">
            <Button
              type="primary"
              size="large"
              icon={<FolderOutlined />}
              onClick={() => handleNavigate('categories')}
            >
              创建分类
            </Button>
            <Button
              size="large"
              icon={<UserOutlined />}
              onClick={() => handleNavigate('authors')}
            >
              添加作者
            </Button>
            <Button
              size="large"
              icon={<ProjectOutlined />}
              onClick={() => handleNavigate('projects')}
            >
              添加项目
            </Button>
            <Button
              size="large"
              icon={<EyeOutlined />}
              onClick={() => handleNavigate('statistics')}
            >
              查看统计
            </Button>
          </Space>
        </Card>

        {/* 功能特性 */}
        <Card title="功能特性">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card size="small">
                <Space>
                  <FolderOutlined style={{ fontSize: '24px', color: '#faad14' }} />
                  <div>
                    <Title level={5} style={{ margin: 0 }}>分类管理</Title>
                    <Paragraph style={{ margin: 0, fontSize: '12px' }}>
                      组织您的项目和作者，支持层级分类
                    </Paragraph>
                  </div>
                </Space>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small">
                <Space>
                  <UserOutlined style={{ fontSize: '24px', color: '#722ed1' }} />
                  <div>
                    <Title level={5} style={{ margin: 0 }}>作者监控</Title>
                    <Paragraph style={{ margin: 0, fontSize: '12px' }}>
                      跟踪您关注的开发者的最新动态
                    </Paragraph>
                  </div>
                </Space>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small">
                <Space>
                  <ProjectOutlined style={{ fontSize: '24px', color: '#13c2c2' }} />
                  <div>
                    <Title level={5} style={{ margin: 0 }}>项目跟踪</Title>
                    <Paragraph style={{ margin: 0, fontSize: '12px' }}>
                      监控项目更新、发布和重要变化
                    </Paragraph>
                  </div>
                </Space>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small">
                <Space>
                  <StarOutlined style={{ fontSize: '24px', color: '#eb2f96' }} />
                  <div>
                    <Title level={5} style={{ margin: 0 }}>统计分析</Title>
                    <Paragraph style={{ margin: 0, fontSize: '12px' }}>
                      查看详细的数据统计和趋势分析
                    </Paragraph>
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>
        </Card>
      </Space>
    </div>
  );
};

export default Home;
