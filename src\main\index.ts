/**
 * Electron主进程入口文件
 * 负责创建应用窗口和管理应用生命周期
 */

import { app } from 'electron';
import { createMainWindow, hasMainWindow } from './window';
import { createApplicationMenu } from './menu';
import { initializeIPC, cleanupIPC } from './ipc';
import { UpdateChecker } from './services/updateChecker';

/**
 * 初始化应用
 */
function initializeApp(): void {
  // 初始化IPC通信
  initializeIPC();

  // 创建主窗口
  createMainWindow();

  // 创建应用菜单
  createApplicationMenu();

  // 启动更新检查服务
  const updateChecker = UpdateChecker.getInstance();
  updateChecker.startScheduledUpdates();
}

/**
 * 应用准备就绪
 */
app.whenReady().then(() => {
  initializeApp();

  app.on('activate', () => {
    if (!hasMainWindow()) {
      createMainWindow();
    }
  });
});

/**
 * 所有窗口关闭时退出应用（macOS除外）
 */
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // 清理服务
    const updateChecker = UpdateChecker.getInstance();
    updateChecker.cleanup();

    cleanupIPC();
    app.quit();
  }
});

/**
 * 安全设置
 */
// 限制导航到外部网站
app.on('web-contents-created', (_, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    if (!parsedUrl.origin.startsWith('http://localhost:') && parsedUrl.origin !== 'file://') {
      event.preventDefault();
    }
  });
});
