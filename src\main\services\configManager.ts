/**
 * 配置管理服务
 * 负责处理应用配置的加载、保存和验证
 */

import * as fs from 'fs';
import * as path from 'path';
import { AppConfig, AppSettings } from '@/shared/types';

/**
 * 配置管理器类
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private configPath: string;
  private config: AppConfig | null = null;

  private constructor() {
    this.configPath = path.join(process.cwd(), 'data', 'config.json');
    this.ensureConfigFile();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * 确保配置文件存在
   */
  private ensureConfigFile(): void {
    const dir = path.dirname(this.configPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    if (!fs.existsSync(this.configPath)) {
      this.saveConfig(this.getDefaultConfig());
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): AppConfig {
    return {
      version: '1.0.0',
      theme: 'dark',
      language: 'zh',
      github: {
        token: '',
        rateLimit: 5000,
        concurrent: 3,
        apiUrl: 'https://api.github.com',
      },
      update: {
        interval: 3600000, // 1小时
        autoCheck: true,
        lastCheck: undefined,
      },
      ui: {
        windowSize: {
          width: 1200,
          height: 800,
        },
        windowPosition: {
          x: 100,
          y: 100,
        },
        sidebarWidth: 240,
        showStatusBar: true,
        compactMode: false,
      },
    };
  }

  /**
   * 加载配置
   */
  public loadConfig(): AppConfig {
    try {
      if (this.config) {
        return this.config;
      }

      const content = fs.readFileSync(this.configPath, 'utf-8');
      const loadedConfig = JSON.parse(content);
      
      // 合并默认配置以确保所有字段都存在
      this.config = this.mergeWithDefaults(loadedConfig);
      
      return this.config;
    } catch (error) {
      console.error('Error loading config:', error);
      this.config = this.getDefaultConfig();
      this.saveConfig(this.config);
      return this.config;
    }
  }

  /**
   * 保存配置
   */
  public saveConfig(config: AppConfig): void {
    try {
      this.config = config;
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2), 'utf-8');
    } catch (error) {
      console.error('Error saving config:', error);
      throw error;
    }
  }

  /**
   * 更新配置
   */
  public updateConfig(updates: Partial<AppConfig>): AppConfig {
    const currentConfig = this.loadConfig();
    const updatedConfig = this.deepMerge(currentConfig, updates);
    this.saveConfig(updatedConfig);
    return updatedConfig;
  }

  /**
   * 获取配置项
   */
  public getConfigValue<T>(key: string): T | undefined {
    const config = this.loadConfig();
    return this.getNestedValue(config, key);
  }

  /**
   * 设置配置项
   */
  public setConfigValue(key: string, value: any): void {
    const config = this.loadConfig();
    this.setNestedValue(config, key, value);
    this.saveConfig(config);
  }

  /**
   * 重置配置
   */
  public resetConfig(): AppConfig {
    const defaultConfig = this.getDefaultConfig();
    this.saveConfig(defaultConfig);
    return defaultConfig;
  }

  /**
   * 验证配置
   */
  public validateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证必需字段
    if (!config.version) errors.push('Missing version');
    if (!config.theme) errors.push('Missing theme');
    if (!config.language) errors.push('Missing language');

    // 验证GitHub配置
    if (!config.github) {
      errors.push('Missing github config');
    } else {
      if (typeof config.github.rateLimit !== 'number') {
        errors.push('Invalid github.rateLimit');
      }
      if (typeof config.github.concurrent !== 'number') {
        errors.push('Invalid github.concurrent');
      }
    }

    // 验证更新配置
    if (!config.update) {
      errors.push('Missing update config');
    } else {
      if (typeof config.update.interval !== 'number') {
        errors.push('Invalid update.interval');
      }
      if (typeof config.update.autoCheck !== 'boolean') {
        errors.push('Invalid update.autoCheck');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 导出配置
   */
  public exportConfig(): string {
    const config = this.loadConfig();
    return JSON.stringify(config, null, 2);
  }

  /**
   * 导入配置
   */
  public importConfig(configJson: string): AppConfig {
    try {
      const importedConfig = JSON.parse(configJson);
      const validation = this.validateConfig(importedConfig);
      
      if (!validation.isValid) {
        throw new Error(`Invalid config: ${validation.errors.join(', ')}`);
      }

      const mergedConfig = this.mergeWithDefaults(importedConfig);
      this.saveConfig(mergedConfig);
      return mergedConfig;
    } catch (error) {
      console.error('Error importing config:', error);
      throw error;
    }
  }

  /**
   * 与默认配置合并
   */
  private mergeWithDefaults(config: any): AppConfig {
    const defaultConfig = this.getDefaultConfig();
    return this.deepMerge(defaultConfig, config);
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(target[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, key: string): any {
    return key.split('.').reduce((current, prop) => current?.[prop], obj);
  }

  /**
   * 设置嵌套值
   */
  private setNestedValue(obj: any, key: string, value: any): void {
    const keys = key.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, prop) => {
      if (!current[prop]) current[prop] = {};
      return current[prop];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * 获取配置文件路径
   */
  public getConfigPath(): string {
    return this.configPath;
  }

  /**
   * 检查配置文件是否存在
   */
  public configExists(): boolean {
    return fs.existsSync(this.configPath);
  }
}
