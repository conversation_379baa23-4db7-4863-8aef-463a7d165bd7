{"version": 3, "sources": ["../../../src/generated/endpoints.ts"], "sourcesContent": ["import type { EndpointsDefaultsAndDecorations } from \"../types.js\";\nconst Endpoints: EndpointsDefaultsAndDecorations = {\n  actions: {\n    addCustomLabelsToSelfHostedRunnerForOrg: [\n      \"POST /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    addCustomLabelsToSelfHostedRunnerForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    addRepoAccessToSelfHostedRunnerGroupInOrg: [\n      \"PUT /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgVariable: [\n      \"PUT /orgs/{org}/actions/variables/{name}/repositories/{repository_id}\",\n    ],\n    approveWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve\",\n    ],\n    cancelWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel\",\n    ],\n    createEnvironmentVariable: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/variables\",\n    ],\n    createHostedRunnerForOrg: [\"POST /orgs/{org}/actions/hosted-runners\"],\n    createOrUpdateEnvironmentSecret: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    createOrUpdateOrgSecret: [\"PUT /orgs/{org}/actions/secrets/{secret_name}\"],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n    ],\n    createOrgVariable: [\"POST /orgs/{org}/actions/variables\"],\n    createRegistrationTokenForOrg: [\n      \"POST /orgs/{org}/actions/runners/registration-token\",\n    ],\n    createRegistrationTokenForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/registration-token\",\n    ],\n    createRemoveTokenForOrg: [\"POST /orgs/{org}/actions/runners/remove-token\"],\n    createRemoveTokenForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/remove-token\",\n    ],\n    createRepoVariable: [\"POST /repos/{owner}/{repo}/actions/variables\"],\n    createWorkflowDispatch: [\n      \"POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches\",\n    ],\n    deleteActionsCacheById: [\n      \"DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}\",\n    ],\n    deleteActionsCacheByKey: [\n      \"DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}\",\n    ],\n    deleteArtifact: [\n      \"DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\",\n    ],\n    deleteEnvironmentSecret: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    deleteEnvironmentVariable: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    deleteHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/actions/secrets/{secret_name}\"],\n    deleteOrgVariable: [\"DELETE /orgs/{org}/actions/variables/{name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n    ],\n    deleteRepoVariable: [\n      \"DELETE /repos/{owner}/{repo}/actions/variables/{name}\",\n    ],\n    deleteSelfHostedRunnerFromOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}\",\n    ],\n    deleteSelfHostedRunnerFromRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n    ],\n    deleteWorkflowRun: [\"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n    deleteWorkflowRunLogs: [\n      \"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n    ],\n    disableSelectedRepositoryGithubActionsOrganization: [\n      \"DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n    ],\n    disableWorkflow: [\n      \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable\",\n    ],\n    downloadArtifact: [\n      \"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}\",\n    ],\n    downloadJobLogsForWorkflowRun: [\n      \"GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs\",\n    ],\n    downloadWorkflowRunAttemptLogs: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs\",\n    ],\n    downloadWorkflowRunLogs: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n    ],\n    enableSelectedRepositoryGithubActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n    ],\n    enableWorkflow: [\n      \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable\",\n    ],\n    forceCancelWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/force-cancel\",\n    ],\n    generateRunnerJitconfigForOrg: [\n      \"POST /orgs/{org}/actions/runners/generate-jitconfig\",\n    ],\n    generateRunnerJitconfigForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/generate-jitconfig\",\n    ],\n    getActionsCacheList: [\"GET /repos/{owner}/{repo}/actions/caches\"],\n    getActionsCacheUsage: [\"GET /repos/{owner}/{repo}/actions/cache/usage\"],\n    getActionsCacheUsageByRepoForOrg: [\n      \"GET /orgs/{org}/actions/cache/usage-by-repository\",\n    ],\n    getActionsCacheUsageForOrg: [\"GET /orgs/{org}/actions/cache/usage\"],\n    getAllowedActionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/selected-actions\",\n    ],\n    getAllowedActionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n    ],\n    getArtifact: [\"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\"],\n    getCustomOidcSubClaimForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/oidc/customization/sub\",\n    ],\n    getEnvironmentPublicKey: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/public-key\",\n    ],\n    getEnvironmentSecret: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    getEnvironmentVariable: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    getGithubActionsDefaultWorkflowPermissionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/workflow\",\n    ],\n    getGithubActionsDefaultWorkflowPermissionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/workflow\",\n    ],\n    getGithubActionsPermissionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions\",\n    ],\n    getGithubActionsPermissionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions\",\n    ],\n    getHostedRunnerForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    getHostedRunnersGithubOwnedImagesForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/images/github-owned\",\n    ],\n    getHostedRunnersLimitsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/limits\",\n    ],\n    getHostedRunnersMachineSpecsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/machine-sizes\",\n    ],\n    getHostedRunnersPartnerImagesForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/images/partner\",\n    ],\n    getHostedRunnersPlatformsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/platforms\",\n    ],\n    getJobForWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/jobs/{job_id}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/actions/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/actions/secrets/{secret_name}\"],\n    getOrgVariable: [\"GET /orgs/{org}/actions/variables/{name}\"],\n    getPendingDeploymentsForRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n    ],\n    getRepoPermissions: [\n      \"GET /repos/{owner}/{repo}/actions/permissions\",\n      {},\n      { renamed: [\"actions\", \"getGithubActionsPermissionsRepository\"] },\n    ],\n    getRepoPublicKey: [\"GET /repos/{owner}/{repo}/actions/secrets/public-key\"],\n    getRepoSecret: [\"GET /repos/{owner}/{repo}/actions/secrets/{secret_name}\"],\n    getRepoVariable: [\"GET /repos/{owner}/{repo}/actions/variables/{name}\"],\n    getReviewsForRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals\",\n    ],\n    getSelfHostedRunnerForOrg: [\"GET /orgs/{org}/actions/runners/{runner_id}\"],\n    getSelfHostedRunnerForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n    ],\n    getWorkflow: [\"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}\"],\n    getWorkflowAccessToRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/access\",\n    ],\n    getWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n    getWorkflowRunAttempt: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}\",\n    ],\n    getWorkflowRunUsage: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing\",\n    ],\n    getWorkflowUsage: [\n      \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing\",\n    ],\n    listArtifactsForRepo: [\"GET /repos/{owner}/{repo}/actions/artifacts\"],\n    listEnvironmentSecrets: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets\",\n    ],\n    listEnvironmentVariables: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/variables\",\n    ],\n    listGithubHostedRunnersInGroupForOrg: [\n      \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners\",\n    ],\n    listHostedRunnersForOrg: [\"GET /orgs/{org}/actions/hosted-runners\"],\n    listJobsForWorkflowRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs\",\n    ],\n    listJobsForWorkflowRunAttempt: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs\",\n    ],\n    listLabelsForSelfHostedRunnerForOrg: [\n      \"GET /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    listLabelsForSelfHostedRunnerForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    listOrgSecrets: [\"GET /orgs/{org}/actions/secrets\"],\n    listOrgVariables: [\"GET /orgs/{org}/actions/variables\"],\n    listRepoOrganizationSecrets: [\n      \"GET /repos/{owner}/{repo}/actions/organization-secrets\",\n    ],\n    listRepoOrganizationVariables: [\n      \"GET /repos/{owner}/{repo}/actions/organization-variables\",\n    ],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/actions/secrets\"],\n    listRepoVariables: [\"GET /repos/{owner}/{repo}/actions/variables\"],\n    listRepoWorkflows: [\"GET /repos/{owner}/{repo}/actions/workflows\"],\n    listRunnerApplicationsForOrg: [\"GET /orgs/{org}/actions/runners/downloads\"],\n    listRunnerApplicationsForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/downloads\",\n    ],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    ],\n    listSelectedReposForOrgVariable: [\n      \"GET /orgs/{org}/actions/variables/{name}/repositories\",\n    ],\n    listSelectedRepositoriesEnabledGithubActionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/repositories\",\n    ],\n    listSelfHostedRunnersForOrg: [\"GET /orgs/{org}/actions/runners\"],\n    listSelfHostedRunnersForRepo: [\"GET /repos/{owner}/{repo}/actions/runners\"],\n    listWorkflowRunArtifacts: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts\",\n    ],\n    listWorkflowRuns: [\n      \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs\",\n    ],\n    listWorkflowRunsForRepo: [\"GET /repos/{owner}/{repo}/actions/runs\"],\n    reRunJobForWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun\",\n    ],\n    reRunWorkflow: [\"POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun\"],\n    reRunWorkflowFailedJobs: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs\",\n    ],\n    removeAllCustomLabelsFromSelfHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    removeAllCustomLabelsFromSelfHostedRunnerForRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    removeCustomLabelFromSelfHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}\",\n    ],\n    removeCustomLabelFromSelfHostedRunnerForRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    removeSelectedRepoFromOrgVariable: [\n      \"DELETE /orgs/{org}/actions/variables/{name}/repositories/{repository_id}\",\n    ],\n    reviewCustomGatesForRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/deployment_protection_rule\",\n    ],\n    reviewPendingDeploymentsForRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n    ],\n    setAllowedActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/selected-actions\",\n    ],\n    setAllowedActionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n    ],\n    setCustomLabelsForSelfHostedRunnerForOrg: [\n      \"PUT /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    setCustomLabelsForSelfHostedRunnerForRepo: [\n      \"PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    setCustomOidcSubClaimForRepo: [\n      \"PUT /repos/{owner}/{repo}/actions/oidc/customization/sub\",\n    ],\n    setGithubActionsDefaultWorkflowPermissionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/workflow\",\n    ],\n    setGithubActionsDefaultWorkflowPermissionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/workflow\",\n    ],\n    setGithubActionsPermissionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions\",\n    ],\n    setGithubActionsPermissionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    ],\n    setSelectedReposForOrgVariable: [\n      \"PUT /orgs/{org}/actions/variables/{name}/repositories\",\n    ],\n    setSelectedRepositoriesEnabledGithubActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/repositories\",\n    ],\n    setWorkflowAccessToRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/access\",\n    ],\n    updateEnvironmentVariable: [\n      \"PATCH /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    updateHostedRunnerForOrg: [\n      \"PATCH /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    updateOrgVariable: [\"PATCH /orgs/{org}/actions/variables/{name}\"],\n    updateRepoVariable: [\n      \"PATCH /repos/{owner}/{repo}/actions/variables/{name}\",\n    ],\n  },\n  activity: {\n    checkRepoIsStarredByAuthenticatedUser: [\"GET /user/starred/{owner}/{repo}\"],\n    deleteRepoSubscription: [\"DELETE /repos/{owner}/{repo}/subscription\"],\n    deleteThreadSubscription: [\n      \"DELETE /notifications/threads/{thread_id}/subscription\",\n    ],\n    getFeeds: [\"GET /feeds\"],\n    getRepoSubscription: [\"GET /repos/{owner}/{repo}/subscription\"],\n    getThread: [\"GET /notifications/threads/{thread_id}\"],\n    getThreadSubscriptionForAuthenticatedUser: [\n      \"GET /notifications/threads/{thread_id}/subscription\",\n    ],\n    listEventsForAuthenticatedUser: [\"GET /users/{username}/events\"],\n    listNotificationsForAuthenticatedUser: [\"GET /notifications\"],\n    listOrgEventsForAuthenticatedUser: [\n      \"GET /users/{username}/events/orgs/{org}\",\n    ],\n    listPublicEvents: [\"GET /events\"],\n    listPublicEventsForRepoNetwork: [\"GET /networks/{owner}/{repo}/events\"],\n    listPublicEventsForUser: [\"GET /users/{username}/events/public\"],\n    listPublicOrgEvents: [\"GET /orgs/{org}/events\"],\n    listReceivedEventsForUser: [\"GET /users/{username}/received_events\"],\n    listReceivedPublicEventsForUser: [\n      \"GET /users/{username}/received_events/public\",\n    ],\n    listRepoEvents: [\"GET /repos/{owner}/{repo}/events\"],\n    listRepoNotificationsForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/notifications\",\n    ],\n    listReposStarredByAuthenticatedUser: [\"GET /user/starred\"],\n    listReposStarredByUser: [\"GET /users/{username}/starred\"],\n    listReposWatchedByUser: [\"GET /users/{username}/subscriptions\"],\n    listStargazersForRepo: [\"GET /repos/{owner}/{repo}/stargazers\"],\n    listWatchedReposForAuthenticatedUser: [\"GET /user/subscriptions\"],\n    listWatchersForRepo: [\"GET /repos/{owner}/{repo}/subscribers\"],\n    markNotificationsAsRead: [\"PUT /notifications\"],\n    markRepoNotificationsAsRead: [\"PUT /repos/{owner}/{repo}/notifications\"],\n    markThreadAsDone: [\"DELETE /notifications/threads/{thread_id}\"],\n    markThreadAsRead: [\"PATCH /notifications/threads/{thread_id}\"],\n    setRepoSubscription: [\"PUT /repos/{owner}/{repo}/subscription\"],\n    setThreadSubscription: [\n      \"PUT /notifications/threads/{thread_id}/subscription\",\n    ],\n    starRepoForAuthenticatedUser: [\"PUT /user/starred/{owner}/{repo}\"],\n    unstarRepoForAuthenticatedUser: [\"DELETE /user/starred/{owner}/{repo}\"],\n  },\n  apps: {\n    addRepoToInstallation: [\n      \"PUT /user/installations/{installation_id}/repositories/{repository_id}\",\n      {},\n      { renamed: [\"apps\", \"addRepoToInstallationForAuthenticatedUser\"] },\n    ],\n    addRepoToInstallationForAuthenticatedUser: [\n      \"PUT /user/installations/{installation_id}/repositories/{repository_id}\",\n    ],\n    checkToken: [\"POST /applications/{client_id}/token\"],\n    createFromManifest: [\"POST /app-manifests/{code}/conversions\"],\n    createInstallationAccessToken: [\n      \"POST /app/installations/{installation_id}/access_tokens\",\n    ],\n    deleteAuthorization: [\"DELETE /applications/{client_id}/grant\"],\n    deleteInstallation: [\"DELETE /app/installations/{installation_id}\"],\n    deleteToken: [\"DELETE /applications/{client_id}/token\"],\n    getAuthenticated: [\"GET /app\"],\n    getBySlug: [\"GET /apps/{app_slug}\"],\n    getInstallation: [\"GET /app/installations/{installation_id}\"],\n    getOrgInstallation: [\"GET /orgs/{org}/installation\"],\n    getRepoInstallation: [\"GET /repos/{owner}/{repo}/installation\"],\n    getSubscriptionPlanForAccount: [\n      \"GET /marketplace_listing/accounts/{account_id}\",\n    ],\n    getSubscriptionPlanForAccountStubbed: [\n      \"GET /marketplace_listing/stubbed/accounts/{account_id}\",\n    ],\n    getUserInstallation: [\"GET /users/{username}/installation\"],\n    getWebhookConfigForApp: [\"GET /app/hook/config\"],\n    getWebhookDelivery: [\"GET /app/hook/deliveries/{delivery_id}\"],\n    listAccountsForPlan: [\"GET /marketplace_listing/plans/{plan_id}/accounts\"],\n    listAccountsForPlanStubbed: [\n      \"GET /marketplace_listing/stubbed/plans/{plan_id}/accounts\",\n    ],\n    listInstallationReposForAuthenticatedUser: [\n      \"GET /user/installations/{installation_id}/repositories\",\n    ],\n    listInstallationRequestsForAuthenticatedApp: [\n      \"GET /app/installation-requests\",\n    ],\n    listInstallations: [\"GET /app/installations\"],\n    listInstallationsForAuthenticatedUser: [\"GET /user/installations\"],\n    listPlans: [\"GET /marketplace_listing/plans\"],\n    listPlansStubbed: [\"GET /marketplace_listing/stubbed/plans\"],\n    listReposAccessibleToInstallation: [\"GET /installation/repositories\"],\n    listSubscriptionsForAuthenticatedUser: [\"GET /user/marketplace_purchases\"],\n    listSubscriptionsForAuthenticatedUserStubbed: [\n      \"GET /user/marketplace_purchases/stubbed\",\n    ],\n    listWebhookDeliveries: [\"GET /app/hook/deliveries\"],\n    redeliverWebhookDelivery: [\n      \"POST /app/hook/deliveries/{delivery_id}/attempts\",\n    ],\n    removeRepoFromInstallation: [\n      \"DELETE /user/installations/{installation_id}/repositories/{repository_id}\",\n      {},\n      { renamed: [\"apps\", \"removeRepoFromInstallationForAuthenticatedUser\"] },\n    ],\n    removeRepoFromInstallationForAuthenticatedUser: [\n      \"DELETE /user/installations/{installation_id}/repositories/{repository_id}\",\n    ],\n    resetToken: [\"PATCH /applications/{client_id}/token\"],\n    revokeInstallationAccessToken: [\"DELETE /installation/token\"],\n    scopeToken: [\"POST /applications/{client_id}/token/scoped\"],\n    suspendInstallation: [\"PUT /app/installations/{installation_id}/suspended\"],\n    unsuspendInstallation: [\n      \"DELETE /app/installations/{installation_id}/suspended\",\n    ],\n    updateWebhookConfigForApp: [\"PATCH /app/hook/config\"],\n  },\n  billing: {\n    getGithubActionsBillingOrg: [\"GET /orgs/{org}/settings/billing/actions\"],\n    getGithubActionsBillingUser: [\n      \"GET /users/{username}/settings/billing/actions\",\n    ],\n    getGithubBillingUsageReportOrg: [\n      \"GET /organizations/{org}/settings/billing/usage\",\n    ],\n    getGithubBillingUsageReportUser: [\n      \"GET /users/{username}/settings/billing/usage\",\n    ],\n    getGithubPackagesBillingOrg: [\"GET /orgs/{org}/settings/billing/packages\"],\n    getGithubPackagesBillingUser: [\n      \"GET /users/{username}/settings/billing/packages\",\n    ],\n    getSharedStorageBillingOrg: [\n      \"GET /orgs/{org}/settings/billing/shared-storage\",\n    ],\n    getSharedStorageBillingUser: [\n      \"GET /users/{username}/settings/billing/shared-storage\",\n    ],\n  },\n  campaigns: {\n    createCampaign: [\"POST /orgs/{org}/campaigns\"],\n    deleteCampaign: [\"DELETE /orgs/{org}/campaigns/{campaign_number}\"],\n    getCampaignSummary: [\"GET /orgs/{org}/campaigns/{campaign_number}\"],\n    listOrgCampaigns: [\"GET /orgs/{org}/campaigns\"],\n    updateCampaign: [\"PATCH /orgs/{org}/campaigns/{campaign_number}\"],\n  },\n  checks: {\n    create: [\"POST /repos/{owner}/{repo}/check-runs\"],\n    createSuite: [\"POST /repos/{owner}/{repo}/check-suites\"],\n    get: [\"GET /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n    getSuite: [\"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}\"],\n    listAnnotations: [\n      \"GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations\",\n    ],\n    listForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-runs\"],\n    listForSuite: [\n      \"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs\",\n    ],\n    listSuitesForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-suites\"],\n    rerequestRun: [\n      \"POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest\",\n    ],\n    rerequestSuite: [\n      \"POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest\",\n    ],\n    setSuitesPreferences: [\n      \"PATCH /repos/{owner}/{repo}/check-suites/preferences\",\n    ],\n    update: [\"PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n  },\n  codeScanning: {\n    commitAutofix: [\n      \"POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix/commits\",\n    ],\n    createAutofix: [\n      \"POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix\",\n    ],\n    createVariantAnalysis: [\n      \"POST /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses\",\n    ],\n    deleteAnalysis: [\n      \"DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}\",\n    ],\n    deleteCodeqlDatabase: [\n      \"DELETE /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}\",\n    ],\n    getAlert: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n      {},\n      { renamedParameters: { alert_id: \"alert_number\" } },\n    ],\n    getAnalysis: [\n      \"GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}\",\n    ],\n    getAutofix: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix\",\n    ],\n    getCodeqlDatabase: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}\",\n    ],\n    getDefaultSetup: [\"GET /repos/{owner}/{repo}/code-scanning/default-setup\"],\n    getSarif: [\"GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}\"],\n    getVariantAnalysis: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}\",\n    ],\n    getVariantAnalysisRepoTask: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}/repos/{repo_owner}/{repo_name}\",\n    ],\n    listAlertInstances: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/code-scanning/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/code-scanning/alerts\"],\n    listAlertsInstances: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n      {},\n      { renamed: [\"codeScanning\", \"listAlertInstances\"] },\n    ],\n    listCodeqlDatabases: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/databases\",\n    ],\n    listRecentAnalyses: [\"GET /repos/{owner}/{repo}/code-scanning/analyses\"],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n    ],\n    updateDefaultSetup: [\n      \"PATCH /repos/{owner}/{repo}/code-scanning/default-setup\",\n    ],\n    uploadSarif: [\"POST /repos/{owner}/{repo}/code-scanning/sarifs\"],\n  },\n  codeSecurity: {\n    attachConfiguration: [\n      \"POST /orgs/{org}/code-security/configurations/{configuration_id}/attach\",\n    ],\n    attachEnterpriseConfiguration: [\n      \"POST /enterprises/{enterprise}/code-security/configurations/{configuration_id}/attach\",\n    ],\n    createConfiguration: [\"POST /orgs/{org}/code-security/configurations\"],\n    createConfigurationForEnterprise: [\n      \"POST /enterprises/{enterprise}/code-security/configurations\",\n    ],\n    deleteConfiguration: [\n      \"DELETE /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    deleteConfigurationForEnterprise: [\n      \"DELETE /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n    detachConfiguration: [\n      \"DELETE /orgs/{org}/code-security/configurations/detach\",\n    ],\n    getConfiguration: [\n      \"GET /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    getConfigurationForRepository: [\n      \"GET /repos/{owner}/{repo}/code-security-configuration\",\n    ],\n    getConfigurationsForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations\",\n    ],\n    getConfigurationsForOrg: [\"GET /orgs/{org}/code-security/configurations\"],\n    getDefaultConfigurations: [\n      \"GET /orgs/{org}/code-security/configurations/defaults\",\n    ],\n    getDefaultConfigurationsForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/defaults\",\n    ],\n    getRepositoriesForConfiguration: [\n      \"GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories\",\n    ],\n    getRepositoriesForEnterpriseConfiguration: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories\",\n    ],\n    getSingleConfigurationForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n    setConfigurationAsDefault: [\n      \"PUT /orgs/{org}/code-security/configurations/{configuration_id}/defaults\",\n    ],\n    setConfigurationAsDefaultForEnterprise: [\n      \"PUT /enterprises/{enterprise}/code-security/configurations/{configuration_id}/defaults\",\n    ],\n    updateConfiguration: [\n      \"PATCH /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    updateEnterpriseConfiguration: [\n      \"PATCH /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n  },\n  codesOfConduct: {\n    getAllCodesOfConduct: [\"GET /codes_of_conduct\"],\n    getConductCode: [\"GET /codes_of_conduct/{key}\"],\n  },\n  codespaces: {\n    addRepositoryForSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    checkPermissionsForDevcontainer: [\n      \"GET /repos/{owner}/{repo}/codespaces/permissions_check\",\n    ],\n    codespaceMachinesForAuthenticatedUser: [\n      \"GET /user/codespaces/{codespace_name}/machines\",\n    ],\n    createForAuthenticatedUser: [\"POST /user/codespaces\"],\n    createOrUpdateOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}\",\n    ],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    createOrUpdateSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}\",\n    ],\n    createWithPrForAuthenticatedUser: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces\",\n    ],\n    createWithRepoForAuthenticatedUser: [\n      \"POST /repos/{owner}/{repo}/codespaces\",\n    ],\n    deleteForAuthenticatedUser: [\"DELETE /user/codespaces/{codespace_name}\"],\n    deleteFromOrganization: [\n      \"DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/codespaces/secrets/{secret_name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    deleteSecretForAuthenticatedUser: [\n      \"DELETE /user/codespaces/secrets/{secret_name}\",\n    ],\n    exportForAuthenticatedUser: [\n      \"POST /user/codespaces/{codespace_name}/exports\",\n    ],\n    getCodespacesForUserInOrg: [\n      \"GET /orgs/{org}/members/{username}/codespaces\",\n    ],\n    getExportDetailsForAuthenticatedUser: [\n      \"GET /user/codespaces/{codespace_name}/exports/{export_id}\",\n    ],\n    getForAuthenticatedUser: [\"GET /user/codespaces/{codespace_name}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/codespaces/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/codespaces/secrets/{secret_name}\"],\n    getPublicKeyForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/public-key\",\n    ],\n    getRepoPublicKey: [\n      \"GET /repos/{owner}/{repo}/codespaces/secrets/public-key\",\n    ],\n    getRepoSecret: [\n      \"GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    getSecretForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/{secret_name}\",\n    ],\n    listDevcontainersInRepositoryForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/devcontainers\",\n    ],\n    listForAuthenticatedUser: [\"GET /user/codespaces\"],\n    listInOrganization: [\n      \"GET /orgs/{org}/codespaces\",\n      {},\n      { renamedParameters: { org_id: \"org\" } },\n    ],\n    listInRepositoryForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces\",\n    ],\n    listOrgSecrets: [\"GET /orgs/{org}/codespaces/secrets\"],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/codespaces/secrets\"],\n    listRepositoriesForSecretForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    listSecretsForAuthenticatedUser: [\"GET /user/codespaces/secrets\"],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    preFlightWithRepoForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/new\",\n    ],\n    publishForAuthenticatedUser: [\n      \"POST /user/codespaces/{codespace_name}/publish\",\n    ],\n    removeRepositoryForSecretForAuthenticatedUser: [\n      \"DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    repoMachinesForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/machines\",\n    ],\n    setRepositoriesForSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    startForAuthenticatedUser: [\"POST /user/codespaces/{codespace_name}/start\"],\n    stopForAuthenticatedUser: [\"POST /user/codespaces/{codespace_name}/stop\"],\n    stopInOrganization: [\n      \"POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop\",\n    ],\n    updateForAuthenticatedUser: [\"PATCH /user/codespaces/{codespace_name}\"],\n  },\n  copilot: {\n    addCopilotSeatsForTeams: [\n      \"POST /orgs/{org}/copilot/billing/selected_teams\",\n    ],\n    addCopilotSeatsForUsers: [\n      \"POST /orgs/{org}/copilot/billing/selected_users\",\n    ],\n    cancelCopilotSeatAssignmentForTeams: [\n      \"DELETE /orgs/{org}/copilot/billing/selected_teams\",\n    ],\n    cancelCopilotSeatAssignmentForUsers: [\n      \"DELETE /orgs/{org}/copilot/billing/selected_users\",\n    ],\n    copilotMetricsForOrganization: [\"GET /orgs/{org}/copilot/metrics\"],\n    copilotMetricsForTeam: [\"GET /orgs/{org}/team/{team_slug}/copilot/metrics\"],\n    getCopilotOrganizationDetails: [\"GET /orgs/{org}/copilot/billing\"],\n    getCopilotSeatDetailsForUser: [\n      \"GET /orgs/{org}/members/{username}/copilot\",\n    ],\n    listCopilotSeats: [\"GET /orgs/{org}/copilot/billing/seats\"],\n  },\n  credentials: { revoke: [\"POST /credentials/revoke\"] },\n  dependabot: {\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    createOrUpdateOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}\",\n    ],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/dependabot/secrets/{secret_name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    getAlert: [\"GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/dependabot/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/dependabot/secrets/{secret_name}\"],\n    getRepoPublicKey: [\n      \"GET /repos/{owner}/{repo}/dependabot/secrets/public-key\",\n    ],\n    getRepoSecret: [\n      \"GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    listAlertsForEnterprise: [\n      \"GET /enterprises/{enterprise}/dependabot/alerts\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/dependabot/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/dependabot/alerts\"],\n    listOrgSecrets: [\"GET /orgs/{org}/dependabot/secrets\"],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/dependabot/secrets\"],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    ],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}\",\n    ],\n  },\n  dependencyGraph: {\n    createRepositorySnapshot: [\n      \"POST /repos/{owner}/{repo}/dependency-graph/snapshots\",\n    ],\n    diffRange: [\n      \"GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}\",\n    ],\n    exportSbom: [\"GET /repos/{owner}/{repo}/dependency-graph/sbom\"],\n  },\n  emojis: { get: [\"GET /emojis\"] },\n  gists: {\n    checkIsStarred: [\"GET /gists/{gist_id}/star\"],\n    create: [\"POST /gists\"],\n    createComment: [\"POST /gists/{gist_id}/comments\"],\n    delete: [\"DELETE /gists/{gist_id}\"],\n    deleteComment: [\"DELETE /gists/{gist_id}/comments/{comment_id}\"],\n    fork: [\"POST /gists/{gist_id}/forks\"],\n    get: [\"GET /gists/{gist_id}\"],\n    getComment: [\"GET /gists/{gist_id}/comments/{comment_id}\"],\n    getRevision: [\"GET /gists/{gist_id}/{sha}\"],\n    list: [\"GET /gists\"],\n    listComments: [\"GET /gists/{gist_id}/comments\"],\n    listCommits: [\"GET /gists/{gist_id}/commits\"],\n    listForUser: [\"GET /users/{username}/gists\"],\n    listForks: [\"GET /gists/{gist_id}/forks\"],\n    listPublic: [\"GET /gists/public\"],\n    listStarred: [\"GET /gists/starred\"],\n    star: [\"PUT /gists/{gist_id}/star\"],\n    unstar: [\"DELETE /gists/{gist_id}/star\"],\n    update: [\"PATCH /gists/{gist_id}\"],\n    updateComment: [\"PATCH /gists/{gist_id}/comments/{comment_id}\"],\n  },\n  git: {\n    createBlob: [\"POST /repos/{owner}/{repo}/git/blobs\"],\n    createCommit: [\"POST /repos/{owner}/{repo}/git/commits\"],\n    createRef: [\"POST /repos/{owner}/{repo}/git/refs\"],\n    createTag: [\"POST /repos/{owner}/{repo}/git/tags\"],\n    createTree: [\"POST /repos/{owner}/{repo}/git/trees\"],\n    deleteRef: [\"DELETE /repos/{owner}/{repo}/git/refs/{ref}\"],\n    getBlob: [\"GET /repos/{owner}/{repo}/git/blobs/{file_sha}\"],\n    getCommit: [\"GET /repos/{owner}/{repo}/git/commits/{commit_sha}\"],\n    getRef: [\"GET /repos/{owner}/{repo}/git/ref/{ref}\"],\n    getTag: [\"GET /repos/{owner}/{repo}/git/tags/{tag_sha}\"],\n    getTree: [\"GET /repos/{owner}/{repo}/git/trees/{tree_sha}\"],\n    listMatchingRefs: [\"GET /repos/{owner}/{repo}/git/matching-refs/{ref}\"],\n    updateRef: [\"PATCH /repos/{owner}/{repo}/git/refs/{ref}\"],\n  },\n  gitignore: {\n    getAllTemplates: [\"GET /gitignore/templates\"],\n    getTemplate: [\"GET /gitignore/templates/{name}\"],\n  },\n  hostedCompute: {\n    createNetworkConfigurationForOrg: [\n      \"POST /orgs/{org}/settings/network-configurations\",\n    ],\n    deleteNetworkConfigurationFromOrg: [\n      \"DELETE /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n    getNetworkConfigurationForOrg: [\n      \"GET /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n    getNetworkSettingsForOrg: [\n      \"GET /orgs/{org}/settings/network-settings/{network_settings_id}\",\n    ],\n    listNetworkConfigurationsForOrg: [\n      \"GET /orgs/{org}/settings/network-configurations\",\n    ],\n    updateNetworkConfigurationForOrg: [\n      \"PATCH /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n  },\n  interactions: {\n    getRestrictionsForAuthenticatedUser: [\"GET /user/interaction-limits\"],\n    getRestrictionsForOrg: [\"GET /orgs/{org}/interaction-limits\"],\n    getRestrictionsForRepo: [\"GET /repos/{owner}/{repo}/interaction-limits\"],\n    getRestrictionsForYourPublicRepos: [\n      \"GET /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"getRestrictionsForAuthenticatedUser\"] },\n    ],\n    removeRestrictionsForAuthenticatedUser: [\"DELETE /user/interaction-limits\"],\n    removeRestrictionsForOrg: [\"DELETE /orgs/{org}/interaction-limits\"],\n    removeRestrictionsForRepo: [\n      \"DELETE /repos/{owner}/{repo}/interaction-limits\",\n    ],\n    removeRestrictionsForYourPublicRepos: [\n      \"DELETE /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"removeRestrictionsForAuthenticatedUser\"] },\n    ],\n    setRestrictionsForAuthenticatedUser: [\"PUT /user/interaction-limits\"],\n    setRestrictionsForOrg: [\"PUT /orgs/{org}/interaction-limits\"],\n    setRestrictionsForRepo: [\"PUT /repos/{owner}/{repo}/interaction-limits\"],\n    setRestrictionsForYourPublicRepos: [\n      \"PUT /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"setRestrictionsForAuthenticatedUser\"] },\n    ],\n  },\n  issues: {\n    addAssignees: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n    ],\n    addLabels: [\"POST /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n    addSubIssue: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/sub_issues\",\n    ],\n    checkUserCanBeAssigned: [\"GET /repos/{owner}/{repo}/assignees/{assignee}\"],\n    checkUserCanBeAssignedToIssue: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/assignees/{assignee}\",\n    ],\n    create: [\"POST /repos/{owner}/{repo}/issues\"],\n    createComment: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/comments\",\n    ],\n    createLabel: [\"POST /repos/{owner}/{repo}/labels\"],\n    createMilestone: [\"POST /repos/{owner}/{repo}/milestones\"],\n    deleteComment: [\n      \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}\",\n    ],\n    deleteLabel: [\"DELETE /repos/{owner}/{repo}/labels/{name}\"],\n    deleteMilestone: [\n      \"DELETE /repos/{owner}/{repo}/milestones/{milestone_number}\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}/issues/{issue_number}\"],\n    getComment: [\"GET /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n    getEvent: [\"GET /repos/{owner}/{repo}/issues/events/{event_id}\"],\n    getLabel: [\"GET /repos/{owner}/{repo}/labels/{name}\"],\n    getMilestone: [\"GET /repos/{owner}/{repo}/milestones/{milestone_number}\"],\n    list: [\"GET /issues\"],\n    listAssignees: [\"GET /repos/{owner}/{repo}/assignees\"],\n    listComments: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/comments\"],\n    listCommentsForRepo: [\"GET /repos/{owner}/{repo}/issues/comments\"],\n    listEvents: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/events\"],\n    listEventsForRepo: [\"GET /repos/{owner}/{repo}/issues/events\"],\n    listEventsForTimeline: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/timeline\",\n    ],\n    listForAuthenticatedUser: [\"GET /user/issues\"],\n    listForOrg: [\"GET /orgs/{org}/issues\"],\n    listForRepo: [\"GET /repos/{owner}/{repo}/issues\"],\n    listLabelsForMilestone: [\n      \"GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels\",\n    ],\n    listLabelsForRepo: [\"GET /repos/{owner}/{repo}/labels\"],\n    listLabelsOnIssue: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    ],\n    listMilestones: [\"GET /repos/{owner}/{repo}/milestones\"],\n    listSubIssues: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues\",\n    ],\n    lock: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n    removeAllLabels: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    ],\n    removeAssignees: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n    ],\n    removeLabel: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}\",\n    ],\n    removeSubIssue: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/sub_issue\",\n    ],\n    reprioritizeSubIssue: [\n      \"PATCH /repos/{owner}/{repo}/issues/{issue_number}/sub_issues/priority\",\n    ],\n    setLabels: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n    unlock: [\"DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n    update: [\"PATCH /repos/{owner}/{repo}/issues/{issue_number}\"],\n    updateComment: [\"PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n    updateLabel: [\"PATCH /repos/{owner}/{repo}/labels/{name}\"],\n    updateMilestone: [\n      \"PATCH /repos/{owner}/{repo}/milestones/{milestone_number}\",\n    ],\n  },\n  licenses: {\n    get: [\"GET /licenses/{license}\"],\n    getAllCommonlyUsed: [\"GET /licenses\"],\n    getForRepo: [\"GET /repos/{owner}/{repo}/license\"],\n  },\n  markdown: {\n    render: [\"POST /markdown\"],\n    renderRaw: [\n      \"POST /markdown/raw\",\n      { headers: { \"content-type\": \"text/plain; charset=utf-8\" } },\n    ],\n  },\n  meta: {\n    get: [\"GET /meta\"],\n    getAllVersions: [\"GET /versions\"],\n    getOctocat: [\"GET /octocat\"],\n    getZen: [\"GET /zen\"],\n    root: [\"GET /\"],\n  },\n  migrations: {\n    deleteArchiveForAuthenticatedUser: [\n      \"DELETE /user/migrations/{migration_id}/archive\",\n    ],\n    deleteArchiveForOrg: [\n      \"DELETE /orgs/{org}/migrations/{migration_id}/archive\",\n    ],\n    downloadArchiveForOrg: [\n      \"GET /orgs/{org}/migrations/{migration_id}/archive\",\n    ],\n    getArchiveForAuthenticatedUser: [\n      \"GET /user/migrations/{migration_id}/archive\",\n    ],\n    getStatusForAuthenticatedUser: [\"GET /user/migrations/{migration_id}\"],\n    getStatusForOrg: [\"GET /orgs/{org}/migrations/{migration_id}\"],\n    listForAuthenticatedUser: [\"GET /user/migrations\"],\n    listForOrg: [\"GET /orgs/{org}/migrations\"],\n    listReposForAuthenticatedUser: [\n      \"GET /user/migrations/{migration_id}/repositories\",\n    ],\n    listReposForOrg: [\"GET /orgs/{org}/migrations/{migration_id}/repositories\"],\n    listReposForUser: [\n      \"GET /user/migrations/{migration_id}/repositories\",\n      {},\n      { renamed: [\"migrations\", \"listReposForAuthenticatedUser\"] },\n    ],\n    startForAuthenticatedUser: [\"POST /user/migrations\"],\n    startForOrg: [\"POST /orgs/{org}/migrations\"],\n    unlockRepoForAuthenticatedUser: [\n      \"DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock\",\n    ],\n    unlockRepoForOrg: [\n      \"DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock\",\n    ],\n  },\n  oidc: {\n    getOidcCustomSubTemplateForOrg: [\n      \"GET /orgs/{org}/actions/oidc/customization/sub\",\n    ],\n    updateOidcCustomSubTemplateForOrg: [\n      \"PUT /orgs/{org}/actions/oidc/customization/sub\",\n    ],\n  },\n  orgs: {\n    addSecurityManagerTeam: [\n      \"PUT /orgs/{org}/security-managers/teams/{team_slug}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.addSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#add-a-security-manager-team\",\n      },\n    ],\n    assignTeamToOrgRole: [\n      \"PUT /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}\",\n    ],\n    assignUserToOrgRole: [\n      \"PUT /orgs/{org}/organization-roles/users/{username}/{role_id}\",\n    ],\n    blockUser: [\"PUT /orgs/{org}/blocks/{username}\"],\n    cancelInvitation: [\"DELETE /orgs/{org}/invitations/{invitation_id}\"],\n    checkBlockedUser: [\"GET /orgs/{org}/blocks/{username}\"],\n    checkMembershipForUser: [\"GET /orgs/{org}/members/{username}\"],\n    checkPublicMembershipForUser: [\"GET /orgs/{org}/public_members/{username}\"],\n    convertMemberToOutsideCollaborator: [\n      \"PUT /orgs/{org}/outside_collaborators/{username}\",\n    ],\n    createInvitation: [\"POST /orgs/{org}/invitations\"],\n    createIssueType: [\"POST /orgs/{org}/issue-types\"],\n    createOrUpdateCustomProperties: [\"PATCH /orgs/{org}/properties/schema\"],\n    createOrUpdateCustomPropertiesValuesForRepos: [\n      \"PATCH /orgs/{org}/properties/values\",\n    ],\n    createOrUpdateCustomProperty: [\n      \"PUT /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    createWebhook: [\"POST /orgs/{org}/hooks\"],\n    delete: [\"DELETE /orgs/{org}\"],\n    deleteIssueType: [\"DELETE /orgs/{org}/issue-types/{issue_type_id}\"],\n    deleteWebhook: [\"DELETE /orgs/{org}/hooks/{hook_id}\"],\n    enableOrDisableSecurityProductOnAllOrgRepos: [\n      \"POST /orgs/{org}/{security_product}/{enablement}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.enableOrDisableSecurityProductOnAllOrgRepos() is deprecated, see https://docs.github.com/rest/orgs/orgs#enable-or-disable-a-security-feature-for-an-organization\",\n      },\n    ],\n    get: [\"GET /orgs/{org}\"],\n    getAllCustomProperties: [\"GET /orgs/{org}/properties/schema\"],\n    getCustomProperty: [\n      \"GET /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    getMembershipForAuthenticatedUser: [\"GET /user/memberships/orgs/{org}\"],\n    getMembershipForUser: [\"GET /orgs/{org}/memberships/{username}\"],\n    getOrgRole: [\"GET /orgs/{org}/organization-roles/{role_id}\"],\n    getOrgRulesetHistory: [\"GET /orgs/{org}/rulesets/{ruleset_id}/history\"],\n    getOrgRulesetVersion: [\n      \"GET /orgs/{org}/rulesets/{ruleset_id}/history/{version_id}\",\n    ],\n    getWebhook: [\"GET /orgs/{org}/hooks/{hook_id}\"],\n    getWebhookConfigForOrg: [\"GET /orgs/{org}/hooks/{hook_id}/config\"],\n    getWebhookDelivery: [\n      \"GET /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}\",\n    ],\n    list: [\"GET /organizations\"],\n    listAppInstallations: [\"GET /orgs/{org}/installations\"],\n    listAttestations: [\"GET /orgs/{org}/attestations/{subject_digest}\"],\n    listBlockedUsers: [\"GET /orgs/{org}/blocks\"],\n    listCustomPropertiesValuesForRepos: [\"GET /orgs/{org}/properties/values\"],\n    listFailedInvitations: [\"GET /orgs/{org}/failed_invitations\"],\n    listForAuthenticatedUser: [\"GET /user/orgs\"],\n    listForUser: [\"GET /users/{username}/orgs\"],\n    listInvitationTeams: [\"GET /orgs/{org}/invitations/{invitation_id}/teams\"],\n    listIssueTypes: [\"GET /orgs/{org}/issue-types\"],\n    listMembers: [\"GET /orgs/{org}/members\"],\n    listMembershipsForAuthenticatedUser: [\"GET /user/memberships/orgs\"],\n    listOrgRoleTeams: [\"GET /orgs/{org}/organization-roles/{role_id}/teams\"],\n    listOrgRoleUsers: [\"GET /orgs/{org}/organization-roles/{role_id}/users\"],\n    listOrgRoles: [\"GET /orgs/{org}/organization-roles\"],\n    listOrganizationFineGrainedPermissions: [\n      \"GET /orgs/{org}/organization-fine-grained-permissions\",\n    ],\n    listOutsideCollaborators: [\"GET /orgs/{org}/outside_collaborators\"],\n    listPatGrantRepositories: [\n      \"GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories\",\n    ],\n    listPatGrantRequestRepositories: [\n      \"GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories\",\n    ],\n    listPatGrantRequests: [\"GET /orgs/{org}/personal-access-token-requests\"],\n    listPatGrants: [\"GET /orgs/{org}/personal-access-tokens\"],\n    listPendingInvitations: [\"GET /orgs/{org}/invitations\"],\n    listPublicMembers: [\"GET /orgs/{org}/public_members\"],\n    listSecurityManagerTeams: [\n      \"GET /orgs/{org}/security-managers\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.listSecurityManagerTeams() is deprecated, see https://docs.github.com/rest/orgs/security-managers#list-security-manager-teams\",\n      },\n    ],\n    listWebhookDeliveries: [\"GET /orgs/{org}/hooks/{hook_id}/deliveries\"],\n    listWebhooks: [\"GET /orgs/{org}/hooks\"],\n    pingWebhook: [\"POST /orgs/{org}/hooks/{hook_id}/pings\"],\n    redeliverWebhookDelivery: [\n      \"POST /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}/attempts\",\n    ],\n    removeCustomProperty: [\n      \"DELETE /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    removeMember: [\"DELETE /orgs/{org}/members/{username}\"],\n    removeMembershipForUser: [\"DELETE /orgs/{org}/memberships/{username}\"],\n    removeOutsideCollaborator: [\n      \"DELETE /orgs/{org}/outside_collaborators/{username}\",\n    ],\n    removePublicMembershipForAuthenticatedUser: [\n      \"DELETE /orgs/{org}/public_members/{username}\",\n    ],\n    removeSecurityManagerTeam: [\n      \"DELETE /orgs/{org}/security-managers/teams/{team_slug}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.removeSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#remove-a-security-manager-team\",\n      },\n    ],\n    reviewPatGrantRequest: [\n      \"POST /orgs/{org}/personal-access-token-requests/{pat_request_id}\",\n    ],\n    reviewPatGrantRequestsInBulk: [\n      \"POST /orgs/{org}/personal-access-token-requests\",\n    ],\n    revokeAllOrgRolesTeam: [\n      \"DELETE /orgs/{org}/organization-roles/teams/{team_slug}\",\n    ],\n    revokeAllOrgRolesUser: [\n      \"DELETE /orgs/{org}/organization-roles/users/{username}\",\n    ],\n    revokeOrgRoleTeam: [\n      \"DELETE /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}\",\n    ],\n    revokeOrgRoleUser: [\n      \"DELETE /orgs/{org}/organization-roles/users/{username}/{role_id}\",\n    ],\n    setMembershipForUser: [\"PUT /orgs/{org}/memberships/{username}\"],\n    setPublicMembershipForAuthenticatedUser: [\n      \"PUT /orgs/{org}/public_members/{username}\",\n    ],\n    unblockUser: [\"DELETE /orgs/{org}/blocks/{username}\"],\n    update: [\"PATCH /orgs/{org}\"],\n    updateIssueType: [\"PUT /orgs/{org}/issue-types/{issue_type_id}\"],\n    updateMembershipForAuthenticatedUser: [\n      \"PATCH /user/memberships/orgs/{org}\",\n    ],\n    updatePatAccess: [\"POST /orgs/{org}/personal-access-tokens/{pat_id}\"],\n    updatePatAccesses: [\"POST /orgs/{org}/personal-access-tokens\"],\n    updateWebhook: [\"PATCH /orgs/{org}/hooks/{hook_id}\"],\n    updateWebhookConfigForOrg: [\"PATCH /orgs/{org}/hooks/{hook_id}/config\"],\n  },\n  packages: {\n    deletePackageForAuthenticatedUser: [\n      \"DELETE /user/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageForOrg: [\n      \"DELETE /orgs/{org}/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageForUser: [\n      \"DELETE /users/{username}/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageVersionForAuthenticatedUser: [\n      \"DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    deletePackageVersionForOrg: [\n      \"DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    deletePackageVersionForUser: [\n      \"DELETE /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getAllPackageVersionsForAPackageOwnedByAnOrg: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n      {},\n      { renamed: [\"packages\", \"getAllPackageVersionsForPackageOwnedByOrg\"] },\n    ],\n    getAllPackageVersionsForAPackageOwnedByTheAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions\",\n      {},\n      {\n        renamed: [\n          \"packages\",\n          \"getAllPackageVersionsForPackageOwnedByAuthenticatedUser\",\n        ],\n      },\n    ],\n    getAllPackageVersionsForPackageOwnedByAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions\",\n    ],\n    getAllPackageVersionsForPackageOwnedByOrg: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n    ],\n    getAllPackageVersionsForPackageOwnedByUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}/versions\",\n    ],\n    getPackageForAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}\",\n    ],\n    getPackageForOrganization: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}\",\n    ],\n    getPackageForUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}\",\n    ],\n    getPackageVersionForAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getPackageVersionForOrganization: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getPackageVersionForUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    listDockerMigrationConflictingPackagesForAuthenticatedUser: [\n      \"GET /user/docker/conflicts\",\n    ],\n    listDockerMigrationConflictingPackagesForOrganization: [\n      \"GET /orgs/{org}/docker/conflicts\",\n    ],\n    listDockerMigrationConflictingPackagesForUser: [\n      \"GET /users/{username}/docker/conflicts\",\n    ],\n    listPackagesForAuthenticatedUser: [\"GET /user/packages\"],\n    listPackagesForOrganization: [\"GET /orgs/{org}/packages\"],\n    listPackagesForUser: [\"GET /users/{username}/packages\"],\n    restorePackageForAuthenticatedUser: [\n      \"POST /user/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageForOrg: [\n      \"POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageForUser: [\n      \"POST /users/{username}/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageVersionForAuthenticatedUser: [\n      \"POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n    restorePackageVersionForOrg: [\n      \"POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n    restorePackageVersionForUser: [\n      \"POST /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n  },\n  privateRegistries: {\n    createOrgPrivateRegistry: [\"POST /orgs/{org}/private-registries\"],\n    deleteOrgPrivateRegistry: [\n      \"DELETE /orgs/{org}/private-registries/{secret_name}\",\n    ],\n    getOrgPrivateRegistry: [\"GET /orgs/{org}/private-registries/{secret_name}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/private-registries/public-key\"],\n    listOrgPrivateRegistries: [\"GET /orgs/{org}/private-registries\"],\n    updateOrgPrivateRegistry: [\n      \"PATCH /orgs/{org}/private-registries/{secret_name}\",\n    ],\n  },\n  pulls: {\n    checkIfMerged: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n    create: [\"POST /repos/{owner}/{repo}/pulls\"],\n    createReplyForReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies\",\n    ],\n    createReview: [\"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n    createReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    ],\n    deletePendingReview: [\n      \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    deleteReviewComment: [\n      \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n    ],\n    dismissReview: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}\"],\n    getReview: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    getReviewComment: [\"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}\"],\n    list: [\"GET /repos/{owner}/{repo}/pulls\"],\n    listCommentsForReview: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments\",\n    ],\n    listCommits: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/commits\"],\n    listFiles: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/files\"],\n    listRequestedReviewers: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    listReviewComments: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    ],\n    listReviewCommentsForRepo: [\"GET /repos/{owner}/{repo}/pulls/comments\"],\n    listReviews: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n    merge: [\"PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n    removeRequestedReviewers: [\n      \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    requestReviewers: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    submitReview: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events\",\n    ],\n    update: [\"PATCH /repos/{owner}/{repo}/pulls/{pull_number}\"],\n    updateBranch: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch\",\n    ],\n    updateReview: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    updateReviewComment: [\n      \"PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n    ],\n  },\n  rateLimit: { get: [\"GET /rate_limit\"] },\n  reactions: {\n    createForCommitComment: [\n      \"POST /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    ],\n    createForIssue: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n    ],\n    createForIssueComment: [\n      \"POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    ],\n    createForPullRequestReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    ],\n    createForRelease: [\n      \"POST /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    ],\n    createForTeamDiscussionCommentInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    ],\n    createForTeamDiscussionInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    ],\n    deleteForCommitComment: [\n      \"DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForIssue: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}\",\n    ],\n    deleteForIssueComment: [\n      \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForPullRequestComment: [\n      \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForRelease: [\n      \"DELETE /repos/{owner}/{repo}/releases/{release_id}/reactions/{reaction_id}\",\n    ],\n    deleteForTeamDiscussion: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}\",\n    ],\n    deleteForTeamDiscussionComment: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}\",\n    ],\n    listForCommitComment: [\n      \"GET /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    ],\n    listForIssue: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/reactions\"],\n    listForIssueComment: [\n      \"GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    ],\n    listForPullRequestReviewComment: [\n      \"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    ],\n    listForRelease: [\n      \"GET /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    ],\n    listForTeamDiscussionCommentInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    ],\n    listForTeamDiscussionInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    ],\n  },\n  repos: {\n    acceptInvitation: [\n      \"PATCH /user/repository_invitations/{invitation_id}\",\n      {},\n      { renamed: [\"repos\", \"acceptInvitationForAuthenticatedUser\"] },\n    ],\n    acceptInvitationForAuthenticatedUser: [\n      \"PATCH /user/repository_invitations/{invitation_id}\",\n    ],\n    addAppAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    addCollaborator: [\"PUT /repos/{owner}/{repo}/collaborators/{username}\"],\n    addStatusCheckContexts: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    addTeamAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    addUserAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    cancelPagesDeployment: [\n      \"POST /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}/cancel\",\n    ],\n    checkAutomatedSecurityFixes: [\n      \"GET /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    checkCollaborator: [\"GET /repos/{owner}/{repo}/collaborators/{username}\"],\n    checkPrivateVulnerabilityReporting: [\n      \"GET /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    checkVulnerabilityAlerts: [\n      \"GET /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    codeownersErrors: [\"GET /repos/{owner}/{repo}/codeowners/errors\"],\n    compareCommits: [\"GET /repos/{owner}/{repo}/compare/{base}...{head}\"],\n    compareCommitsWithBasehead: [\n      \"GET /repos/{owner}/{repo}/compare/{basehead}\",\n    ],\n    createAttestation: [\"POST /repos/{owner}/{repo}/attestations\"],\n    createAutolink: [\"POST /repos/{owner}/{repo}/autolinks\"],\n    createCommitComment: [\n      \"POST /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    ],\n    createCommitSignatureProtection: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    createCommitStatus: [\"POST /repos/{owner}/{repo}/statuses/{sha}\"],\n    createDeployKey: [\"POST /repos/{owner}/{repo}/keys\"],\n    createDeployment: [\"POST /repos/{owner}/{repo}/deployments\"],\n    createDeploymentBranchPolicy: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies\",\n    ],\n    createDeploymentProtectionRule: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules\",\n    ],\n    createDeploymentStatus: [\n      \"POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    ],\n    createDispatchEvent: [\"POST /repos/{owner}/{repo}/dispatches\"],\n    createForAuthenticatedUser: [\"POST /user/repos\"],\n    createFork: [\"POST /repos/{owner}/{repo}/forks\"],\n    createInOrg: [\"POST /orgs/{org}/repos\"],\n    createOrUpdateCustomPropertiesValues: [\n      \"PATCH /repos/{owner}/{repo}/properties/values\",\n    ],\n    createOrUpdateEnvironment: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    createOrUpdateFileContents: [\"PUT /repos/{owner}/{repo}/contents/{path}\"],\n    createOrgRuleset: [\"POST /orgs/{org}/rulesets\"],\n    createPagesDeployment: [\"POST /repos/{owner}/{repo}/pages/deployments\"],\n    createPagesSite: [\"POST /repos/{owner}/{repo}/pages\"],\n    createRelease: [\"POST /repos/{owner}/{repo}/releases\"],\n    createRepoRuleset: [\"POST /repos/{owner}/{repo}/rulesets\"],\n    createUsingTemplate: [\n      \"POST /repos/{template_owner}/{template_repo}/generate\",\n    ],\n    createWebhook: [\"POST /repos/{owner}/{repo}/hooks\"],\n    declineInvitation: [\n      \"DELETE /user/repository_invitations/{invitation_id}\",\n      {},\n      { renamed: [\"repos\", \"declineInvitationForAuthenticatedUser\"] },\n    ],\n    declineInvitationForAuthenticatedUser: [\n      \"DELETE /user/repository_invitations/{invitation_id}\",\n    ],\n    delete: [\"DELETE /repos/{owner}/{repo}\"],\n    deleteAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n    ],\n    deleteAdminBranchProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    deleteAnEnvironment: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    deleteAutolink: [\"DELETE /repos/{owner}/{repo}/autolinks/{autolink_id}\"],\n    deleteBranchProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    deleteCommitComment: [\"DELETE /repos/{owner}/{repo}/comments/{comment_id}\"],\n    deleteCommitSignatureProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    deleteDeployKey: [\"DELETE /repos/{owner}/{repo}/keys/{key_id}\"],\n    deleteDeployment: [\n      \"DELETE /repos/{owner}/{repo}/deployments/{deployment_id}\",\n    ],\n    deleteDeploymentBranchPolicy: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    deleteFile: [\"DELETE /repos/{owner}/{repo}/contents/{path}\"],\n    deleteInvitation: [\n      \"DELETE /repos/{owner}/{repo}/invitations/{invitation_id}\",\n    ],\n    deleteOrgRuleset: [\"DELETE /orgs/{org}/rulesets/{ruleset_id}\"],\n    deletePagesSite: [\"DELETE /repos/{owner}/{repo}/pages\"],\n    deletePullRequestReviewProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    deleteRelease: [\"DELETE /repos/{owner}/{repo}/releases/{release_id}\"],\n    deleteReleaseAsset: [\n      \"DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n    ],\n    deleteRepoRuleset: [\"DELETE /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    deleteWebhook: [\"DELETE /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    disableAutomatedSecurityFixes: [\n      \"DELETE /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    disableDeploymentProtectionRule: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}\",\n    ],\n    disablePrivateVulnerabilityReporting: [\n      \"DELETE /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    disableVulnerabilityAlerts: [\n      \"DELETE /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    downloadArchive: [\n      \"GET /repos/{owner}/{repo}/zipball/{ref}\",\n      {},\n      { renamed: [\"repos\", \"downloadZipballArchive\"] },\n    ],\n    downloadTarballArchive: [\"GET /repos/{owner}/{repo}/tarball/{ref}\"],\n    downloadZipballArchive: [\"GET /repos/{owner}/{repo}/zipball/{ref}\"],\n    enableAutomatedSecurityFixes: [\n      \"PUT /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    enablePrivateVulnerabilityReporting: [\n      \"PUT /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    enableVulnerabilityAlerts: [\n      \"PUT /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    generateReleaseNotes: [\n      \"POST /repos/{owner}/{repo}/releases/generate-notes\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}\"],\n    getAccessRestrictions: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n    ],\n    getAdminBranchProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    getAllDeploymentProtectionRules: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules\",\n    ],\n    getAllEnvironments: [\"GET /repos/{owner}/{repo}/environments\"],\n    getAllStatusCheckContexts: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n    ],\n    getAllTopics: [\"GET /repos/{owner}/{repo}/topics\"],\n    getAppsWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n    ],\n    getAutolink: [\"GET /repos/{owner}/{repo}/autolinks/{autolink_id}\"],\n    getBranch: [\"GET /repos/{owner}/{repo}/branches/{branch}\"],\n    getBranchProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    getBranchRules: [\"GET /repos/{owner}/{repo}/rules/branches/{branch}\"],\n    getClones: [\"GET /repos/{owner}/{repo}/traffic/clones\"],\n    getCodeFrequencyStats: [\"GET /repos/{owner}/{repo}/stats/code_frequency\"],\n    getCollaboratorPermissionLevel: [\n      \"GET /repos/{owner}/{repo}/collaborators/{username}/permission\",\n    ],\n    getCombinedStatusForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/status\"],\n    getCommit: [\"GET /repos/{owner}/{repo}/commits/{ref}\"],\n    getCommitActivityStats: [\"GET /repos/{owner}/{repo}/stats/commit_activity\"],\n    getCommitComment: [\"GET /repos/{owner}/{repo}/comments/{comment_id}\"],\n    getCommitSignatureProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    getCommunityProfileMetrics: [\"GET /repos/{owner}/{repo}/community/profile\"],\n    getContent: [\"GET /repos/{owner}/{repo}/contents/{path}\"],\n    getContributorsStats: [\"GET /repos/{owner}/{repo}/stats/contributors\"],\n    getCustomDeploymentProtectionRule: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}\",\n    ],\n    getCustomPropertiesValues: [\"GET /repos/{owner}/{repo}/properties/values\"],\n    getDeployKey: [\"GET /repos/{owner}/{repo}/keys/{key_id}\"],\n    getDeployment: [\"GET /repos/{owner}/{repo}/deployments/{deployment_id}\"],\n    getDeploymentBranchPolicy: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    getDeploymentStatus: [\n      \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}\",\n    ],\n    getEnvironment: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    getLatestPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/latest\"],\n    getLatestRelease: [\"GET /repos/{owner}/{repo}/releases/latest\"],\n    getOrgRuleSuite: [\"GET /orgs/{org}/rulesets/rule-suites/{rule_suite_id}\"],\n    getOrgRuleSuites: [\"GET /orgs/{org}/rulesets/rule-suites\"],\n    getOrgRuleset: [\"GET /orgs/{org}/rulesets/{ruleset_id}\"],\n    getOrgRulesets: [\"GET /orgs/{org}/rulesets\"],\n    getPages: [\"GET /repos/{owner}/{repo}/pages\"],\n    getPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/{build_id}\"],\n    getPagesDeployment: [\n      \"GET /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}\",\n    ],\n    getPagesHealthCheck: [\"GET /repos/{owner}/{repo}/pages/health\"],\n    getParticipationStats: [\"GET /repos/{owner}/{repo}/stats/participation\"],\n    getPullRequestReviewProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    getPunchCardStats: [\"GET /repos/{owner}/{repo}/stats/punch_card\"],\n    getReadme: [\"GET /repos/{owner}/{repo}/readme\"],\n    getReadmeInDirectory: [\"GET /repos/{owner}/{repo}/readme/{dir}\"],\n    getRelease: [\"GET /repos/{owner}/{repo}/releases/{release_id}\"],\n    getReleaseAsset: [\"GET /repos/{owner}/{repo}/releases/assets/{asset_id}\"],\n    getReleaseByTag: [\"GET /repos/{owner}/{repo}/releases/tags/{tag}\"],\n    getRepoRuleSuite: [\n      \"GET /repos/{owner}/{repo}/rulesets/rule-suites/{rule_suite_id}\",\n    ],\n    getRepoRuleSuites: [\"GET /repos/{owner}/{repo}/rulesets/rule-suites\"],\n    getRepoRuleset: [\"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    getRepoRulesetHistory: [\n      \"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history\",\n    ],\n    getRepoRulesetVersion: [\n      \"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history/{version_id}\",\n    ],\n    getRepoRulesets: [\"GET /repos/{owner}/{repo}/rulesets\"],\n    getStatusChecksProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    getTeamsWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n    ],\n    getTopPaths: [\"GET /repos/{owner}/{repo}/traffic/popular/paths\"],\n    getTopReferrers: [\"GET /repos/{owner}/{repo}/traffic/popular/referrers\"],\n    getUsersWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n    ],\n    getViews: [\"GET /repos/{owner}/{repo}/traffic/views\"],\n    getWebhook: [\"GET /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    getWebhookConfigForRepo: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n    ],\n    getWebhookDelivery: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}\",\n    ],\n    listActivities: [\"GET /repos/{owner}/{repo}/activity\"],\n    listAttestations: [\n      \"GET /repos/{owner}/{repo}/attestations/{subject_digest}\",\n    ],\n    listAutolinks: [\"GET /repos/{owner}/{repo}/autolinks\"],\n    listBranches: [\"GET /repos/{owner}/{repo}/branches\"],\n    listBranchesForHeadCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head\",\n    ],\n    listCollaborators: [\"GET /repos/{owner}/{repo}/collaborators\"],\n    listCommentsForCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    ],\n    listCommitCommentsForRepo: [\"GET /repos/{owner}/{repo}/comments\"],\n    listCommitStatusesForRef: [\n      \"GET /repos/{owner}/{repo}/commits/{ref}/statuses\",\n    ],\n    listCommits: [\"GET /repos/{owner}/{repo}/commits\"],\n    listContributors: [\"GET /repos/{owner}/{repo}/contributors\"],\n    listCustomDeploymentRuleIntegrations: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps\",\n    ],\n    listDeployKeys: [\"GET /repos/{owner}/{repo}/keys\"],\n    listDeploymentBranchPolicies: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies\",\n    ],\n    listDeploymentStatuses: [\n      \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    ],\n    listDeployments: [\"GET /repos/{owner}/{repo}/deployments\"],\n    listForAuthenticatedUser: [\"GET /user/repos\"],\n    listForOrg: [\"GET /orgs/{org}/repos\"],\n    listForUser: [\"GET /users/{username}/repos\"],\n    listForks: [\"GET /repos/{owner}/{repo}/forks\"],\n    listInvitations: [\"GET /repos/{owner}/{repo}/invitations\"],\n    listInvitationsForAuthenticatedUser: [\"GET /user/repository_invitations\"],\n    listLanguages: [\"GET /repos/{owner}/{repo}/languages\"],\n    listPagesBuilds: [\"GET /repos/{owner}/{repo}/pages/builds\"],\n    listPublic: [\"GET /repositories\"],\n    listPullRequestsAssociatedWithCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls\",\n    ],\n    listReleaseAssets: [\n      \"GET /repos/{owner}/{repo}/releases/{release_id}/assets\",\n    ],\n    listReleases: [\"GET /repos/{owner}/{repo}/releases\"],\n    listTags: [\"GET /repos/{owner}/{repo}/tags\"],\n    listTeams: [\"GET /repos/{owner}/{repo}/teams\"],\n    listWebhookDeliveries: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries\",\n    ],\n    listWebhooks: [\"GET /repos/{owner}/{repo}/hooks\"],\n    merge: [\"POST /repos/{owner}/{repo}/merges\"],\n    mergeUpstream: [\"POST /repos/{owner}/{repo}/merge-upstream\"],\n    pingWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/pings\"],\n    redeliverWebhookDelivery: [\n      \"POST /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}/attempts\",\n    ],\n    removeAppAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    removeCollaborator: [\n      \"DELETE /repos/{owner}/{repo}/collaborators/{username}\",\n    ],\n    removeStatusCheckContexts: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    removeStatusCheckProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    removeTeamAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    removeUserAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    renameBranch: [\"POST /repos/{owner}/{repo}/branches/{branch}/rename\"],\n    replaceAllTopics: [\"PUT /repos/{owner}/{repo}/topics\"],\n    requestPagesBuild: [\"POST /repos/{owner}/{repo}/pages/builds\"],\n    setAdminBranchProtection: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    setAppAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    setStatusCheckContexts: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    setTeamAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    setUserAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    testPushWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/tests\"],\n    transfer: [\"POST /repos/{owner}/{repo}/transfer\"],\n    update: [\"PATCH /repos/{owner}/{repo}\"],\n    updateBranchProtection: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    updateCommitComment: [\"PATCH /repos/{owner}/{repo}/comments/{comment_id}\"],\n    updateDeploymentBranchPolicy: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    updateInformationAboutPagesSite: [\"PUT /repos/{owner}/{repo}/pages\"],\n    updateInvitation: [\n      \"PATCH /repos/{owner}/{repo}/invitations/{invitation_id}\",\n    ],\n    updateOrgRuleset: [\"PUT /orgs/{org}/rulesets/{ruleset_id}\"],\n    updatePullRequestReviewProtection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    updateRelease: [\"PATCH /repos/{owner}/{repo}/releases/{release_id}\"],\n    updateReleaseAsset: [\n      \"PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n    ],\n    updateRepoRuleset: [\"PUT /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    updateStatusCheckPotection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n      {},\n      { renamed: [\"repos\", \"updateStatusCheckProtection\"] },\n    ],\n    updateStatusCheckProtection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    updateWebhook: [\"PATCH /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    updateWebhookConfigForRepo: [\n      \"PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n    ],\n    uploadReleaseAsset: [\n      \"POST /repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}\",\n      { baseUrl: \"https://uploads.github.com\" },\n    ],\n  },\n  search: {\n    code: [\"GET /search/code\"],\n    commits: [\"GET /search/commits\"],\n    issuesAndPullRequests: [\n      \"GET /search/issues\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.search.issuesAndPullRequests() is deprecated, see https://docs.github.com/rest/search/search#search-issues-and-pull-requests\",\n      },\n    ],\n    labels: [\"GET /search/labels\"],\n    repos: [\"GET /search/repositories\"],\n    topics: [\"GET /search/topics\"],\n    users: [\"GET /search/users\"],\n  },\n  secretScanning: {\n    createPushProtectionBypass: [\n      \"POST /repos/{owner}/{repo}/secret-scanning/push-protection-bypasses\",\n    ],\n    getAlert: [\n      \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n    ],\n    getScanHistory: [\"GET /repos/{owner}/{repo}/secret-scanning/scan-history\"],\n    listAlertsForEnterprise: [\n      \"GET /enterprises/{enterprise}/secret-scanning/alerts\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/secret-scanning/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/secret-scanning/alerts\"],\n    listLocationsForAlert: [\n      \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations\",\n    ],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n    ],\n  },\n  securityAdvisories: {\n    createFork: [\n      \"POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/forks\",\n    ],\n    createPrivateVulnerabilityReport: [\n      \"POST /repos/{owner}/{repo}/security-advisories/reports\",\n    ],\n    createRepositoryAdvisory: [\n      \"POST /repos/{owner}/{repo}/security-advisories\",\n    ],\n    createRepositoryAdvisoryCveRequest: [\n      \"POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/cve\",\n    ],\n    getGlobalAdvisory: [\"GET /advisories/{ghsa_id}\"],\n    getRepositoryAdvisory: [\n      \"GET /repos/{owner}/{repo}/security-advisories/{ghsa_id}\",\n    ],\n    listGlobalAdvisories: [\"GET /advisories\"],\n    listOrgRepositoryAdvisories: [\"GET /orgs/{org}/security-advisories\"],\n    listRepositoryAdvisories: [\"GET /repos/{owner}/{repo}/security-advisories\"],\n    updateRepositoryAdvisory: [\n      \"PATCH /repos/{owner}/{repo}/security-advisories/{ghsa_id}\",\n    ],\n  },\n  teams: {\n    addOrUpdateMembershipForUserInOrg: [\n      \"PUT /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    addOrUpdateRepoPermissionsInOrg: [\n      \"PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    checkPermissionsForRepoInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    create: [\"POST /orgs/{org}/teams\"],\n    createDiscussionCommentInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    ],\n    createDiscussionInOrg: [\"POST /orgs/{org}/teams/{team_slug}/discussions\"],\n    deleteDiscussionCommentInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    deleteDiscussionInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    deleteInOrg: [\"DELETE /orgs/{org}/teams/{team_slug}\"],\n    getByName: [\"GET /orgs/{org}/teams/{team_slug}\"],\n    getDiscussionCommentInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    getDiscussionInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    getMembershipForUserInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    list: [\"GET /orgs/{org}/teams\"],\n    listChildInOrg: [\"GET /orgs/{org}/teams/{team_slug}/teams\"],\n    listDiscussionCommentsInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    ],\n    listDiscussionsInOrg: [\"GET /orgs/{org}/teams/{team_slug}/discussions\"],\n    listForAuthenticatedUser: [\"GET /user/teams\"],\n    listMembersInOrg: [\"GET /orgs/{org}/teams/{team_slug}/members\"],\n    listPendingInvitationsInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/invitations\",\n    ],\n    listReposInOrg: [\"GET /orgs/{org}/teams/{team_slug}/repos\"],\n    removeMembershipForUserInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    removeRepoInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    updateDiscussionCommentInOrg: [\n      \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    updateDiscussionInOrg: [\n      \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    updateInOrg: [\"PATCH /orgs/{org}/teams/{team_slug}\"],\n  },\n  users: {\n    addEmailForAuthenticated: [\n      \"POST /user/emails\",\n      {},\n      { renamed: [\"users\", \"addEmailForAuthenticatedUser\"] },\n    ],\n    addEmailForAuthenticatedUser: [\"POST /user/emails\"],\n    addSocialAccountForAuthenticatedUser: [\"POST /user/social_accounts\"],\n    block: [\"PUT /user/blocks/{username}\"],\n    checkBlocked: [\"GET /user/blocks/{username}\"],\n    checkFollowingForUser: [\"GET /users/{username}/following/{target_user}\"],\n    checkPersonIsFollowedByAuthenticated: [\"GET /user/following/{username}\"],\n    createGpgKeyForAuthenticated: [\n      \"POST /user/gpg_keys\",\n      {},\n      { renamed: [\"users\", \"createGpgKeyForAuthenticatedUser\"] },\n    ],\n    createGpgKeyForAuthenticatedUser: [\"POST /user/gpg_keys\"],\n    createPublicSshKeyForAuthenticated: [\n      \"POST /user/keys\",\n      {},\n      { renamed: [\"users\", \"createPublicSshKeyForAuthenticatedUser\"] },\n    ],\n    createPublicSshKeyForAuthenticatedUser: [\"POST /user/keys\"],\n    createSshSigningKeyForAuthenticatedUser: [\"POST /user/ssh_signing_keys\"],\n    deleteEmailForAuthenticated: [\n      \"DELETE /user/emails\",\n      {},\n      { renamed: [\"users\", \"deleteEmailForAuthenticatedUser\"] },\n    ],\n    deleteEmailForAuthenticatedUser: [\"DELETE /user/emails\"],\n    deleteGpgKeyForAuthenticated: [\n      \"DELETE /user/gpg_keys/{gpg_key_id}\",\n      {},\n      { renamed: [\"users\", \"deleteGpgKeyForAuthenticatedUser\"] },\n    ],\n    deleteGpgKeyForAuthenticatedUser: [\"DELETE /user/gpg_keys/{gpg_key_id}\"],\n    deletePublicSshKeyForAuthenticated: [\n      \"DELETE /user/keys/{key_id}\",\n      {},\n      { renamed: [\"users\", \"deletePublicSshKeyForAuthenticatedUser\"] },\n    ],\n    deletePublicSshKeyForAuthenticatedUser: [\"DELETE /user/keys/{key_id}\"],\n    deleteSocialAccountForAuthenticatedUser: [\"DELETE /user/social_accounts\"],\n    deleteSshSigningKeyForAuthenticatedUser: [\n      \"DELETE /user/ssh_signing_keys/{ssh_signing_key_id}\",\n    ],\n    follow: [\"PUT /user/following/{username}\"],\n    getAuthenticated: [\"GET /user\"],\n    getById: [\"GET /user/{account_id}\"],\n    getByUsername: [\"GET /users/{username}\"],\n    getContextForUser: [\"GET /users/{username}/hovercard\"],\n    getGpgKeyForAuthenticated: [\n      \"GET /user/gpg_keys/{gpg_key_id}\",\n      {},\n      { renamed: [\"users\", \"getGpgKeyForAuthenticatedUser\"] },\n    ],\n    getGpgKeyForAuthenticatedUser: [\"GET /user/gpg_keys/{gpg_key_id}\"],\n    getPublicSshKeyForAuthenticated: [\n      \"GET /user/keys/{key_id}\",\n      {},\n      { renamed: [\"users\", \"getPublicSshKeyForAuthenticatedUser\"] },\n    ],\n    getPublicSshKeyForAuthenticatedUser: [\"GET /user/keys/{key_id}\"],\n    getSshSigningKeyForAuthenticatedUser: [\n      \"GET /user/ssh_signing_keys/{ssh_signing_key_id}\",\n    ],\n    list: [\"GET /users\"],\n    listAttestations: [\"GET /users/{username}/attestations/{subject_digest}\"],\n    listBlockedByAuthenticated: [\n      \"GET /user/blocks\",\n      {},\n      { renamed: [\"users\", \"listBlockedByAuthenticatedUser\"] },\n    ],\n    listBlockedByAuthenticatedUser: [\"GET /user/blocks\"],\n    listEmailsForAuthenticated: [\n      \"GET /user/emails\",\n      {},\n      { renamed: [\"users\", \"listEmailsForAuthenticatedUser\"] },\n    ],\n    listEmailsForAuthenticatedUser: [\"GET /user/emails\"],\n    listFollowedByAuthenticated: [\n      \"GET /user/following\",\n      {},\n      { renamed: [\"users\", \"listFollowedByAuthenticatedUser\"] },\n    ],\n    listFollowedByAuthenticatedUser: [\"GET /user/following\"],\n    listFollowersForAuthenticatedUser: [\"GET /user/followers\"],\n    listFollowersForUser: [\"GET /users/{username}/followers\"],\n    listFollowingForUser: [\"GET /users/{username}/following\"],\n    listGpgKeysForAuthenticated: [\n      \"GET /user/gpg_keys\",\n      {},\n      { renamed: [\"users\", \"listGpgKeysForAuthenticatedUser\"] },\n    ],\n    listGpgKeysForAuthenticatedUser: [\"GET /user/gpg_keys\"],\n    listGpgKeysForUser: [\"GET /users/{username}/gpg_keys\"],\n    listPublicEmailsForAuthenticated: [\n      \"GET /user/public_emails\",\n      {},\n      { renamed: [\"users\", \"listPublicEmailsForAuthenticatedUser\"] },\n    ],\n    listPublicEmailsForAuthenticatedUser: [\"GET /user/public_emails\"],\n    listPublicKeysForUser: [\"GET /users/{username}/keys\"],\n    listPublicSshKeysForAuthenticated: [\n      \"GET /user/keys\",\n      {},\n      { renamed: [\"users\", \"listPublicSshKeysForAuthenticatedUser\"] },\n    ],\n    listPublicSshKeysForAuthenticatedUser: [\"GET /user/keys\"],\n    listSocialAccountsForAuthenticatedUser: [\"GET /user/social_accounts\"],\n    listSocialAccountsForUser: [\"GET /users/{username}/social_accounts\"],\n    listSshSigningKeysForAuthenticatedUser: [\"GET /user/ssh_signing_keys\"],\n    listSshSigningKeysForUser: [\"GET /users/{username}/ssh_signing_keys\"],\n    setPrimaryEmailVisibilityForAuthenticated: [\n      \"PATCH /user/email/visibility\",\n      {},\n      { renamed: [\"users\", \"setPrimaryEmailVisibilityForAuthenticatedUser\"] },\n    ],\n    setPrimaryEmailVisibilityForAuthenticatedUser: [\n      \"PATCH /user/email/visibility\",\n    ],\n    unblock: [\"DELETE /user/blocks/{username}\"],\n    unfollow: [\"DELETE /user/following/{username}\"],\n    updateAuthenticated: [\"PATCH /user\"],\n  },\n};\n\nexport default Endpoints;\n"], "mappings": "AACA,MAAM,YAA6C;AAAA,EACjD,SAAS;AAAA,IACP,yCAAyC;AAAA,MACvC;AAAA,IACF;AAAA,IACA,0CAA0C;AAAA,MACxC;AAAA,IACF;AAAA,IACA,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,0BAA0B,CAAC,yCAAyC;AAAA,IACpE,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,yBAAyB,CAAC,+CAA+C;AAAA,IACzE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,oCAAoC;AAAA,IACxD,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,yBAAyB,CAAC,+CAA+C;AAAA,IACzE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,8CAA8C;AAAA,IACnE,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,kDAAkD;AAAA,IACpE,mBAAmB,CAAC,6CAA6C;AAAA,IACjE,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,oDAAoD;AAAA,IACxE,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,oDAAoD;AAAA,MAClD;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,mDAAmD;AAAA,MACjD;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,0CAA0C;AAAA,IAChE,sBAAsB,CAAC,+CAA+C;AAAA,IACtE,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,4BAA4B,CAAC,qCAAqC;AAAA,IAClE,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,aAAa,CAAC,2DAA2D;AAAA,IACzE,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,wDAAwD;AAAA,MACtD;AAAA,IACF;AAAA,IACA,sDAAsD;AAAA,MACpD;AAAA,IACF;AAAA,IACA,yCAAyC;AAAA,MACvC;AAAA,IACF;AAAA,IACA,uCAAuC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,yCAAyC;AAAA,MACvC;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,oCAAoC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,sBAAsB,CAAC,iDAAiD;AAAA,IACxE,iBAAiB,CAAC,4CAA4C;AAAA,IAC9D,cAAc,CAAC,+CAA+C;AAAA,IAC9D,gBAAgB,CAAC,0CAA0C;AAAA,IAC3D,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,WAAW,uCAAuC,EAAE;AAAA,IAClE;AAAA,IACA,kBAAkB,CAAC,sDAAsD;AAAA,IACzE,eAAe,CAAC,yDAAyD;AAAA,IACzE,iBAAiB,CAAC,oDAAoD;AAAA,IACtE,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,2BAA2B,CAAC,6CAA6C;AAAA,IACzE,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,aAAa,CAAC,2DAA2D;AAAA,IACzE,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,iDAAiD;AAAA,IAClE,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,sBAAsB,CAAC,6CAA6C;AAAA,IACpE,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,yBAAyB,CAAC,wCAAwC;AAAA,IAClE,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,iCAAiC;AAAA,IAClD,kBAAkB,CAAC,mCAAmC;AAAA,IACtD,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,2CAA2C;AAAA,IAC7D,mBAAmB,CAAC,6CAA6C;AAAA,IACjE,mBAAmB,CAAC,6CAA6C;AAAA,IACjE,8BAA8B,CAAC,2CAA2C;AAAA,IAC1E,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,0DAA0D;AAAA,MACxD;AAAA,IACF;AAAA,IACA,6BAA6B,CAAC,iCAAiC;AAAA,IAC/D,8BAA8B,CAAC,2CAA2C;AAAA,IAC1E,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,yBAAyB,CAAC,wCAAwC;AAAA,IAClE,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,eAAe,CAAC,wDAAwD;AAAA,IACxE,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,iDAAiD;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,kDAAkD;AAAA,MAChD;AAAA,IACF;AAAA,IACA,6CAA6C;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,8CAA8C;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,0CAA0C;AAAA,MACxC;AAAA,IACF;AAAA,IACA,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,wDAAwD;AAAA,MACtD;AAAA,IACF;AAAA,IACA,sDAAsD;AAAA,MACpD;AAAA,IACF;AAAA,IACA,yCAAyC;AAAA,MACvC;AAAA,IACF;AAAA,IACA,uCAAuC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,yDAAyD;AAAA,MACvD;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,4CAA4C;AAAA,IAChE,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,uCAAuC,CAAC,kCAAkC;AAAA,IAC1E,wBAAwB,CAAC,2CAA2C;AAAA,IACpE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,qBAAqB,CAAC,wCAAwC;AAAA,IAC9D,WAAW,CAAC,wCAAwC;AAAA,IACpD,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,gCAAgC,CAAC,8BAA8B;AAAA,IAC/D,uCAAuC,CAAC,oBAAoB;AAAA,IAC5D,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,aAAa;AAAA,IAChC,gCAAgC,CAAC,qCAAqC;AAAA,IACtE,yBAAyB,CAAC,qCAAqC;AAAA,IAC/D,qBAAqB,CAAC,wBAAwB;AAAA,IAC9C,2BAA2B,CAAC,uCAAuC;AAAA,IACnE,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,kCAAkC;AAAA,IACnD,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,qCAAqC,CAAC,mBAAmB;AAAA,IACzD,wBAAwB,CAAC,+BAA+B;AAAA,IACxD,wBAAwB,CAAC,qCAAqC;AAAA,IAC9D,uBAAuB,CAAC,sCAAsC;AAAA,IAC9D,sCAAsC,CAAC,yBAAyB;AAAA,IAChE,qBAAqB,CAAC,uCAAuC;AAAA,IAC7D,yBAAyB,CAAC,oBAAoB;AAAA,IAC9C,6BAA6B,CAAC,yCAAyC;AAAA,IACvE,kBAAkB,CAAC,2CAA2C;AAAA,IAC9D,kBAAkB,CAAC,0CAA0C;AAAA,IAC7D,qBAAqB,CAAC,wCAAwC;AAAA,IAC9D,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,8BAA8B,CAAC,kCAAkC;AAAA,IACjE,gCAAgC,CAAC,qCAAqC;AAAA,EACxE;AAAA,EACA,MAAM;AAAA,IACJ,uBAAuB;AAAA,MACrB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,QAAQ,2CAA2C,EAAE;AAAA,IACnE;AAAA,IACA,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,YAAY,CAAC,sCAAsC;AAAA,IACnD,oBAAoB,CAAC,wCAAwC;AAAA,IAC7D,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,wCAAwC;AAAA,IAC9D,oBAAoB,CAAC,6CAA6C;AAAA,IAClE,aAAa,CAAC,wCAAwC;AAAA,IACtD,kBAAkB,CAAC,UAAU;AAAA,IAC7B,WAAW,CAAC,sBAAsB;AAAA,IAClC,iBAAiB,CAAC,0CAA0C;AAAA,IAC5D,oBAAoB,CAAC,8BAA8B;AAAA,IACnD,qBAAqB,CAAC,wCAAwC;AAAA,IAC9D,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,oCAAoC;AAAA,IAC1D,wBAAwB,CAAC,sBAAsB;AAAA,IAC/C,oBAAoB,CAAC,wCAAwC;AAAA,IAC7D,qBAAqB,CAAC,mDAAmD;AAAA,IACzE,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,6CAA6C;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,wBAAwB;AAAA,IAC5C,uCAAuC,CAAC,yBAAyB;AAAA,IACjE,WAAW,CAAC,gCAAgC;AAAA,IAC5C,kBAAkB,CAAC,wCAAwC;AAAA,IAC3D,mCAAmC,CAAC,gCAAgC;AAAA,IACpE,uCAAuC,CAAC,iCAAiC;AAAA,IACzE,8CAA8C;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,uBAAuB,CAAC,0BAA0B;AAAA,IAClD,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,QAAQ,gDAAgD,EAAE;AAAA,IACxE;AAAA,IACA,gDAAgD;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,YAAY,CAAC,uCAAuC;AAAA,IACpD,+BAA+B,CAAC,4BAA4B;AAAA,IAC5D,YAAY,CAAC,6CAA6C;AAAA,IAC1D,qBAAqB,CAAC,oDAAoD;AAAA,IAC1E,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,2BAA2B,CAAC,wBAAwB;AAAA,EACtD;AAAA,EACA,SAAS;AAAA,IACP,4BAA4B,CAAC,0CAA0C;AAAA,IACvE,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,6BAA6B,CAAC,2CAA2C;AAAA,IACzE,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,gBAAgB,CAAC,4BAA4B;AAAA,IAC7C,gBAAgB,CAAC,gDAAgD;AAAA,IACjE,oBAAoB,CAAC,6CAA6C;AAAA,IAClE,kBAAkB,CAAC,2BAA2B;AAAA,IAC9C,gBAAgB,CAAC,+CAA+C;AAAA,EAClE;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ,CAAC,uCAAuC;AAAA,IAChD,aAAa,CAAC,yCAAyC;AAAA,IACvD,KAAK,CAAC,qDAAqD;AAAA,IAC3D,UAAU,CAAC,yDAAyD;AAAA,IACpE,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,IACA,YAAY,CAAC,oDAAoD;AAAA,IACjE,cAAc;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,sDAAsD;AAAA,IACzE,cAAc;AAAA,MACZ;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,uDAAuD;AAAA,EAClE;AAAA,EACA,cAAc;AAAA,IACZ,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA,CAAC;AAAA,MACD,EAAE,mBAAmB,EAAE,UAAU,eAAe,EAAE;AAAA,IACpD;AAAA,IACA,aAAa;AAAA,MACX;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,uDAAuD;AAAA,IACzE,UAAU,CAAC,2DAA2D;AAAA,IACtE,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,sCAAsC;AAAA,IACzD,mBAAmB,CAAC,gDAAgD;AAAA,IACpE,qBAAqB;AAAA,MACnB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,gBAAgB,oBAAoB,EAAE;AAAA,IACpD;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,kDAAkD;AAAA,IACvE,aAAa;AAAA,MACX;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,aAAa,CAAC,iDAAiD;AAAA,EACjE;AAAA,EACA,cAAc;AAAA,IACZ,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,+CAA+C;AAAA,IACrE,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,yBAAyB,CAAC,8CAA8C;AAAA,IACxE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,uCAAuC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,wCAAwC;AAAA,MACtC;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,sBAAsB,CAAC,uBAAuB;AAAA,IAC9C,gBAAgB,CAAC,6BAA6B;AAAA,EAChD;AAAA,EACA,YAAY;AAAA,IACV,4CAA4C;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,uCAAuC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,4BAA4B,CAAC,uBAAuB;AAAA,IACpD,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,0CAA0C;AAAA,MACxC;AAAA,IACF;AAAA,IACA,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,oCAAoC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,4BAA4B,CAAC,0CAA0C;AAAA,IACvE,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,qDAAqD;AAAA,IACvE,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,yBAAyB,CAAC,uCAAuC;AAAA,IACjE,iBAAiB,CAAC,+CAA+C;AAAA,IACjE,cAAc,CAAC,kDAAkD;AAAA,IACjE,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,mDAAmD;AAAA,MACjD;AAAA,IACF;AAAA,IACA,0BAA0B,CAAC,sBAAsB;AAAA,IACjD,oBAAoB;AAAA,MAClB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,mBAAmB,EAAE,QAAQ,MAAM,EAAE;AAAA,IACzC;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,oCAAoC;AAAA,IACrD,iBAAiB,CAAC,8CAA8C;AAAA,IAChE,+CAA+C;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,iCAAiC,CAAC,8BAA8B;AAAA,IAChE,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,uCAAuC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,+CAA+C;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,8CAA8C;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,2BAA2B,CAAC,8CAA8C;AAAA,IAC1E,0BAA0B,CAAC,6CAA6C;AAAA,IACxE,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,4BAA4B,CAAC,yCAAyC;AAAA,EACxE;AAAA,EACA,SAAS;AAAA,IACP,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,+BAA+B,CAAC,iCAAiC;AAAA,IACjE,uBAAuB,CAAC,kDAAkD;AAAA,IAC1E,+BAA+B,CAAC,iCAAiC;AAAA,IACjE,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,uCAAuC;AAAA,EAC5D;AAAA,EACA,aAAa,EAAE,QAAQ,CAAC,0BAA0B,EAAE;AAAA,EACpD,YAAY;AAAA,IACV,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,qDAAqD;AAAA,IACvE,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,UAAU,CAAC,4DAA4D;AAAA,IACvE,iBAAiB,CAAC,+CAA+C;AAAA,IACjE,cAAc,CAAC,kDAAkD;AAAA,IACjE,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,mCAAmC;AAAA,IACtD,mBAAmB,CAAC,6CAA6C;AAAA,IACjE,gBAAgB,CAAC,oCAAoC;AAAA,IACrD,iBAAiB,CAAC,8CAA8C;AAAA,IAChE,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB;AAAA,IACf,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,YAAY,CAAC,iDAAiD;AAAA,EAChE;AAAA,EACA,QAAQ,EAAE,KAAK,CAAC,aAAa,EAAE;AAAA,EAC/B,OAAO;AAAA,IACL,gBAAgB,CAAC,2BAA2B;AAAA,IAC5C,QAAQ,CAAC,aAAa;AAAA,IACtB,eAAe,CAAC,gCAAgC;AAAA,IAChD,QAAQ,CAAC,yBAAyB;AAAA,IAClC,eAAe,CAAC,+CAA+C;AAAA,IAC/D,MAAM,CAAC,6BAA6B;AAAA,IACpC,KAAK,CAAC,sBAAsB;AAAA,IAC5B,YAAY,CAAC,4CAA4C;AAAA,IACzD,aAAa,CAAC,4BAA4B;AAAA,IAC1C,MAAM,CAAC,YAAY;AAAA,IACnB,cAAc,CAAC,+BAA+B;AAAA,IAC9C,aAAa,CAAC,8BAA8B;AAAA,IAC5C,aAAa,CAAC,6BAA6B;AAAA,IAC3C,WAAW,CAAC,4BAA4B;AAAA,IACxC,YAAY,CAAC,mBAAmB;AAAA,IAChC,aAAa,CAAC,oBAAoB;AAAA,IAClC,MAAM,CAAC,2BAA2B;AAAA,IAClC,QAAQ,CAAC,8BAA8B;AAAA,IACvC,QAAQ,CAAC,wBAAwB;AAAA,IACjC,eAAe,CAAC,8CAA8C;AAAA,EAChE;AAAA,EACA,KAAK;AAAA,IACH,YAAY,CAAC,sCAAsC;AAAA,IACnD,cAAc,CAAC,wCAAwC;AAAA,IACvD,WAAW,CAAC,qCAAqC;AAAA,IACjD,WAAW,CAAC,qCAAqC;AAAA,IACjD,YAAY,CAAC,sCAAsC;AAAA,IACnD,WAAW,CAAC,6CAA6C;AAAA,IACzD,SAAS,CAAC,gDAAgD;AAAA,IAC1D,WAAW,CAAC,oDAAoD;AAAA,IAChE,QAAQ,CAAC,yCAAyC;AAAA,IAClD,QAAQ,CAAC,8CAA8C;AAAA,IACvD,SAAS,CAAC,gDAAgD;AAAA,IAC1D,kBAAkB,CAAC,mDAAmD;AAAA,IACtE,WAAW,CAAC,4CAA4C;AAAA,EAC1D;AAAA,EACA,WAAW;AAAA,IACT,iBAAiB,CAAC,0BAA0B;AAAA,IAC5C,aAAa,CAAC,iCAAiC;AAAA,EACjD;AAAA,EACA,eAAe;AAAA,IACb,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,qCAAqC,CAAC,8BAA8B;AAAA,IACpE,uBAAuB,CAAC,oCAAoC;AAAA,IAC5D,wBAAwB,CAAC,8CAA8C;AAAA,IACvE,mCAAmC;AAAA,MACjC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,gBAAgB,qCAAqC,EAAE;AAAA,IACrE;AAAA,IACA,wCAAwC,CAAC,iCAAiC;AAAA,IAC1E,0BAA0B,CAAC,uCAAuC;AAAA,IAClE,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,gBAAgB,wCAAwC,EAAE;AAAA,IACxE;AAAA,IACA,qCAAqC,CAAC,8BAA8B;AAAA,IACpE,uBAAuB,CAAC,oCAAoC;AAAA,IAC5D,wBAAwB,CAAC,8CAA8C;AAAA,IACvE,mCAAmC;AAAA,MACjC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,gBAAgB,qCAAqC,EAAE;AAAA,IACrE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,cAAc;AAAA,MACZ;AAAA,IACF;AAAA,IACA,WAAW,CAAC,yDAAyD;AAAA,IACrE,aAAa;AAAA,MACX;AAAA,IACF;AAAA,IACA,wBAAwB,CAAC,gDAAgD;AAAA,IACzE,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,mCAAmC;AAAA,IAC5C,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,aAAa,CAAC,mCAAmC;AAAA,IACjD,iBAAiB,CAAC,uCAAuC;AAAA,IACzD,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,aAAa,CAAC,4CAA4C;AAAA,IAC1D,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,IACA,KAAK,CAAC,iDAAiD;AAAA,IACvD,YAAY,CAAC,wDAAwD;AAAA,IACrE,UAAU,CAAC,oDAAoD;AAAA,IAC/D,UAAU,CAAC,yCAAyC;AAAA,IACpD,cAAc,CAAC,yDAAyD;AAAA,IACxE,MAAM,CAAC,aAAa;AAAA,IACpB,eAAe,CAAC,qCAAqC;AAAA,IACrD,cAAc,CAAC,0DAA0D;AAAA,IACzE,qBAAqB,CAAC,2CAA2C;AAAA,IACjE,YAAY,CAAC,wDAAwD;AAAA,IACrE,mBAAmB,CAAC,yCAAyC;AAAA,IAC7D,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,0BAA0B,CAAC,kBAAkB;AAAA,IAC7C,YAAY,CAAC,wBAAwB;AAAA,IACrC,aAAa,CAAC,kCAAkC;AAAA,IAChD,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,kCAAkC;AAAA,IACtD,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,sCAAsC;AAAA,IACvD,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,MAAM,CAAC,sDAAsD;AAAA,IAC7D,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,WAAW,CAAC,wDAAwD;AAAA,IACpE,QAAQ,CAAC,yDAAyD;AAAA,IAClE,QAAQ,CAAC,mDAAmD;AAAA,IAC5D,eAAe,CAAC,0DAA0D;AAAA,IAC1E,aAAa,CAAC,2CAA2C;AAAA,IACzD,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,KAAK,CAAC,yBAAyB;AAAA,IAC/B,oBAAoB,CAAC,eAAe;AAAA,IACpC,YAAY,CAAC,mCAAmC;AAAA,EAClD;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,gBAAgB;AAAA,IACzB,WAAW;AAAA,MACT;AAAA,MACA,EAAE,SAAS,EAAE,gBAAgB,4BAA4B,EAAE;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,KAAK,CAAC,WAAW;AAAA,IACjB,gBAAgB,CAAC,eAAe;AAAA,IAChC,YAAY,CAAC,cAAc;AAAA,IAC3B,QAAQ,CAAC,UAAU;AAAA,IACnB,MAAM,CAAC,OAAO;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,+BAA+B,CAAC,qCAAqC;AAAA,IACrE,iBAAiB,CAAC,2CAA2C;AAAA,IAC7D,0BAA0B,CAAC,sBAAsB;AAAA,IACjD,YAAY,CAAC,4BAA4B;AAAA,IACzC,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,wDAAwD;AAAA,IAC1E,kBAAkB;AAAA,MAChB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,cAAc,+BAA+B,EAAE;AAAA,IAC7D;AAAA,IACA,2BAA2B,CAAC,uBAAuB;AAAA,IACnD,aAAa,CAAC,6BAA6B;AAAA,IAC3C,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,wBAAwB;AAAA,MACtB;AAAA,MACA,CAAC;AAAA,MACD;AAAA,QACE,YACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,WAAW,CAAC,mCAAmC;AAAA,IAC/C,kBAAkB,CAAC,gDAAgD;AAAA,IACnE,kBAAkB,CAAC,mCAAmC;AAAA,IACtD,wBAAwB,CAAC,oCAAoC;AAAA,IAC7D,8BAA8B,CAAC,2CAA2C;AAAA,IAC1E,oCAAoC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,8BAA8B;AAAA,IACjD,iBAAiB,CAAC,8BAA8B;AAAA,IAChD,gCAAgC,CAAC,qCAAqC;AAAA,IACtE,8CAA8C;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,eAAe,CAAC,wBAAwB;AAAA,IACxC,QAAQ,CAAC,oBAAoB;AAAA,IAC7B,iBAAiB,CAAC,gDAAgD;AAAA,IAClE,eAAe,CAAC,oCAAoC;AAAA,IACpD,6CAA6C;AAAA,MAC3C;AAAA,MACA,CAAC;AAAA,MACD;AAAA,QACE,YACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA,KAAK,CAAC,iBAAiB;AAAA,IACvB,wBAAwB,CAAC,mCAAmC;AAAA,IAC5D,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,mCAAmC,CAAC,kCAAkC;AAAA,IACtE,sBAAsB,CAAC,wCAAwC;AAAA,IAC/D,YAAY,CAAC,8CAA8C;AAAA,IAC3D,sBAAsB,CAAC,+CAA+C;AAAA,IACtE,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,YAAY,CAAC,iCAAiC;AAAA,IAC9C,wBAAwB,CAAC,wCAAwC;AAAA,IACjE,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,MAAM,CAAC,oBAAoB;AAAA,IAC3B,sBAAsB,CAAC,+BAA+B;AAAA,IACtD,kBAAkB,CAAC,+CAA+C;AAAA,IAClE,kBAAkB,CAAC,wBAAwB;AAAA,IAC3C,oCAAoC,CAAC,mCAAmC;AAAA,IACxE,uBAAuB,CAAC,oCAAoC;AAAA,IAC5D,0BAA0B,CAAC,gBAAgB;AAAA,IAC3C,aAAa,CAAC,4BAA4B;AAAA,IAC1C,qBAAqB,CAAC,mDAAmD;AAAA,IACzE,gBAAgB,CAAC,6BAA6B;AAAA,IAC9C,aAAa,CAAC,yBAAyB;AAAA,IACvC,qCAAqC,CAAC,4BAA4B;AAAA,IAClE,kBAAkB,CAAC,oDAAoD;AAAA,IACvE,kBAAkB,CAAC,oDAAoD;AAAA,IACvE,cAAc,CAAC,oCAAoC;AAAA,IACnD,wCAAwC;AAAA,MACtC;AAAA,IACF;AAAA,IACA,0BAA0B,CAAC,uCAAuC;AAAA,IAClE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,sBAAsB,CAAC,gDAAgD;AAAA,IACvE,eAAe,CAAC,wCAAwC;AAAA,IACxD,wBAAwB,CAAC,6BAA6B;AAAA,IACtD,mBAAmB,CAAC,gCAAgC;AAAA,IACpD,0BAA0B;AAAA,MACxB;AAAA,MACA,CAAC;AAAA,MACD;AAAA,QACE,YACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA,uBAAuB,CAAC,4CAA4C;AAAA,IACpE,cAAc,CAAC,uBAAuB;AAAA,IACtC,aAAa,CAAC,wCAAwC;AAAA,IACtD,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,uCAAuC;AAAA,IACtD,yBAAyB,CAAC,2CAA2C;AAAA,IACrE,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,4CAA4C;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,MACA,CAAC;AAAA,MACD;AAAA,QACE,YACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,sBAAsB,CAAC,wCAAwC;AAAA,IAC/D,yCAAyC;AAAA,MACvC;AAAA,IACF;AAAA,IACA,aAAa,CAAC,sCAAsC;AAAA,IACpD,QAAQ,CAAC,mBAAmB;AAAA,IAC5B,iBAAiB,CAAC,6CAA6C;AAAA,IAC/D,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,kDAAkD;AAAA,IACpE,mBAAmB,CAAC,yCAAyC;AAAA,IAC7D,eAAe,CAAC,mCAAmC;AAAA,IACnD,2BAA2B,CAAC,0CAA0C;AAAA,EACxE;AAAA,EACA,UAAU;AAAA,IACR,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,0CAA0C;AAAA,MACxC;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,8CAA8C;AAAA,MAC5C;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,YAAY,2CAA2C,EAAE;AAAA,IACvE;AAAA,IACA,6DAA6D;AAAA,MAC3D;AAAA,MACA,CAAC;AAAA,MACD;AAAA,QACE,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,yDAAyD;AAAA,MACvD;AAAA,IACF;AAAA,IACA,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,4CAA4C;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,uCAAuC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,4DAA4D;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,uDAAuD;AAAA,MACrD;AAAA,IACF;AAAA,IACA,+CAA+C;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,kCAAkC,CAAC,oBAAoB;AAAA,IACvD,6BAA6B,CAAC,0BAA0B;AAAA,IACxD,qBAAqB,CAAC,gCAAgC;AAAA,IACtD,oCAAoC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,2CAA2C;AAAA,MACzC;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB;AAAA,IACjB,0BAA0B,CAAC,qCAAqC;AAAA,IAChE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,uBAAuB,CAAC,kDAAkD;AAAA,IAC1E,iBAAiB,CAAC,+CAA+C;AAAA,IACjE,0BAA0B,CAAC,oCAAoC;AAAA,IAC/D,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,eAAe,CAAC,qDAAqD;AAAA,IACrE,QAAQ,CAAC,kCAAkC;AAAA,IAC3C,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,wDAAwD;AAAA,IACvE,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb;AAAA,IACF;AAAA,IACA,KAAK,CAAC,+CAA+C;AAAA,IACrD,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,uDAAuD;AAAA,IAC1E,MAAM,CAAC,iCAAiC;AAAA,IACxC,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,aAAa,CAAC,uDAAuD;AAAA,IACrE,WAAW,CAAC,qDAAqD;AAAA,IACjE,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,2BAA2B,CAAC,0CAA0C;AAAA,IACtE,aAAa,CAAC,uDAAuD;AAAA,IACrE,OAAO,CAAC,qDAAqD;AAAA,IAC7D,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,iDAAiD;AAAA,IAC1D,cAAc;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,EAAE,KAAK,CAAC,iBAAiB,EAAE;AAAA,EACtC,WAAW;AAAA,IACT,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,2DAA2D;AAAA,IAC1E,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,kBAAkB;AAAA,MAChB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,sCAAsC,EAAE;AAAA,IAC/D;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,OAAO;AAAA,IACtB;AAAA,IACA,iBAAiB,CAAC,oDAAoD;AAAA,IACtE,wBAAwB;AAAA,MACtB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,WAAW;AAAA,IAC1B;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,QAAQ;AAAA,IACvB;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,QAAQ;AAAA,IACvB;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,oDAAoD;AAAA,IACxE,oCAAoC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,6CAA6C;AAAA,IAChE,gBAAgB,CAAC,mDAAmD;AAAA,IACpE,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,yCAAyC;AAAA,IAC7D,gBAAgB,CAAC,sCAAsC;AAAA,IACvD,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,2CAA2C;AAAA,IAChE,iBAAiB,CAAC,iCAAiC;AAAA,IACnD,kBAAkB,CAAC,wCAAwC;AAAA,IAC3D,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,uCAAuC;AAAA,IAC7D,4BAA4B,CAAC,kBAAkB;AAAA,IAC/C,YAAY,CAAC,kCAAkC;AAAA,IAC/C,aAAa,CAAC,wBAAwB;AAAA,IACtC,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,4BAA4B,CAAC,2CAA2C;AAAA,IACxE,kBAAkB,CAAC,2BAA2B;AAAA,IAC9C,uBAAuB,CAAC,8CAA8C;AAAA,IACtE,iBAAiB,CAAC,kCAAkC;AAAA,IACpD,eAAe,CAAC,qCAAqC;AAAA,IACrD,mBAAmB,CAAC,qCAAqC;AAAA,IACzD,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe,CAAC,kCAAkC;AAAA,IAClD,mBAAmB;AAAA,MACjB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,uCAAuC,EAAE;AAAA,IAChE;AAAA,IACA,uCAAuC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,8BAA8B;AAAA,IACvC,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,sDAAsD;AAAA,IACvE,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,oDAAoD;AAAA,IAC1E,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,4CAA4C;AAAA,IAC9D,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,YAAY,CAAC,8CAA8C;AAAA,IAC3D,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,0CAA0C;AAAA,IAC7D,iBAAiB,CAAC,oCAAoC;AAAA,IACtD,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,eAAe,CAAC,oDAAoD;AAAA,IACpE,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,oDAAoD;AAAA,IACxE,eAAe,CAAC,8CAA8C;AAAA,IAC9D,+BAA+B;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,wBAAwB,EAAE;AAAA,IACjD;AAAA,IACA,wBAAwB,CAAC,yCAAyC;AAAA,IAClE,wBAAwB,CAAC,yCAAyC;AAAA,IAClE,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,KAAK,CAAC,2BAA2B;AAAA,IACjC,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,wCAAwC;AAAA,IAC7D,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kCAAkC;AAAA,IACjD,oCAAoC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,aAAa,CAAC,mDAAmD;AAAA,IACjE,WAAW,CAAC,6CAA6C;AAAA,IACzD,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,mDAAmD;AAAA,IACpE,WAAW,CAAC,0CAA0C;AAAA,IACtD,uBAAuB,CAAC,gDAAgD;AAAA,IACxE,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,yBAAyB,CAAC,gDAAgD;AAAA,IAC1E,WAAW,CAAC,yCAAyC;AAAA,IACrD,wBAAwB,CAAC,iDAAiD;AAAA,IAC1E,kBAAkB,CAAC,iDAAiD;AAAA,IACpE,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,4BAA4B,CAAC,6CAA6C;AAAA,IAC1E,YAAY,CAAC,2CAA2C;AAAA,IACxD,sBAAsB,CAAC,8CAA8C;AAAA,IACrE,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,2BAA2B,CAAC,6CAA6C;AAAA,IACzE,cAAc,CAAC,yCAAyC;AAAA,IACxD,eAAe,CAAC,uDAAuD;AAAA,IACvE,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,+CAA+C;AAAA,IACrE,kBAAkB,CAAC,2CAA2C;AAAA,IAC9D,iBAAiB,CAAC,sDAAsD;AAAA,IACxE,kBAAkB,CAAC,sCAAsC;AAAA,IACzD,eAAe,CAAC,uCAAuC;AAAA,IACvD,gBAAgB,CAAC,0BAA0B;AAAA,IAC3C,UAAU,CAAC,iCAAiC;AAAA,IAC5C,eAAe,CAAC,mDAAmD;AAAA,IACnE,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,wCAAwC;AAAA,IAC9D,uBAAuB,CAAC,+CAA+C;AAAA,IACvE,gCAAgC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,4CAA4C;AAAA,IAChE,WAAW,CAAC,kCAAkC;AAAA,IAC9C,sBAAsB,CAAC,wCAAwC;AAAA,IAC/D,YAAY,CAAC,iDAAiD;AAAA,IAC9D,iBAAiB,CAAC,sDAAsD;AAAA,IACxE,iBAAiB,CAAC,+CAA+C;AAAA,IACjE,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,gDAAgD;AAAA,IACpE,gBAAgB,CAAC,iDAAiD;AAAA,IAClE,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,oCAAoC;AAAA,IACtD,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,aAAa,CAAC,iDAAiD;AAAA,IAC/D,iBAAiB,CAAC,qDAAqD;AAAA,IACvE,qCAAqC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,UAAU,CAAC,yCAAyC;AAAA,IACpD,YAAY,CAAC,2CAA2C;AAAA,IACxD,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,oCAAoC;AAAA,IACrD,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,eAAe,CAAC,qCAAqC;AAAA,IACrD,cAAc,CAAC,oCAAoC;AAAA,IACnD,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,yCAAyC;AAAA,IAC7D,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,2BAA2B,CAAC,oCAAoC;AAAA,IAChE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,aAAa,CAAC,mCAAmC;AAAA,IACjD,kBAAkB,CAAC,wCAAwC;AAAA,IAC3D,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,gCAAgC;AAAA,IACjD,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,uCAAuC;AAAA,IACzD,0BAA0B,CAAC,iBAAiB;AAAA,IAC5C,YAAY,CAAC,uBAAuB;AAAA,IACpC,aAAa,CAAC,6BAA6B;AAAA,IAC3C,WAAW,CAAC,iCAAiC;AAAA,IAC7C,iBAAiB,CAAC,uCAAuC;AAAA,IACzD,qCAAqC,CAAC,kCAAkC;AAAA,IACxE,eAAe,CAAC,qCAAqC;AAAA,IACrD,iBAAiB,CAAC,wCAAwC;AAAA,IAC1D,YAAY,CAAC,mBAAmB;AAAA,IAChC,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,oCAAoC;AAAA,IACnD,UAAU,CAAC,gCAAgC;AAAA,IAC3C,WAAW,CAAC,iCAAiC;AAAA,IAC7C,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iCAAiC;AAAA,IAChD,OAAO,CAAC,mCAAmC;AAAA,IAC3C,eAAe,CAAC,2CAA2C;AAAA,IAC3D,aAAa,CAAC,kDAAkD;AAAA,IAChE,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,OAAO;AAAA,IACtB;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,WAAW;AAAA,IAC1B;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,QAAQ;AAAA,IACvB;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,QAAQ;AAAA,IACvB;AAAA,IACA,cAAc,CAAC,qDAAqD;AAAA,IACpE,kBAAkB,CAAC,kCAAkC;AAAA,IACrD,mBAAmB,CAAC,yCAAyC;AAAA,IAC7D,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,OAAO;AAAA,IACtB;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,WAAW;AAAA,IAC1B;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,QAAQ;AAAA,IACvB;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,WAAW,QAAQ;AAAA,IACvB;AAAA,IACA,iBAAiB,CAAC,kDAAkD;AAAA,IACpE,UAAU,CAAC,qCAAqC;AAAA,IAChD,QAAQ,CAAC,6BAA6B;AAAA,IACtC,wBAAwB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,qBAAqB,CAAC,mDAAmD;AAAA,IACzE,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,iCAAiC,CAAC,iCAAiC;AAAA,IACnE,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,uCAAuC;AAAA,IAC1D,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,eAAe,CAAC,mDAAmD;AAAA,IACnE,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,iDAAiD;AAAA,IACrE,4BAA4B;AAAA,MAC1B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,6BAA6B,EAAE;AAAA,IACtD;AAAA,IACA,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,eAAe,CAAC,6CAA6C;AAAA,IAC7D,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,MACA,EAAE,SAAS,6BAA6B;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,kBAAkB;AAAA,IACzB,SAAS,CAAC,qBAAqB;AAAA,IAC/B,uBAAuB;AAAA,MACrB;AAAA,MACA,CAAC;AAAA,MACD;AAAA,QACE,YACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,oBAAoB;AAAA,IAC7B,OAAO,CAAC,0BAA0B;AAAA,IAClC,QAAQ,CAAC,oBAAoB;AAAA,IAC7B,OAAO,CAAC,mBAAmB;AAAA,EAC7B;AAAA,EACA,gBAAgB;AAAA,IACd,4BAA4B;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,wDAAwD;AAAA,IACzE,yBAAyB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,wCAAwC;AAAA,IAC3D,mBAAmB,CAAC,kDAAkD;AAAA,IACtE,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,IACF;AAAA,IACA,kCAAkC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,IACA,oCAAoC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,2BAA2B;AAAA,IAC/C,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,sBAAsB,CAAC,iBAAiB;AAAA,IACxC,6BAA6B,CAAC,qCAAqC;AAAA,IACnE,0BAA0B,CAAC,+CAA+C;AAAA,IAC1E,0BAA0B;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,mCAAmC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,iCAAiC;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,wBAAwB;AAAA,IACjC,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,uBAAuB,CAAC,gDAAgD;AAAA,IACxE,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,aAAa,CAAC,sCAAsC;AAAA,IACpD,WAAW,CAAC,mCAAmC;AAAA,IAC/C,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,IACF;AAAA,IACA,MAAM,CAAC,uBAAuB;AAAA,IAC9B,gBAAgB,CAAC,yCAAyC;AAAA,IAC1D,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,sBAAsB,CAAC,+CAA+C;AAAA,IACtE,0BAA0B,CAAC,iBAAiB;AAAA,IAC5C,kBAAkB,CAAC,2CAA2C;AAAA,IAC9D,6BAA6B;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC,yCAAyC;AAAA,IAC1D,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,aAAa,CAAC,qCAAqC;AAAA,EACrD;AAAA,EACA,OAAO;AAAA,IACL,0BAA0B;AAAA,MACxB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,8BAA8B,EAAE;AAAA,IACvD;AAAA,IACA,8BAA8B,CAAC,mBAAmB;AAAA,IAClD,sCAAsC,CAAC,4BAA4B;AAAA,IACnE,OAAO,CAAC,6BAA6B;AAAA,IACrC,cAAc,CAAC,6BAA6B;AAAA,IAC5C,uBAAuB,CAAC,+CAA+C;AAAA,IACvE,sCAAsC,CAAC,gCAAgC;AAAA,IACvE,8BAA8B;AAAA,MAC5B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,kCAAkC,EAAE;AAAA,IAC3D;AAAA,IACA,kCAAkC,CAAC,qBAAqB;AAAA,IACxD,oCAAoC;AAAA,MAClC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,wCAAwC,EAAE;AAAA,IACjE;AAAA,IACA,wCAAwC,CAAC,iBAAiB;AAAA,IAC1D,yCAAyC,CAAC,6BAA6B;AAAA,IACvE,6BAA6B;AAAA,MAC3B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,iCAAiC,EAAE;AAAA,IAC1D;AAAA,IACA,iCAAiC,CAAC,qBAAqB;AAAA,IACvD,8BAA8B;AAAA,MAC5B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,kCAAkC,EAAE;AAAA,IAC3D;AAAA,IACA,kCAAkC,CAAC,oCAAoC;AAAA,IACvE,oCAAoC;AAAA,MAClC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,wCAAwC,EAAE;AAAA,IACjE;AAAA,IACA,wCAAwC,CAAC,4BAA4B;AAAA,IACrE,yCAAyC,CAAC,8BAA8B;AAAA,IACxE,yCAAyC;AAAA,MACvC;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,gCAAgC;AAAA,IACzC,kBAAkB,CAAC,WAAW;AAAA,IAC9B,SAAS,CAAC,wBAAwB;AAAA,IAClC,eAAe,CAAC,uBAAuB;AAAA,IACvC,mBAAmB,CAAC,iCAAiC;AAAA,IACrD,2BAA2B;AAAA,MACzB;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,+BAA+B,EAAE;AAAA,IACxD;AAAA,IACA,+BAA+B,CAAC,iCAAiC;AAAA,IACjE,iCAAiC;AAAA,MAC/B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,qCAAqC,EAAE;AAAA,IAC9D;AAAA,IACA,qCAAqC,CAAC,yBAAyB;AAAA,IAC/D,sCAAsC;AAAA,MACpC;AAAA,IACF;AAAA,IACA,MAAM,CAAC,YAAY;AAAA,IACnB,kBAAkB,CAAC,qDAAqD;AAAA,IACxE,4BAA4B;AAAA,MAC1B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,gCAAgC,EAAE;AAAA,IACzD;AAAA,IACA,gCAAgC,CAAC,kBAAkB;AAAA,IACnD,4BAA4B;AAAA,MAC1B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,gCAAgC,EAAE;AAAA,IACzD;AAAA,IACA,gCAAgC,CAAC,kBAAkB;AAAA,IACnD,6BAA6B;AAAA,MAC3B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,iCAAiC,EAAE;AAAA,IAC1D;AAAA,IACA,iCAAiC,CAAC,qBAAqB;AAAA,IACvD,mCAAmC,CAAC,qBAAqB;AAAA,IACzD,sBAAsB,CAAC,iCAAiC;AAAA,IACxD,sBAAsB,CAAC,iCAAiC;AAAA,IACxD,6BAA6B;AAAA,MAC3B;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,iCAAiC,EAAE;AAAA,IAC1D;AAAA,IACA,iCAAiC,CAAC,oBAAoB;AAAA,IACtD,oBAAoB,CAAC,gCAAgC;AAAA,IACrD,kCAAkC;AAAA,MAChC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,sCAAsC,EAAE;AAAA,IAC/D;AAAA,IACA,sCAAsC,CAAC,yBAAyB;AAAA,IAChE,uBAAuB,CAAC,4BAA4B;AAAA,IACpD,mCAAmC;AAAA,MACjC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,uCAAuC,EAAE;AAAA,IAChE;AAAA,IACA,uCAAuC,CAAC,gBAAgB;AAAA,IACxD,wCAAwC,CAAC,2BAA2B;AAAA,IACpE,2BAA2B,CAAC,uCAAuC;AAAA,IACnE,wCAAwC,CAAC,4BAA4B;AAAA,IACrE,2BAA2B,CAAC,wCAAwC;AAAA,IACpE,2CAA2C;AAAA,MACzC;AAAA,MACA,CAAC;AAAA,MACD,EAAE,SAAS,CAAC,SAAS,+CAA+C,EAAE;AAAA,IACxE;AAAA,IACA,+CAA+C;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,SAAS,CAAC,gCAAgC;AAAA,IAC1C,UAAU,CAAC,mCAAmC;AAAA,IAC9C,qBAAqB,CAAC,aAAa;AAAA,EACrC;AACF;AAEA,IAAO,oBAAQ;", "names": []}