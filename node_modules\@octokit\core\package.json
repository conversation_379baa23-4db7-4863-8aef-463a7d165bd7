{"name": "@octokit/core", "version": "7.0.3", "publishConfig": {"access": "public", "provenance": true}, "type": "module", "description": "Extendable client for GitHub's REST & GraphQL APIs", "repository": "github:octokit/core.js", "keywords": ["octokit", "github", "api", "sdk", "toolkit"], "author": "<PERSON> (https://github.com/gr2m)", "license": "MIT", "dependencies": {"@octokit/auth-token": "^6.0.0", "@octokit/graphql": "^9.0.1", "@octokit/request": "^10.0.2", "@octokit/request-error": "^7.0.0", "@octokit/types": "^14.0.0", "before-after-hook": "^4.0.0", "universal-user-agent": "^7.0.0"}, "devDependencies": {"@octokit/auth-action": "^6.0.1", "@octokit/auth-app": "^8.0.0", "@octokit/auth-oauth-app": "^9.0.0", "@octokit/tsconfig": "^4.0.0", "@sinonjs/fake-timers": "^14.0.0", "@types/lolex": "^5.1.0", "@types/node": "^22.0.0", "@types/sinonjs__fake-timers": "^8.1.5", "@vitest/coverage-v8": "^3.0.5", "esbuild": "^0.25.0", "fetch-mock": "^12.0.0", "prettier": "3.5.3", "proxy": "^2.0.0", "semantic-release-plugin-update-version-in-files": "^2.0.0", "typescript": "^5.0.0", "undici": "^7.0.0", "vitest": "^3.0.5"}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "./dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-src/index.js", "default": "./dist-src/index.js"}, "./types": {"types": "./dist-types/types.d.ts"}}, "sideEffects": false}