lockfileVersion: 5.4

specifiers:
  get-them-args: 1.3.2
  jest: 28.1.0
  npm-check: ^5.9.2
  shell-exec: 1.0.2
  standard: 17.0.0

dependencies:
  get-them-args: 1.3.2
  shell-exec: 1.0.2

devDependencies:
  jest: 28.1.0
  npm-check: 5.9.2
  standard: 17.0.0

packages:

  /@ampproject/remapping/2.2.0:
    resolution: {integrity: sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.1.1
      '@jridgewell/trace-mapping': 0.3.13
    dev: true

  /@babel/code-frame/7.16.7:
    resolution: {integrity: sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.17.9
    dev: true

  /@babel/compat-data/7.17.10:
    resolution: {integrity: sha512-GZt/TCsG70Ms19gfZO1tM4CVnXsPgEPBCpJu+Qz3L0LUDsY5nZqFZglIoPC1kIYOtNBZlrnFT+klg12vFGZXrw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core/7.17.10:
    resolution: {integrity: sha512-liKoppandF3ZcBnIYFjfSDHZLKdLHGJRkoWtG8zQyGJBQfIYobpnVGI5+pLBNtS6psFLDzyq8+h5HiVljW9PNA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.2.0
      '@babel/code-frame': 7.16.7
      '@babel/generator': 7.17.10
      '@babel/helper-compilation-targets': 7.17.10_@babel+core@7.17.10
      '@babel/helper-module-transforms': 7.17.7
      '@babel/helpers': 7.17.9
      '@babel/parser': 7.17.10
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.10
      '@babel/types': 7.17.10
      convert-source-map: 1.8.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator/7.17.10:
    resolution: {integrity: sha512-46MJZZo9y3o4kmhBVc7zW7i8dtR1oIK/sdO5NcfcZRhTGYi+KKJRtHNgsU6c4VUcJmUNV/LQdebD/9Dlv4K+Tg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.10
      '@jridgewell/gen-mapping': 0.1.1
      jsesc: 2.5.2
    dev: true

  /@babel/helper-compilation-targets/7.17.10_@babel+core@7.17.10:
    resolution: {integrity: sha512-gh3RxjWbauw/dFiU/7whjd0qN9K6nPJMqe6+Er7rOavFh0CQUSwhAE3IcTho2rywPJFxej6TUUHDkWcYI6gGqQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.17.10
      '@babel/core': 7.17.10
      '@babel/helper-validator-option': 7.16.7
      browserslist: 4.20.3
      semver: 6.3.0
    dev: true

  /@babel/helper-environment-visitor/7.16.7:
    resolution: {integrity: sha512-SLLb0AAn6PkUeAfKJCCOl9e1R53pQlGAfc4y4XuMRZfqeMYLE0dM1LMhqbGAlGQY0lfw5/ohoYWAe9V1yibRag==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@babel/helper-function-name/7.17.9:
    resolution: {integrity: sha512-7cRisGlVtiVqZ0MW0/yFB4atgpGLWEHUVYnb448hZK4x+vih0YO5UoS11XIYtZYqHd0dIPMdUSv8q5K4LdMnIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.16.7
      '@babel/types': 7.17.10
    dev: true

  /@babel/helper-hoist-variables/7.16.7:
    resolution: {integrity: sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@babel/helper-module-imports/7.16.7:
    resolution: {integrity: sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@babel/helper-module-transforms/7.17.7:
    resolution: {integrity: sha512-VmZD99F3gNTYB7fJRDTi+u6l/zxY0BE6OIxPSU7a50s6ZUQkHwSDmV92FfM+oCG0pZRVojGYhkR8I0OGeCVREw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.16.7
      '@babel/helper-module-imports': 7.16.7
      '@babel/helper-simple-access': 7.17.7
      '@babel/helper-split-export-declaration': 7.16.7
      '@babel/helper-validator-identifier': 7.16.7
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.10
      '@babel/types': 7.17.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-plugin-utils/7.16.7:
    resolution: {integrity: sha512-Qg3Nk7ZxpgMrsox6HreY1ZNKdBq7K72tDSliA6dCl5f007jR4ne8iD5UzuNnCJH2xBf2BEEVGr+/OL6Gdp7RxA==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-simple-access/7.17.7:
    resolution: {integrity: sha512-txyMCGroZ96i+Pxr3Je3lzEJjqwaRC9buMUgtomcrLe5Nd0+fk1h0LLA+ixUF5OW7AhHuQ7Es1WcQJZmZsz2XA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@babel/helper-split-export-declaration/7.16.7:
    resolution: {integrity: sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@babel/helper-validator-identifier/7.16.7:
    resolution: {integrity: sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-option/7.16.7:
    resolution: {integrity: sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helpers/7.17.9:
    resolution: {integrity: sha512-cPCt915ShDWUEzEp3+UNRktO2n6v49l5RSnG9M5pS24hA+2FAc5si+Pn1i4VVbQQ+jh+bIZhPFQOJOzbrOYY1Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.10
      '@babel/types': 7.17.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/highlight/7.17.9:
    resolution: {integrity: sha512-J9PfEKCbFIv2X5bjTMiZu6Vf341N05QIY+d6FvVKynkG1S7G0j3I0QoRtWIrXhZ+/Nlb5Q0MzqL7TokEJ5BNHg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.16.7
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/parser/7.17.10:
    resolution: {integrity: sha512-n2Q6i+fnJqzOaq2VkdXxy2TCPCWQZHiCo0XqmrCvDWcZQKRyZzYi4Z0yxlBuN0w+r2ZHmre+Q087DSrw3pbJDQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@babel/plugin-syntax-async-generators/7.8.4_@babel+core@7.17.10:
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-bigint/7.8.3_@babel+core@7.17.10:
    resolution: {integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-class-properties/7.12.13_@babel+core@7.17.10:
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-import-meta/7.10.4_@babel+core@7.17.10:
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-json-strings/7.8.3_@babel+core@7.17.10:
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-logical-assignment-operators/7.10.4_@babel+core@7.17.10:
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.17.10:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-numeric-separator/7.10.4_@babel+core@7.17.10:
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.17.10:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-optional-catch-binding/7.8.3_@babel+core@7.17.10:
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.17.10:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-top-level-await/7.14.5_@babel+core@7.17.10:
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/plugin-syntax-typescript/7.17.10_@babel+core@7.17.10:
    resolution: {integrity: sha512-xJefea1DWXW09pW4Tm9bjwVlPDyYA2it3fWlmEjpYz6alPvTUjL0EOzNzI/FEOyI3r4/J7uVH5UqKgl1TQ5hqQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/helper-plugin-utils': 7.16.7
    dev: true

  /@babel/template/7.16.7:
    resolution: {integrity: sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.16.7
      '@babel/parser': 7.17.10
      '@babel/types': 7.17.10
    dev: true

  /@babel/traverse/7.17.10:
    resolution: {integrity: sha512-VmbrTHQteIdUUQNTb+zE12SHS/xQVIShmBPhlNP12hD5poF2pbITW1Z4172d03HegaQWhLffdkRJYtAzp0AGcw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.16.7
      '@babel/generator': 7.17.10
      '@babel/helper-environment-visitor': 7.16.7
      '@babel/helper-function-name': 7.17.9
      '@babel/helper-hoist-variables': 7.16.7
      '@babel/helper-split-export-declaration': 7.16.7
      '@babel/parser': 7.17.10
      '@babel/types': 7.17.10
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/types/7.17.10:
    resolution: {integrity: sha512-9O26jG0mBYfGkUYCYZRnBwbVLd1UZOICEr2Em6InB6jVfsAv1GKgwXHmrSg+WFWDmeKTA6vyTZiN8tCSM5Oo3A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.16.7
      to-fast-properties: 2.0.0
    dev: true

  /@bcoe/v8-coverage/0.2.3:
    resolution: {integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==}
    dev: true

  /@eslint/eslintrc/1.2.3:
    resolution: {integrity: sha512-uGo44hIwoLGNyduRpjdEpovcbMdd+Nv7amtmJxnKmI8xj6yd5LncmSwDa5NgX/41lIFJtkjD6YdVfgEzPfJ5UA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.3.4
      espree: 9.3.2
      globals: 13.15.0
      ignore: 5.2.0
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/config-array/0.9.5:
    resolution: {integrity: sha512-ObyMyWxZiCu/yTisA7uzx81s40xR2fD5Cg/2Kq7G02ajkNubJf6BopgDTmDyc3U7sXpNKM8cYOw7s7Tyr+DnCw==}
    engines: {node: '>=10.10.0'}
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.3.4
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/object-schema/1.2.1:
    resolution: {integrity: sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==}
    dev: true

  /@istanbuljs/load-nyc-config/1.1.0:
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0
    dev: true

  /@istanbuljs/schema/0.1.3:
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}
    dev: true

  /@jest/console/28.1.0:
    resolution: {integrity: sha512-tscn3dlJFGay47kb4qVruQg/XWlmvU0xp3EJOjzzY+sBaI+YgwKcvAmTcyYU7xEiLLIY5HCdWRooAL8dqkFlDA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      chalk: 4.1.2
      jest-message-util: 28.1.0
      jest-util: 28.1.0
      slash: 3.0.0
    dev: true

  /@jest/core/28.1.0:
    resolution: {integrity: sha512-/2PTt0ywhjZ4NwNO4bUqD9IVJfmFVhVKGlhvSpmEfUCuxYf/3NHcKmRFI+I71lYzbTT3wMuYpETDCTHo81gC/g==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/console': 28.1.0
      '@jest/reporters': 28.1.0
      '@jest/test-result': 28.1.0
      '@jest/transform': 28.1.0
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.3.1
      exit: 0.1.2
      graceful-fs: 4.2.10
      jest-changed-files: 28.0.2
      jest-config: 28.1.0_@types+node@17.0.33
      jest-haste-map: 28.1.0
      jest-message-util: 28.1.0
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.0
      jest-resolve-dependencies: 28.1.0
      jest-runner: 28.1.0
      jest-runtime: 28.1.0
      jest-snapshot: 28.1.0
      jest-util: 28.1.0
      jest-validate: 28.1.0
      jest-watcher: 28.1.0
      micromatch: 4.0.5
      pretty-format: 28.1.0
      rimraf: 3.0.2
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - supports-color
      - ts-node
    dev: true

  /@jest/environment/28.1.0:
    resolution: {integrity: sha512-S44WGSxkRngzHslhV6RoAExekfF7Qhwa6R5+IYFa81mpcj0YgdBnRSmvHe3SNwOt64yXaE5GG8Y2xM28ii5ssA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/fake-timers': 28.1.0
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      jest-mock: 28.1.0
    dev: true

  /@jest/expect-utils/28.1.0:
    resolution: {integrity: sha512-5BrG48dpC0sB80wpeIX5FU6kolDJI4K0n5BM9a5V38MGx0pyRvUBSS0u2aNTdDzmOrCjhOg8pGs6a20ivYkdmw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      jest-get-type: 28.0.2
    dev: true

  /@jest/expect/28.1.0:
    resolution: {integrity: sha512-be9ETznPLaHOmeJqzYNIXv1ADEzENuQonIoobzThOYPuK/6GhrWNIJDVTgBLCrz3Am73PyEU2urQClZp0hLTtA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      expect: 28.1.0
      jest-snapshot: 28.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@jest/fake-timers/28.1.0:
    resolution: {integrity: sha512-Xqsf/6VLeAAq78+GNPzI7FZQRf5cCHj1qgQxCjws9n8rKw8r1UYoeaALwBvyuzOkpU3c1I6emeMySPa96rxtIg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.0
      '@sinonjs/fake-timers': 9.1.2
      '@types/node': 17.0.33
      jest-message-util: 28.1.0
      jest-mock: 28.1.0
      jest-util: 28.1.0
    dev: true

  /@jest/globals/28.1.0:
    resolution: {integrity: sha512-3m7sTg52OTQR6dPhsEQSxAvU+LOBbMivZBwOvKEZ+Rb+GyxVnXi9HKgOTYkx/S99T8yvh17U4tNNJPIEQmtwYw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.0
      '@jest/expect': 28.1.0
      '@jest/types': 28.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@jest/reporters/28.1.0:
    resolution: {integrity: sha512-qxbFfqap/5QlSpIizH9c/bFCDKsQlM4uAKSOvZrP+nIdrjqre3FmKzpTtYyhsaVcOSNK7TTt2kjm+4BJIjysFA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 28.1.0
      '@jest/test-result': 28.1.0
      '@jest/transform': 28.1.0
      '@jest/types': 28.1.0
      '@jridgewell/trace-mapping': 0.3.13
      '@types/node': 17.0.33
      chalk: 4.1.2
      collect-v8-coverage: 1.0.1
      exit: 0.1.2
      glob: 7.2.2
      graceful-fs: 4.2.10
      istanbul-lib-coverage: 3.2.0
      istanbul-lib-instrument: 5.2.0
      istanbul-lib-report: 3.0.0
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.4
      jest-util: 28.1.0
      jest-worker: 28.1.0
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      terminal-link: 2.1.1
      v8-to-istanbul: 9.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@jest/schemas/28.0.2:
    resolution: {integrity: sha512-YVDJZjd4izeTDkij00vHHAymNXQ6WWsdChFRK86qck6Jpr3DCL5W3Is3vslviRlP+bLuMYRLbdp98amMvqudhA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@sinclair/typebox': 0.23.5
    dev: true

  /@jest/source-map/28.0.2:
    resolution: {integrity: sha512-Y9dxC8ZpN3kImkk0LkK5XCEneYMAXlZ8m5bflmSL5vrwyeUpJfentacCUg6fOb8NOpOO7hz2+l37MV77T6BFPw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jridgewell/trace-mapping': 0.3.13
      callsites: 3.1.0
      graceful-fs: 4.2.10
    dev: true

  /@jest/test-result/28.1.0:
    resolution: {integrity: sha512-sBBFIyoPzrZho3N+80P35A5oAkSKlGfsEFfXFWuPGBsW40UAjCkGakZhn4UQK4iQlW2vgCDMRDOob9FGKV8YoQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/console': 28.1.0
      '@jest/types': 28.1.0
      '@types/istanbul-lib-coverage': 2.0.4
      collect-v8-coverage: 1.0.1
    dev: true

  /@jest/test-sequencer/28.1.0:
    resolution: {integrity: sha512-tZCEiVWlWNTs/2iK9yi6o3AlMfbbYgV4uuZInSVdzZ7ftpHZhCMuhvk2HLYhCZzLgPFQ9MnM1YaxMnh3TILFiQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/test-result': 28.1.0
      graceful-fs: 4.2.10
      jest-haste-map: 28.1.0
      slash: 3.0.0
    dev: true

  /@jest/transform/28.1.0:
    resolution: {integrity: sha512-omy2xe5WxlAfqmsTjTPxw+iXRTRnf+NtX0ToG+4S0tABeb4KsKmPUHq5UBuwunHg3tJRwgEQhEp0M/8oiatLEA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/core': 7.17.10
      '@jest/types': 28.1.0
      '@jridgewell/trace-mapping': 0.3.13
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 1.8.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.10
      jest-haste-map: 28.1.0
      jest-regex-util: 28.0.2
      jest-util: 28.1.0
      micromatch: 4.0.5
      pirates: 4.0.5
      slash: 3.0.0
      write-file-atomic: 4.0.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@jest/types/28.1.0:
    resolution: {integrity: sha512-xmEggMPr317MIOjjDoZ4ejCSr9Lpbt/u34+dvc99t7DS8YirW5rwZEhzKPC2BMUFkUhI48qs6qLUSGw5FuL0GA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/schemas': 28.0.2
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-reports': 3.0.1
      '@types/node': 17.0.33
      '@types/yargs': 17.0.10
      chalk: 4.1.2
    dev: true

  /@jridgewell/gen-mapping/0.1.1:
    resolution: {integrity: sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.1
      '@jridgewell/sourcemap-codec': 1.4.13
    dev: true

  /@jridgewell/resolve-uri/3.0.7:
    resolution: {integrity: sha512-8cXDaBBHOr2pQ7j77Y6Vp5VDT2sIqWyWQ56TjEq4ih/a4iST3dItRe8Q9fp0rrIl9DoKhWQtUQz/YpOxLkXbNA==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/set-array/1.1.1:
    resolution: {integrity: sha512-Ct5MqZkLGEXTVmQYbGtx9SVqD2fqwvdubdps5D3djjAkgkKwT918VNOz65pEHFaYTeWcukmJmH5SwsA9Tn2ObQ==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/sourcemap-codec/1.4.13:
    resolution: {integrity: sha512-GryiOJmNcWbovBxTfZSF71V/mXbgcV3MewDe3kIMCLyIh5e7SKAeUZs+rMnJ8jkMolZ/4/VsdBmMrw3l+VdZ3w==}
    dev: true

  /@jridgewell/trace-mapping/0.3.13:
    resolution: {integrity: sha512-o1xbKhp9qnIAoHJSWd6KlCZfqslL4valSF81H8ImioOAxluWYWOpWkpyktY2vnt4tbrX9XYaxovq6cgowaJp2w==}
    dependencies:
      '@jridgewell/resolve-uri': 3.0.7
      '@jridgewell/sourcemap-codec': 1.4.13
    dev: true

  /@sinclair/typebox/0.23.5:
    resolution: {integrity: sha512-AFBVi/iT4g20DHoujvMH1aEDn8fGJh4xsRGCP6d8RpLPMqsNPvW01Jcn0QysXTsg++/xj25NmJsGyH9xug/wKg==}
    dev: true

  /@sinonjs/commons/1.8.3:
    resolution: {integrity: sha512-xkNcLAn/wZaX14RPlwizcKicDk9G3F8m2nU3L7Ukm5zBgTwiT0wsoFAHx9Jq56fJA1z/7uKGtCRu16sOUCLIHQ==}
    dependencies:
      type-detect: 4.0.8
    dev: true

  /@sinonjs/fake-timers/9.1.2:
    resolution: {integrity: sha512-BPS4ynJW/o92PUR4wgriz2Ud5gpST5vz6GQfMixEDK0Z8ZCUv2M7SkBLykH56T++Xs+8ln9zTGbOvNGIe02/jw==}
    dependencies:
      '@sinonjs/commons': 1.8.3
    dev: true

  /@types/babel__core/7.1.19:
    resolution: {integrity: sha512-WEOTgRsbYkvA/KCsDwVEGkd7WAr1e3g31VHQ8zy5gul/V1qKullU/BU5I68X5v7V3GnB9eotmom4v5a5gjxorw==}
    dependencies:
      '@babel/parser': 7.17.10
      '@babel/types': 7.17.10
      '@types/babel__generator': 7.6.4
      '@types/babel__template': 7.4.1
      '@types/babel__traverse': 7.17.1
    dev: true

  /@types/babel__generator/7.6.4:
    resolution: {integrity: sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==}
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@types/babel__template/7.4.1:
    resolution: {integrity: sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==}
    dependencies:
      '@babel/parser': 7.17.10
      '@babel/types': 7.17.10
    dev: true

  /@types/babel__traverse/7.17.1:
    resolution: {integrity: sha512-kVzjari1s2YVi77D3w1yuvohV2idweYXMCDzqBiVNN63TcDWrIlTVOYpqVrvbbyOE/IyzBoTKF0fdnLPEORFxA==}
    dependencies:
      '@babel/types': 7.17.10
    dev: true

  /@types/graceful-fs/4.1.5:
    resolution: {integrity: sha512-anKkLmZZ+xm4p8JWBf4hElkM4XR+EZeA2M9BAkkTldmcyDY4mbdIJnRghDJH3Ov5ooY7/UAoENtmdMSkaAd7Cw==}
    dependencies:
      '@types/node': 17.0.33
    dev: true

  /@types/istanbul-lib-coverage/2.0.4:
    resolution: {integrity: sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==}
    dev: true

  /@types/istanbul-lib-report/3.0.0:
    resolution: {integrity: sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
    dev: true

  /@types/istanbul-reports/3.0.1:
    resolution: {integrity: sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw==}
    dependencies:
      '@types/istanbul-lib-report': 3.0.0
    dev: true

  /@types/json5/0.0.29:
    resolution: {integrity: sha1-7ihweulOEdK4J7y+UnC86n8+ce4=}
    dev: true

  /@types/keyv/3.1.4:
    resolution: {integrity: sha512-BQ5aZNSCpj7D6K2ksrRCTmKRLEpnPvWDiLPfoGyhZ++8YtiK9d/3DBKPJgry359X/P1PfruyYwvnvwFjuEiEIg==}
    dependencies:
      '@types/node': 17.0.33
    dev: true

  /@types/node/17.0.33:
    resolution: {integrity: sha512-miWq2m2FiQZmaHfdZNcbpp9PuXg34W5JZ5CrJ/BaS70VuhoJENBEQybeiYSaPBRNq6KQGnjfEnc/F3PN++D+XQ==}
    dev: true

  /@types/prettier/2.6.1:
    resolution: {integrity: sha512-XFjFHmaLVifrAKaZ+EKghFHtHSUonyw8P2Qmy2/+osBnrKbH9UYtlK10zg8/kCt47MFilll/DEDKy3DHfJ0URw==}
    dev: true

  /@types/responselike/1.0.0:
    resolution: {integrity: sha512-85Y2BjiufFzaMIlvJDvTTB8Fxl2xfLo4HgmHzVBz08w4wDePCTjYw66PdrolO0kzli3yam/YCgRufyo1DdQVTA==}
    dependencies:
      '@types/node': 17.0.33
    dev: true

  /@types/stack-utils/2.0.1:
    resolution: {integrity: sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw==}
    dev: true

  /@types/yargs-parser/21.0.0:
    resolution: {integrity: sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==}
    dev: true

  /@types/yargs/17.0.10:
    resolution: {integrity: sha512-gmEaFwpj/7f/ROdtIlci1R1VYU1J4j95m8T+Tj3iBgiBFKg1foE/PSl93bBd5T9LDXNPo8UlNN6W0qwD8O5OaA==}
    dependencies:
      '@types/yargs-parser': 21.0.0
    dev: true

  /acorn-jsx/5.3.2_acorn@8.7.1:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.7.1
    dev: true

  /acorn/8.7.1:
    resolution: {integrity: sha512-Xx54uLJQZ19lKygFXOWsscKUbsBZW0CPykPhVQdhIeIwrbPmJzqeASDInc8nKBnp/JT6igTs82qPXz069H8I/A==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: true

  /ansi-align/2.0.0:
    resolution: {integrity: sha1-w2rsy6VjuJzrVW82kPCx2eNUf38=}
    dependencies:
      string-width: 2.1.1
    dev: true

  /ansi-escapes/1.4.0:
    resolution: {integrity: sha1-06ioOzGapneTZisT52HHkRQiMG4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-escapes/4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-regex/2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-regex/3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}
    dev: true

  /ansi-regex/4.1.1:
    resolution: {integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==}
    engines: {node: '>=6'}
    dev: true

  /ansi-regex/5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: true

  /ansi-styles/2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-styles/3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /ansi-styles/5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}
    dev: true

  /anymatch/3.1.2:
    resolution: {integrity: sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: true

  /argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: true

  /argparse/2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: true

  /array-find-index/1.0.2:
    resolution: {integrity: sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-includes/3.1.5:
    resolution: {integrity: sha512-iSDYZMMyTPkiFasVqfuAQnWAYcvO/SeBSCGKePoEthjp4LEMTe4uLc7b025o4jAZpHhihh8xPo99TNWUWWkGDQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
      get-intrinsic: 1.1.1
      is-string: 1.0.7
    dev: true

  /array-union/1.0.2:
    resolution: {integrity: sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-uniq: 1.0.3
    dev: true

  /array-uniq/1.0.3:
    resolution: {integrity: sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array.prototype.flat/1.3.0:
    resolution: {integrity: sha512-12IUEkHsAhA4DY5s0FPgNXIdc8VRSqD9Zp78a5au9abH/SOBrsp082JOWFNTjkMozh8mqcdiKuaLGhPeYztxSw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
      es-shim-unscopables: 1.0.0
    dev: true

  /array.prototype.flatmap/1.3.0:
    resolution: {integrity: sha512-PZC9/8TKAIxcWKdyeb77EzULHPrIX/tIZebLJUQOMR1OwYosT8yggdfWScfTBCDj5utONvOuPQQumYsU2ULbkg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
      es-shim-unscopables: 1.0.0
    dev: true

  /arrify/1.0.1:
    resolution: {integrity: sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /babel-jest/28.1.0_@babel+core@7.17.10:
    resolution: {integrity: sha512-zNKk0yhDZ6QUwfxh9k07GII6siNGMJWVUU49gmFj5gfdqDKLqa2RArXOF2CODp4Dr7dLxN2cvAV+667dGJ4b4w==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0
    dependencies:
      '@babel/core': 7.17.10
      '@jest/transform': 28.1.0
      '@types/babel__core': 7.1.19
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 28.0.2_@babel+core@7.17.10
      chalk: 4.1.2
      graceful-fs: 4.2.10
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-istanbul/6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/helper-plugin-utils': 7.16.7
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.0
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-jest-hoist/28.0.2:
    resolution: {integrity: sha512-Kizhn/ZL+68ZQHxSnHyuvJv8IchXD62KQxV77TBDV/xoBFBOfgRAk97GNs6hXdTTCiVES9nB2I6+7MXXrk5llQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/template': 7.16.7
      '@babel/types': 7.17.10
      '@types/babel__core': 7.1.19
      '@types/babel__traverse': 7.17.1
    dev: true

  /babel-preset-current-node-syntax/1.0.1_@babel+core@7.17.10:
    resolution: {integrity: sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ==}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.17.10
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.17.10
      '@babel/plugin-syntax-bigint': 7.8.3_@babel+core@7.17.10
      '@babel/plugin-syntax-class-properties': 7.12.13_@babel+core@7.17.10
      '@babel/plugin-syntax-import-meta': 7.10.4_@babel+core@7.17.10
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.17.10
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.17.10
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.17.10
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.17.10
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.17.10
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.17.10
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.17.10
      '@babel/plugin-syntax-top-level-await': 7.14.5_@babel+core@7.17.10
    dev: true

  /babel-preset-jest/28.0.2_@babel+core@7.17.10:
    resolution: {integrity: sha512-sYzXIdgIXXroJTFeB3S6sNDWtlJ2dllCdTEsnZ65ACrMojj3hVNFRmnJ1HZtomGi+Be7aqpY/HJ92fr8OhKVkQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.17.10
      babel-plugin-jest-hoist: 28.0.2
      babel-preset-current-node-syntax: 1.0.1_@babel+core@7.17.10
    dev: true

  /babel-runtime/6.26.0:
    resolution: {integrity: sha1-llxwWGaOgrVde/4E/yM3vItWR/4=}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: true

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    dev: true

  /boxen/1.3.0:
    resolution: {integrity: sha512-TNPjfTr432qx7yOjQyaXm3dSR0MH9vXp7eT1BFSl/C51g+EFnOR9hTg1IreahGBmDNCehscshe45f+C1TBZbLw==}
    engines: {node: '>=4'}
    dependencies:
      ansi-align: 2.0.0
      camelcase: 4.1.0
      chalk: 2.4.2
      cli-boxes: 1.0.0
      string-width: 2.1.1
      term-size: 1.2.0
      widest-line: 2.0.1
    dev: true

  /brace-expansion/1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: true

  /braces/3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1
    dev: true

  /browserslist/4.20.3:
    resolution: {integrity: sha512-NBhymBQl1zM0Y5dQT/O+xiLP9/rzOIQdKM/eMJBAq7yBgaB6krIYLGejrwVYnSHZdqjscB1SPuAjHwxjvN6Wdg==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001341
      electron-to-chromium: 1.4.137
      escalade: 3.1.1
      node-releases: 2.0.4
      picocolors: 1.0.0
    dev: true

  /bser/2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}
    dependencies:
      node-int64: 0.4.0
    dev: true

  /buffer-from/1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: true

  /builtin-modules/3.3.0:
    resolution: {integrity: sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==}
    engines: {node: '>=6'}
    dev: true

  /builtins/4.1.0:
    resolution: {integrity: sha512-1bPRZQtmKaO6h7qV1YHXNtr6nCK28k0Zo95KM4dXfILcZZwoHJBN1m3lfLv9LPkcOZlrSr+J1bzMaZFO98Yq0w==}
    dependencies:
      semver: 7.3.7
    dev: true

  /call-bind/1.0.2:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.1.1
    dev: true

  /callsite-record/3.2.2:
    resolution: {integrity: sha1-mgOQZC5D/ou4I5ReUUZPafQWQ94=}
    dependencies:
      callsite: 1.0.0
      chalk: 1.1.3
      error-stack-parser: 1.3.6
      highlight-es: 1.0.3
      lodash: 4.17.21
      pinkie-promise: 2.0.1
    dev: true

  /callsite/1.0.0:
    resolution: {integrity: sha1-KAOY5dZkvXQDi28JBRU+borxvCA=}
    dev: true

  /callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: true

  /camelcase-keys/2.1.0:
    resolution: {integrity: sha1-MIvur/3ygRkFHvodkyITyRuPkuc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      camelcase: 2.1.1
      map-obj: 1.0.1
    dev: true

  /camelcase/2.1.1:
    resolution: {integrity: sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /camelcase/4.1.0:
    resolution: {integrity: sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=}
    engines: {node: '>=4'}
    dev: true

  /camelcase/5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}
    dev: true

  /camelcase/6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}
    dev: true

  /caniuse-lite/1.0.30001341:
    resolution: {integrity: sha512-2SodVrFFtvGENGCv0ChVJIDQ0KPaS1cg7/qtfMaICgeMolDdo/Z2OD32F0Aq9yl6F4YFwGPBS5AaPqNYiW4PoA==}
    dev: true

  /capture-stack-trace/1.0.1:
    resolution: {integrity: sha512-mYQLZnx5Qt1JgB1WEiMCf2647plpGeQ2NMR/5L0HNZzGQo4fuSPnK+wjfPnKZV0aiJDgzmWqqkV/g7JD+DW0qw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /chalk/1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: true

  /chalk/2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /char-regex/1.0.2:
    resolution: {integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==}
    engines: {node: '>=10'}
    dev: true

  /ci-info/1.6.0:
    resolution: {integrity: sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A==}
    dev: true

  /ci-info/3.3.1:
    resolution: {integrity: sha512-SXgeMX9VwDe7iFFaEWkA5AstuER9YKqy4EhHqr4DVqkwmD9rpVimkMKWHdjn30Ja45txyjhSn63lVX69eVCckg==}
    dev: true

  /cjs-module-lexer/1.2.2:
    resolution: {integrity: sha512-cOU9usZw8/dXIXKtwa8pM0OTJQuJkxMN6w30csNRUerHfeQ5R6U3kkU/FtJeIf3M202OHfY2U8ccInBG7/xogA==}
    dev: true

  /cli-boxes/1.0.0:
    resolution: {integrity: sha1-T6kXw+WclKAEzWH47lCdplFocUM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /cli-cursor/1.0.2:
    resolution: {integrity: sha1-ZNo/fValRBLll5S9Ytw1KV6PKYc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      restore-cursor: 1.0.1
    dev: true

  /cli-spinners/0.1.2:
    resolution: {integrity: sha1-u3ZNiOGF+54eaiofGXcjGPYF4xw=}
    engines: {node: '>=0.10.0'}
    dev: true

  /cli-width/2.2.1:
    resolution: {integrity: sha512-GRMWDxpOB6Dgk2E5Uo+3eEBvtOOlimMmpbFiKuLFnQzYDavtLFY3K5ona41jgN/WdRZtG7utuVSVTL4HbZHGkw==}
    dev: true

  /cliui/5.0.0:
    resolution: {integrity: sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==}
    dependencies:
      string-width: 3.1.0
      strip-ansi: 5.2.0
      wrap-ansi: 5.1.0
    dev: true

  /cliui/7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /co/4.6.0:
    resolution: {integrity: sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}
    dev: true

  /code-point-at/1.1.0:
    resolution: {integrity: sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=}
    engines: {node: '>=0.10.0'}
    dev: true

  /collect-v8-coverage/1.0.1:
    resolution: {integrity: sha512-iBPtljfCNcTKNAto0KEtDfZ3qzjJvqE3aTGZsbhjSBlorqpXJlaWWtPO35D+ZImoC3KWejX64o+yPGxhWSTzfg==}
    dev: true

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name/1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}
    dev: true

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    dev: true

  /concat-map/0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}
    dev: true

  /configstore/3.1.5:
    resolution: {integrity: sha512-nlOhI4+fdzoK5xmJ+NY+1gZK56bwEaWZr8fYuXohZ9Vkc1o3a4T/R3M+yE/w7x/ZVJ1zF8c+oaOvF0dztdUgmA==}
    engines: {node: '>=4'}
    dependencies:
      dot-prop: 4.2.1
      graceful-fs: 4.2.10
      make-dir: 1.3.0
      unique-string: 1.0.0
      write-file-atomic: 2.4.3
      xdg-basedir: 3.0.0
    dev: true

  /convert-source-map/1.8.0:
    resolution: {integrity: sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA==}
    dependencies:
      safe-buffer: 5.1.2
    dev: true

  /core-js/2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.4 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true
    dev: true

  /create-error-class/3.0.2:
    resolution: {integrity: sha1-Br56vvlHo/FKMP1hBnHUAbyot7Y=}
    engines: {node: '>=0.10.0'}
    dependencies:
      capture-stack-trace: 1.0.1
    dev: true

  /cross-spawn-async/2.2.5:
    resolution: {integrity: sha1-hF/wwINKPe2dFg2sptOQkGuyiMw=}
    deprecated: cross-spawn no longer requires a build toolchain, use it instead
    dependencies:
      lru-cache: 4.1.5
      which: 1.3.1
    dev: true

  /cross-spawn/5.1.0:
    resolution: {integrity: sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=}
    dependencies:
      lru-cache: 4.1.5
      shebang-command: 1.2.0
      which: 1.3.1
    dev: true

  /cross-spawn/7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /crypto-random-string/1.0.0:
    resolution: {integrity: sha1-ojD2T1aDEOFJgAmUB5DsmVRbyn4=}
    engines: {node: '>=4'}
    dev: true

  /currently-unhandled/0.4.1:
    resolution: {integrity: sha1-mI3zP+qxke95mmE2nddsF635V+o=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-find-index: 1.0.2
    dev: true

  /de-indent/1.0.2:
    resolution: {integrity: sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=}
    dev: true

  /debug/2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: true

  /debug/3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: true

  /debug/4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /decamelize/1.2.0:
    resolution: {integrity: sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /dedent/0.7.0:
    resolution: {integrity: sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=}
    dev: true

  /deep-extend/0.6.0:
    resolution: {integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==}
    engines: {node: '>=4.0.0'}
    dev: true

  /deep-is/0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}
    dev: true

  /deepmerge/4.2.2:
    resolution: {integrity: sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /define-properties/1.1.4:
    resolution: {integrity: sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1
    dev: true

  /depcheck/0.8.3:
    resolution: {integrity: sha512-xcLTnaovCFFTts5Ge7mUUhMGHSu6eRfftvVvOjN7gXO5EFUhJfX6UQa1b08a0SIwKfzG9eKNn5mzZlXp0mZARA==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      '@babel/parser': 7.17.10
      '@babel/traverse': 7.17.10
      builtin-modules: 3.3.0
      deprecate: 1.1.1
      deps-regex: 0.1.4
      js-yaml: 3.14.1
      lodash: 4.17.21
      minimatch: 3.1.2
      node-sass-tilde-importer: 1.0.2
      please-upgrade-node: 3.2.0
      require-package-name: 2.0.1
      resolve: 1.22.0
      vue-template-compiler: 2.6.14
      walkdir: 0.3.2
      yargs: 13.3.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /deprecate/1.1.1:
    resolution: {integrity: sha512-ZGDXefq1xknT292LnorMY5s8UVU08/WKdzDZCUT6t9JzsiMSP4uzUhgpqugffNVcT5WC6wMBiSQ+LFjlv3v7iQ==}
    dev: true

  /deps-regex/0.1.4:
    resolution: {integrity: sha1-UYZnt2kUYKXn4KNBvnbrfOgJAYQ=}
    dev: true

  /detect-newline/3.1.0:
    resolution: {integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==}
    engines: {node: '>=8'}
    dev: true

  /diff-sequences/28.0.2:
    resolution: {integrity: sha512-YtEoNynLDFCRznv/XDalsKGSZDoj0U5kLnXvY0JSq3nBboRrZXjD81+eSiwi+nzcZDwedMmcowcxNwwgFW23mQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dev: true

  /doctrine/2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /doctrine/3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dot-prop/4.2.1:
    resolution: {integrity: sha512-l0p4+mIuJIua0mhxGoh4a+iNL9bmeK5DvnSVQa6T0OhrVmaEa1XScX5Etc673FePCJOArq/4Pa2cLGODUWTPOQ==}
    engines: {node: '>=4'}
    dependencies:
      is-obj: 1.0.1
    dev: true

  /duplexer3/0.1.4:
    resolution: {integrity: sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=}
    dev: true

  /electron-to-chromium/1.4.137:
    resolution: {integrity: sha512-0Rcpald12O11BUogJagX3HsCN3FE83DSqWjgXoHo5a72KUKMSfI39XBgJpgNNxS9fuGzytaFjE06kZkiVFy2qA==}
    dev: true

  /emittery/0.10.2:
    resolution: {integrity: sha512-aITqOwnLanpHLNXZJENbOgjUBeHocD+xsSJmNrjovKBW5HbSpW3d1pEls7GFQPUWXiwG9+0P4GtHfEqC/4M0Iw==}
    engines: {node: '>=12'}
    dev: true

  /emoji-regex/7.0.3:
    resolution: {integrity: sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==}
    dev: true

  /emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: true

  /error-ex/1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1
    dev: true

  /error-stack-parser/1.3.6:
    resolution: {integrity: sha1-4Oc7k+QXE40c18C3RrGkoUhUwpI=}
    dependencies:
      stackframe: 0.3.1
    dev: true

  /es-abstract/1.20.0:
    resolution: {integrity: sha512-URbD8tgRthKD3YcC39vbvSDrX23upXnPcnGAjQfgxXF5ID75YcENawc9ZX/9iTP9ptUyfCLIxTTuMYoRfiOVKA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      es-to-primitive: 1.2.1
      function-bind: 1.1.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.1.1
      get-symbol-description: 1.0.0
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-symbols: 1.0.3
      internal-slot: 1.0.3
      is-callable: 1.2.4
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-weakref: 1.0.2
      object-inspect: 1.12.0
      object-keys: 1.1.1
      object.assign: 4.1.2
      regexp.prototype.flags: 1.4.3
      string.prototype.trimend: 1.0.5
      string.prototype.trimstart: 1.0.5
      unbox-primitive: 1.0.2
    dev: true

  /es-shim-unscopables/1.0.0:
    resolution: {integrity: sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==}
    dependencies:
      has: 1.0.3
    dev: true

  /es-to-primitive/1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.4
      is-date-object: 1.0.5
      is-symbol: 1.0.4
    dev: true

  /escalade/3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}
    dev: true

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}
    dev: true

  /escape-string-regexp/2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}
    dev: true

  /escape-string-regexp/4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: true

  /eslint-config-standard-jsx/11.0.0_jwaecsryikbooohroqzy6hkyri:
    resolution: {integrity: sha512-+1EV/R0JxEK1L0NGolAr8Iktm3Rgotx3BKwgaX+eAuSX8D952LULKtjgZD3F+e6SvibONnhLwoTi9DPxN5LvvQ==}
    peerDependencies:
      eslint: ^8.8.0
      eslint-plugin-react: ^7.28.0
    dependencies:
      eslint: 8.15.0
      eslint-plugin-react: 7.29.4_eslint@8.15.0
    dev: true

  /eslint-config-standard/17.0.0_4b4uawhw2diczlyyjnbxhuzoii:
    resolution: {integrity: sha512-/2ks1GKyqSOkH7JFvXJicu0iMpoojkwB+f5Du/1SC0PtBL+s8v30k9njRZ21pm2drKYm2342jFnGWzttxPmZVg==}
    peerDependencies:
      eslint: ^8.0.1
      eslint-plugin-import: ^2.25.2
      eslint-plugin-n: ^15.0.0
      eslint-plugin-promise: ^6.0.0
    dependencies:
      eslint: 8.15.0
      eslint-plugin-import: 2.26.0_eslint@8.15.0
      eslint-plugin-n: 15.2.0_eslint@8.15.0
      eslint-plugin-promise: 6.0.0_eslint@8.15.0
    dev: true

  /eslint-import-resolver-node/0.3.6:
    resolution: {integrity: sha512-0En0w03NRVMn9Uiyn8YRPDKvWjxCWkslUEhGNTdGx15RvPJYQ+lbOlqrlNI2vEAs4pDYK4f/HN2TbDmk5TP0iw==}
    dependencies:
      debug: 3.2.7
      resolve: 1.22.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-module-utils/2.7.3_ulu2225r2ychl26a37c6o2rfje:
    resolution: {integrity: sha512-088JEC7O3lDZM9xGe0RerkOMd0EjFl+Yvd1jPWIkMT5u3H9+HC34mWWPnqPrN13gieT9pBOO+Qt07Nb/6TresQ==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true
    dependencies:
      debug: 3.2.7
      eslint-import-resolver-node: 0.3.6
      find-up: 2.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-plugin-es/4.1.0_eslint@8.15.0:
    resolution: {integrity: sha512-GILhQTnjYE2WorX5Jyi5i4dz5ALWxBIdQECVQavL6s7cI76IZTDWleTHkxz/QT3kvcs2QlGHvKLYsSlPOlPXnQ==}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=4.19.1'
    dependencies:
      eslint: 8.15.0
      eslint-utils: 2.1.0
      regexpp: 3.2.0
    dev: true

  /eslint-plugin-import/2.26.0_eslint@8.15.0:
    resolution: {integrity: sha512-hYfi3FXaM8WPLf4S1cikh/r4IxnO6zrhZbEGz2b660EJRbuxgpDS5gkCuYgGWg2xxh2rBuIr4Pvhve/7c31koA==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
    dependencies:
      array-includes: 3.1.5
      array.prototype.flat: 1.3.0
      debug: 2.6.9
      doctrine: 2.1.0
      eslint: 8.15.0
      eslint-import-resolver-node: 0.3.6
      eslint-module-utils: 2.7.3_ulu2225r2ychl26a37c6o2rfje
      has: 1.0.3
      is-core-module: 2.9.0
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.values: 1.1.5
      resolve: 1.22.0
      tsconfig-paths: 3.14.1
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: true

  /eslint-plugin-n/15.2.0_eslint@8.15.0:
    resolution: {integrity: sha512-lWLg++jGwC88GDGGBX3CMkk0GIWq0y41aH51lavWApOKcMQcYoL3Ayd0lEdtD3SnQtR+3qBvWQS3qGbR2BxRWg==}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      builtins: 4.1.0
      eslint: 8.15.0
      eslint-plugin-es: 4.1.0_eslint@8.15.0
      eslint-utils: 3.0.0_eslint@8.15.0
      ignore: 5.2.0
      is-core-module: 2.9.0
      minimatch: 3.1.2
      resolve: 1.22.0
      semver: 6.3.0
    dev: true

  /eslint-plugin-promise/6.0.0_eslint@8.15.0:
    resolution: {integrity: sha512-7GPezalm5Bfi/E22PnQxDWH2iW9GTvAlUNTztemeHb6c1BniSyoeTrM87JkC0wYdi6aQrZX9p2qEiAno8aTcbw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
    dependencies:
      eslint: 8.15.0
    dev: true

  /eslint-plugin-react/7.29.4_eslint@8.15.0:
    resolution: {integrity: sha512-CVCXajliVh509PcZYRFyu/BoUEz452+jtQJq2b3Bae4v3xBUWPLCmtmBM+ZinG4MzwmxJgJ2M5rMqhqLVn7MtQ==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8
    dependencies:
      array-includes: 3.1.5
      array.prototype.flatmap: 1.3.0
      doctrine: 2.1.0
      eslint: 8.15.0
      estraverse: 5.3.0
      jsx-ast-utils: 2.4.1
      minimatch: 3.1.2
      object.entries: 1.1.5
      object.fromentries: 2.0.5
      object.hasown: 1.1.1
      object.values: 1.1.5
      prop-types: 15.8.1
      resolve: 2.0.0-next.3
      semver: 6.3.0
      string.prototype.matchall: 4.0.7
    dev: true

  /eslint-scope/7.1.1:
    resolution: {integrity: sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-utils/2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /eslint-utils/3.0.0_eslint@8.15.0:
    resolution: {integrity: sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==}
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'
    dependencies:
      eslint: 8.15.0
      eslint-visitor-keys: 2.1.0
    dev: true

  /eslint-visitor-keys/1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}
    dev: true

  /eslint-visitor-keys/2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==}
    engines: {node: '>=10'}
    dev: true

  /eslint-visitor-keys/3.3.0:
    resolution: {integrity: sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint/8.15.0:
    resolution: {integrity: sha512-GG5USZ1jhCu8HJkzGgeK8/+RGnHaNYZGrGDzUtigK3BsGESW/rs2az23XqE0WVwDxy1VRvvjSSGu5nB0Bu+6SA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true
    dependencies:
      '@eslint/eslintrc': 1.2.3
      '@humanwhocodes/config-array': 0.9.5
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.4
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.1.1
      eslint-utils: 3.0.0_eslint@8.15.0
      eslint-visitor-keys: 3.3.0
      espree: 9.3.2
      esquery: 1.4.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 6.0.2
      globals: 13.15.0
      ignore: 5.2.0
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.1
      regexpp: 3.2.0
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      text-table: 0.2.0
      v8-compile-cache: 2.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree/9.3.2:
    resolution: {integrity: sha512-D211tC7ZwouTIuY5x9XnS0E9sWNChB7IYKX/Xp5eQj3nFXhqmiUDB9q27y76oFl8jTg3pXcQx/bpxMfs3CIZbA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: 8.7.1
      acorn-jsx: 5.3.2_acorn@8.7.1
      eslint-visitor-keys: 3.3.0
    dev: true

  /esprima/4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /esquery/1.4.0:
    resolution: {integrity: sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse/4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse/5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: true

  /esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /execa/0.2.2:
    resolution: {integrity: sha1-4urUcsLDGq1vc/GslW7vReEjIMs=}
    engines: {node: '>=0.12'}
    dependencies:
      cross-spawn-async: 2.2.5
      npm-run-path: 1.0.0
      object-assign: 4.1.1
      path-key: 1.0.0
      strip-eof: 1.0.0
    dev: true

  /execa/0.7.0:
    resolution: {integrity: sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=}
    engines: {node: '>=4'}
    dependencies:
      cross-spawn: 5.1.0
      get-stream: 3.0.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0
    dev: true

  /execa/5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /exit-hook/1.1.1:
    resolution: {integrity: sha1-8FyiM7SMBdVP/wd2XfhQfpXAL/g=}
    engines: {node: '>=0.10.0'}
    dev: true

  /exit/0.1.2:
    resolution: {integrity: sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=}
    engines: {node: '>= 0.8.0'}
    dev: true

  /expand-tilde/2.0.2:
    resolution: {integrity: sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      homedir-polyfill: 1.0.3
    dev: true

  /expect/28.1.0:
    resolution: {integrity: sha512-qFXKl8Pmxk8TBGfaFKRtcQjfXEnKAs+dmlxdwvukJZorwrAabT7M3h8oLOG01I2utEhkmUTi17CHaPBovZsKdw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/expect-utils': 28.1.0
      jest-get-type: 28.0.2
      jest-matcher-utils: 28.1.0
      jest-message-util: 28.1.0
      jest-util: 28.1.0
    dev: true

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: true

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}
    dev: true

  /fb-watchman/2.0.1:
    resolution: {integrity: sha512-DkPJKQeY6kKwmuMretBhr7G6Vodr7bFwDYTXIkfG1gjvNpaxBTQV3PbXg6bR1c1UP4jPOX0jHUbbHANL9vRjVg==}
    dependencies:
      bser: 2.1.1
    dev: true

  /figures/1.7.0:
    resolution: {integrity: sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      escape-string-regexp: 1.0.5
      object-assign: 4.1.1
    dev: true

  /file-entry-cache/6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.0.4
    dev: true

  /fill-range/7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /find-parent-dir/0.3.1:
    resolution: {integrity: sha512-o4UcykWV/XN9wm+jMEtWLPlV8RXCZnMhQI6F6OdHeSez7iiJWePw8ijOlskJZMsaQoGR/b7dH6lO02HhaTN7+A==}
    dev: true

  /find-up/1.1.2:
    resolution: {integrity: sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      path-exists: 2.1.0
      pinkie-promise: 2.0.1
    dev: true

  /find-up/2.1.0:
    resolution: {integrity: sha1-RdG35QbHF93UgndaK3eSCjwMV6c=}
    engines: {node: '>=4'}
    dependencies:
      locate-path: 2.0.0
    dev: true

  /find-up/3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0
    dev: true

  /find-up/4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0
    dev: true

  /flat-cache/3.0.4:
    resolution: {integrity: sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.2.5
      rimraf: 3.0.2
    dev: true

  /flatted/3.2.5:
    resolution: {integrity: sha512-WIWGi2L3DyTUvUrwRKgGi9TwxQMUEqPOPQBVi71R96jZXJdFskXEmf54BoZaS1kknGODoIGASGEzBUYdyMCBJg==}
    dev: true

  /fs.realpath/1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}
    dev: true

  /fsevents/2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /function-bind/1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}
    dev: true

  /function.prototype.name/1.1.5:
    resolution: {integrity: sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
      functions-have-names: 1.2.3
    dev: true

  /functional-red-black-tree/1.0.1:
    resolution: {integrity: sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=}
    dev: true

  /functions-have-names/1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: true

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /get-caller-file/2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: true

  /get-intrinsic/1.1.1:
    resolution: {integrity: sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q==}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-symbols: 1.0.3
    dev: true

  /get-package-type/0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}
    dev: true

  /get-stdin/4.0.1:
    resolution: {integrity: sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /get-stdin/8.0.0:
    resolution: {integrity: sha512-sY22aA6xchAzprjyqmSEQv4UbAAzRN0L2dQB0NlN5acTTK9Don6nhoc3eAbUnpZiCANAMfd/+40kVdKfFygohg==}
    engines: {node: '>=10'}
    dev: true

  /get-stream/3.0.0:
    resolution: {integrity: sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=}
    engines: {node: '>=4'}
    dev: true

  /get-stream/6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}
    dev: true

  /get-symbol-description/1.0.0:
    resolution: {integrity: sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.1
    dev: true

  /get-them-args/1.3.2:
    resolution: {integrity: sha1-dKILqKSr7OWuGZrQPyvMaP38m6U=}
    dev: false

  /giturl/1.0.1:
    resolution: {integrity: sha512-wQourBdI13n8tbjcZTDl6k+ZrCRMU6p9vfp9jknZq+zfWc8xXNztpZFM4XkPHVzHcMSUZxEMYYKZjIGkPlei6Q==}
    engines: {node: '>= 0.10.0'}
    dev: true

  /glob-parent/6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob/6.0.4:
    resolution: {integrity: sha1-DwiGD2oVUSey+t1PnOJLGqtuTSI=}
    dependencies:
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /glob/7.2.2:
    resolution: {integrity: sha512-NzDgHDiJwKYByLrL5lONmQFpK/2G78SMMfo+E9CuGlX4IkvfKDsiQSNPwAYxEy+e6p7ZQ3uslSLlwlJcqezBmQ==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /global-dirs/0.1.1:
    resolution: {integrity: sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=}
    engines: {node: '>=4'}
    dependencies:
      ini: 1.3.8
    dev: true

  /global-modules/1.0.0:
    resolution: {integrity: sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      global-prefix: 1.0.2
      is-windows: 1.0.2
      resolve-dir: 1.0.1
    dev: true

  /global-prefix/1.0.2:
    resolution: {integrity: sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: 2.0.2
      homedir-polyfill: 1.0.3
      ini: 1.3.8
      is-windows: 1.0.2
      which: 1.3.1
    dev: true

  /globals/11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}
    dev: true

  /globals/13.15.0:
    resolution: {integrity: sha512-bpzcOlgDhMG070Av0Vy5Owklpv1I6+j96GhUI7Rh7IzDCKLzboflLrrfqMu8NquDbiR4EOQk7XzJwqVJxicxog==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.20.2
    dev: true

  /globby/4.1.0:
    resolution: {integrity: sha1-CA9UVJ7BuCpsYOYx/ILhIR2+lfg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-union: 1.0.2
      arrify: 1.0.1
      glob: 6.0.4
      object-assign: 4.1.1
      pify: 2.3.0
      pinkie-promise: 2.0.1
    dev: true

  /got/6.7.1:
    resolution: {integrity: sha1-JAzQV4WpoY5WHcG0S0HHY+8ejbA=}
    engines: {node: '>=4'}
    dependencies:
      '@types/keyv': 3.1.4
      '@types/responselike': 1.0.0
      create-error-class: 3.0.2
      duplexer3: 0.1.4
      get-stream: 3.0.0
      is-redirect: 1.0.0
      is-retry-allowed: 1.2.0
      is-stream: 1.1.0
      lowercase-keys: 1.0.1
      safe-buffer: 5.2.1
      timed-out: 4.0.1
      unzip-response: 2.0.1
      url-parse-lax: 1.0.0
    dev: true

  /graceful-fs/4.2.10:
    resolution: {integrity: sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==}
    dev: true

  /has-ansi/2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /has-bigints/1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}
    dev: true

  /has-flag/3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}
    dev: true

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: true

  /has-property-descriptors/1.0.0:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}
    dependencies:
      get-intrinsic: 1.1.1
    dev: true

  /has-symbols/1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}
    dev: true

  /has-tostringtag/1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: true

  /has/1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1
    dev: true

  /he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: true

  /highlight-es/1.0.3:
    resolution: {integrity: sha512-s/SIX6yp/5S1p8aC/NRDC1fwEb+myGIfp8/TzZz0rtAv8fzsdX7vGl3Q1TrXCsczFq8DI3CBFBCySPClfBSdbg==}
    dependencies:
      chalk: 2.4.2
      is-es2016-keyword: 1.0.0
      js-tokens: 3.0.2
    dev: true

  /homedir-polyfill/1.0.3:
    resolution: {integrity: sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      parse-passwd: 1.0.0
    dev: true

  /hosted-git-info/2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}
    dev: true

  /html-escaper/2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}
    dev: true

  /human-signals/2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}
    dev: true

  /ignore/5.2.0:
    resolution: {integrity: sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==}
    engines: {node: '>= 4'}
    dev: true

  /import-fresh/3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /import-lazy/2.1.0:
    resolution: {integrity: sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=}
    engines: {node: '>=4'}
    dev: true

  /import-local/3.1.0:
    resolution: {integrity: sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0
    dev: true

  /imurmurhash/0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}
    dev: true

  /indent-string/2.1.0:
    resolution: {integrity: sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      repeating: 2.0.1
    dev: true

  /inflight/1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2
    dev: true

  /inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}
    dev: true

  /ini/1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: true

  /inquirer/0.12.0:
    resolution: {integrity: sha1-HvK/1jUE3wvHV4X/+MLEHfEvB34=}
    dependencies:
      ansi-escapes: 1.4.0
      ansi-regex: 2.1.1
      chalk: 1.1.3
      cli-cursor: 1.0.2
      cli-width: 2.2.1
      figures: 1.7.0
      lodash: 4.17.21
      readline2: 1.0.1
      run-async: 0.1.0
      rx-lite: 3.1.2
      string-width: 1.0.2
      strip-ansi: 3.0.1
      through: 2.3.8
    dev: true

  /internal-slot/1.0.3:
    resolution: {integrity: sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.1.1
      has: 1.0.3
      side-channel: 1.0.4
    dev: true

  /is-arrayish/0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}
    dev: true

  /is-bigint/1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}
    dependencies:
      has-bigints: 1.0.2
    dev: true

  /is-boolean-object/1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: true

  /is-callable/1.2.4:
    resolution: {integrity: sha512-nsuwtxZfMX67Oryl9LCQ+upnC0Z0BgpwntpS89m1H/TLF0zNfzfLMV/9Wa/6MZsj0acpEjAO0KF1xT6ZdLl95w==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-ci/1.2.1:
    resolution: {integrity: sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg==}
    hasBin: true
    dependencies:
      ci-info: 1.6.0
    dev: true

  /is-core-module/2.9.0:
    resolution: {integrity: sha512-+5FPy5PnwmO3lvfMb0AsoPaBG+5KHUI0wYFXOtYPnVVVspTFUuMZNfNaNVRt3FZadstu2c8x23vykRW/NBoU6A==}
    dependencies:
      has: 1.0.3
    dev: true

  /is-date-object/1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-es2016-keyword/1.0.0:
    resolution: {integrity: sha1-9uVOEQxeT40mXmnS7Q6vjPX0dxg=}
    dev: true

  /is-extglob/2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-finite/1.1.0:
    resolution: {integrity: sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-fullwidth-code-point/1.0.0:
    resolution: {integrity: sha1-754xOG8DGn8NZDr4L95QxFfvAMs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      number-is-nan: 1.0.1
    dev: true

  /is-fullwidth-code-point/2.0.0:
    resolution: {integrity: sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=}
    engines: {node: '>=4'}
    dev: true

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: true

  /is-generator-fn/2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-installed-globally/0.1.0:
    resolution: {integrity: sha1-Df2Y9akRFxbdU13aZJL2e/PSWoA=}
    engines: {node: '>=4'}
    dependencies:
      global-dirs: 0.1.1
      is-path-inside: 1.0.1
    dev: true

  /is-negative-zero/2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-npm/1.0.0:
    resolution: {integrity: sha1-8vtjpl5JBbQGyGBydloaTceTufQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-number-object/1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}
    dev: true

  /is-obj/1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-path-inside/1.0.1:
    resolution: {integrity: sha1-jvW33lBDej/cprToZe96pVy0gDY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      path-is-inside: 1.0.2
    dev: true

  /is-redirect/1.0.0:
    resolution: {integrity: sha1-HQPd7VO9jbDzDCbk+V02/HyH3CQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-regex/1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: true

  /is-retry-allowed/1.2.0:
    resolution: {integrity: sha512-RUbUeKwvm3XG2VYamhJL1xFktgjvPzL0Hq8C+6yrWIswDy3BIXGqCxhxkc30N9jqK311gVU137K8Ei55/zVJRg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-shared-array-buffer/1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}
    dependencies:
      call-bind: 1.0.2
    dev: true

  /is-stream/1.1.0:
    resolution: {integrity: sha1-EtSj3U5o4Lec6428hBc66A2RykQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-stream/2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    dev: true

  /is-string/1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-symbol/1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: true

  /is-utf8/0.2.1:
    resolution: {integrity: sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=}
    dev: true

  /is-weakref/1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.2
    dev: true

  /is-windows/1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /isexe/2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}
    dev: true

  /istanbul-lib-coverage/3.2.0:
    resolution: {integrity: sha512-eOeJ5BHCmHYvQK7xt9GkdHuzuCGS1Y6g9Gvnx3Ym33fz/HpLRYxiS0wHNr+m/MBC8B647Xt608vCDEvhl9c6Mw==}
    engines: {node: '>=8'}
    dev: true

  /istanbul-lib-instrument/5.2.0:
    resolution: {integrity: sha512-6Lthe1hqXHBNsqvgDzGO6l03XNeu3CrG4RqQ1KM9+l5+jNGpEJfIELx1NS3SEHmJQA8np/u+E4EPRKRiu6m19A==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': 7.17.10
      '@babel/parser': 7.17.10
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.0
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /istanbul-lib-report/3.0.0:
    resolution: {integrity: sha512-wcdi+uAKzfiGT2abPpKZ0hSU1rGQjUQnLvtY5MpQ7QCTahD3VODhcu4wcfY1YtkGaDD5yuydOLINXsfbus9ROw==}
    engines: {node: '>=8'}
    dependencies:
      istanbul-lib-coverage: 3.2.0
      make-dir: 3.1.0
      supports-color: 7.2.0
    dev: true

  /istanbul-lib-source-maps/4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}
    dependencies:
      debug: 4.3.4
      istanbul-lib-coverage: 3.2.0
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /istanbul-reports/3.1.4:
    resolution: {integrity: sha512-r1/DshN4KSE7xWEknZLLLLDn5CJybV3nw01VTkp6D5jzLuELlcbudfj/eSQFvrKsJuTVCGnePO7ho82Nw9zzfw==}
    engines: {node: '>=8'}
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.0
    dev: true

  /jest-changed-files/28.0.2:
    resolution: {integrity: sha512-QX9u+5I2s54ZnGoMEjiM2WeBvJR2J7w/8ZUmH2um/WLAuGAYFQcsVXY9+1YL6k0H/AGUdH8pXUAv6erDqEsvIA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      execa: 5.1.1
      throat: 6.0.1
    dev: true

  /jest-circus/28.1.0:
    resolution: {integrity: sha512-rNYfqfLC0L0zQKRKsg4n4J+W1A2fbyGH7Ss/kDIocp9KXD9iaL111glsLu7+Z7FHuZxwzInMDXq+N1ZIBkI/TQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.0
      '@jest/expect': 28.1.0
      '@jest/test-result': 28.1.0
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      chalk: 4.1.2
      co: 4.6.0
      dedent: 0.7.0
      is-generator-fn: 2.1.0
      jest-each: 28.1.0
      jest-matcher-utils: 28.1.0
      jest-message-util: 28.1.0
      jest-runtime: 28.1.0
      jest-snapshot: 28.1.0
      jest-util: 28.1.0
      pretty-format: 28.1.0
      slash: 3.0.0
      stack-utils: 2.0.5
      throat: 6.0.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-cli/28.1.0:
    resolution: {integrity: sha512-fDJRt6WPRriHrBsvvgb93OxgajHHsJbk4jZxiPqmZbMDRcHskfJBBfTyjFko0jjfprP544hOktdSi9HVgl4VUQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/core': 28.1.0
      '@jest/test-result': 28.1.0
      '@jest/types': 28.1.0
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.10
      import-local: 3.1.0
      jest-config: 28.1.0
      jest-util: 28.1.0
      jest-validate: 28.1.0
      prompts: 2.4.2
      yargs: 17.5.0
    transitivePeerDependencies:
      - '@types/node'
      - supports-color
      - ts-node
    dev: true

  /jest-config/28.1.0:
    resolution: {integrity: sha512-aOV80E9LeWrmflp7hfZNn/zGA4QKv/xsn2w8QCBP0t0+YqObuCWTSgNbHJ0j9YsTuCO08ZR/wsvlxqqHX20iUA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true
    dependencies:
      '@babel/core': 7.17.10
      '@jest/test-sequencer': 28.1.0
      '@jest/types': 28.1.0
      babel-jest: 28.1.0_@babel+core@7.17.10
      chalk: 4.1.2
      ci-info: 3.3.1
      deepmerge: 4.2.2
      glob: 7.2.2
      graceful-fs: 4.2.10
      jest-circus: 28.1.0
      jest-environment-node: 28.1.0
      jest-get-type: 28.0.2
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.0
      jest-runner: 28.1.0
      jest-util: 28.1.0
      jest-validate: 28.1.0
      micromatch: 4.0.5
      parse-json: 5.2.0
      pretty-format: 28.1.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-config/28.1.0_@types+node@17.0.33:
    resolution: {integrity: sha512-aOV80E9LeWrmflp7hfZNn/zGA4QKv/xsn2w8QCBP0t0+YqObuCWTSgNbHJ0j9YsTuCO08ZR/wsvlxqqHX20iUA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true
    dependencies:
      '@babel/core': 7.17.10
      '@jest/test-sequencer': 28.1.0
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      babel-jest: 28.1.0_@babel+core@7.17.10
      chalk: 4.1.2
      ci-info: 3.3.1
      deepmerge: 4.2.2
      glob: 7.2.2
      graceful-fs: 4.2.10
      jest-circus: 28.1.0
      jest-environment-node: 28.1.0
      jest-get-type: 28.0.2
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.0
      jest-runner: 28.1.0
      jest-util: 28.1.0
      jest-validate: 28.1.0
      micromatch: 4.0.5
      parse-json: 5.2.0
      pretty-format: 28.1.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-diff/28.1.0:
    resolution: {integrity: sha512-8eFd3U3OkIKRtlasXfiAQfbovgFgRDb0Ngcs2E+FMeBZ4rUezqIaGjuyggJBp+llosQXNEWofk/Sz4Hr5gMUhA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      chalk: 4.1.2
      diff-sequences: 28.0.2
      jest-get-type: 28.0.2
      pretty-format: 28.1.0
    dev: true

  /jest-docblock/28.0.2:
    resolution: {integrity: sha512-FH10WWw5NxLoeSdQlJwu+MTiv60aXV/t8KEwIRGEv74WARE1cXIqh1vGdy2CraHuWOOrnzTWj/azQKqW4fO7xg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      detect-newline: 3.1.0
    dev: true

  /jest-each/28.1.0:
    resolution: {integrity: sha512-a/XX02xF5NTspceMpHujmOexvJ4GftpYXqr6HhhmKmExtMXsyIN/fvanQlt/BcgFoRKN4OCXxLQKth9/n6OPFg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.0
      chalk: 4.1.2
      jest-get-type: 28.0.2
      jest-util: 28.1.0
      pretty-format: 28.1.0
    dev: true

  /jest-environment-node/28.1.0:
    resolution: {integrity: sha512-gBLZNiyrPw9CSMlTXF1yJhaBgWDPVvH0Pq6bOEwGMXaYNzhzhw2kA/OijNF8egbCgDS0/veRv97249x2CX+udQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.0
      '@jest/fake-timers': 28.1.0
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      jest-mock: 28.1.0
      jest-util: 28.1.0
    dev: true

  /jest-get-type/28.0.2:
    resolution: {integrity: sha512-ioj2w9/DxSYHfOm5lJKCdcAmPJzQXmbM/Url3rhlghrPvT3tt+7a/+oXc9azkKmLvoiXjtV83bEWqi+vs5nlPA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dev: true

  /jest-haste-map/28.1.0:
    resolution: {integrity: sha512-xyZ9sXV8PtKi6NCrJlmq53PyNVHzxmcfXNVvIRHpHmh1j/HChC4pwKgyjj7Z9us19JMw8PpQTJsFWOsIfT93Dw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.0
      '@types/graceful-fs': 4.1.5
      '@types/node': 17.0.33
      anymatch: 3.1.2
      fb-watchman: 2.0.1
      graceful-fs: 4.2.10
      jest-regex-util: 28.0.2
      jest-util: 28.1.0
      jest-worker: 28.1.0
      micromatch: 4.0.5
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /jest-leak-detector/28.1.0:
    resolution: {integrity: sha512-uIJDQbxwEL2AMMs2xjhZl2hw8s77c3wrPaQ9v6tXJLGaaQ+4QrNJH5vuw7hA7w/uGT/iJ42a83opAqxGHeyRIA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      jest-get-type: 28.0.2
      pretty-format: 28.1.0
    dev: true

  /jest-matcher-utils/28.1.0:
    resolution: {integrity: sha512-onnax0n2uTLRQFKAjC7TuaxibrPSvZgKTcSCnNUz/tOjJ9UhxNm7ZmPpoQavmTDUjXvUQ8KesWk2/VdrxIFzTQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      chalk: 4.1.2
      jest-diff: 28.1.0
      jest-get-type: 28.0.2
      pretty-format: 28.1.0
    dev: true

  /jest-message-util/28.1.0:
    resolution: {integrity: sha512-RpA8mpaJ/B2HphDMiDlrAZdDytkmwFqgjDZovM21F35lHGeUeCvYmm6W+sbQ0ydaLpg5bFAUuWG1cjqOl8vqrw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/code-frame': 7.16.7
      '@jest/types': 28.1.0
      '@types/stack-utils': 2.0.1
      chalk: 4.1.2
      graceful-fs: 4.2.10
      micromatch: 4.0.5
      pretty-format: 28.1.0
      slash: 3.0.0
      stack-utils: 2.0.5
    dev: true

  /jest-mock/28.1.0:
    resolution: {integrity: sha512-H7BrhggNn77WhdL7O1apG0Q/iwl0Bdd5E1ydhCJzL3oBLh/UYxAwR3EJLsBZ9XA3ZU4PA3UNw4tQjduBTCTmLw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
    dev: true

  /jest-pnp-resolver/1.2.2_jest-resolve@28.1.0:
    resolution: {integrity: sha512-olV41bKSMm8BdnuMsewT4jqlZ8+3TCARAXjZGT9jcoSnrfUnRCqnMoF9XEeoWjbzObpqF9dRhHQj0Xb9QdF6/w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true
    dependencies:
      jest-resolve: 28.1.0
    dev: true

  /jest-regex-util/28.0.2:
    resolution: {integrity: sha512-4s0IgyNIy0y9FK+cjoVYoxamT7Zeo7MhzqRGx7YDYmaQn1wucY9rotiGkBzzcMXTtjrCAP/f7f+E0F7+fxPNdw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dev: true

  /jest-resolve-dependencies/28.1.0:
    resolution: {integrity: sha512-Ue1VYoSZquPwEvng7Uefw8RmZR+me/1kr30H2jMINjGeHgeO/JgrR6wxj2ofkJ7KSAA11W3cOrhNCbj5Dqqd9g==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      jest-regex-util: 28.0.2
      jest-snapshot: 28.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-resolve/28.1.0:
    resolution: {integrity: sha512-vvfN7+tPNnnhDvISuzD1P+CRVP8cK0FHXRwPAcdDaQv4zgvwvag2n55/h5VjYcM5UJG7L4TwE5tZlzcI0X2Lhw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      chalk: 4.1.2
      graceful-fs: 4.2.10
      jest-haste-map: 28.1.0
      jest-pnp-resolver: 1.2.2_jest-resolve@28.1.0
      jest-util: 28.1.0
      jest-validate: 28.1.0
      resolve: 1.22.0
      resolve.exports: 1.1.0
      slash: 3.0.0
    dev: true

  /jest-runner/28.1.0:
    resolution: {integrity: sha512-FBpmuh1HB2dsLklAlRdOxNTTHKFR6G1Qmd80pVDvwbZXTriqjWqjei5DKFC1UlM732KjYcE6yuCdiF0WUCOS2w==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/console': 28.1.0
      '@jest/environment': 28.1.0
      '@jest/test-result': 28.1.0
      '@jest/transform': 28.1.0
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      chalk: 4.1.2
      emittery: 0.10.2
      graceful-fs: 4.2.10
      jest-docblock: 28.0.2
      jest-environment-node: 28.1.0
      jest-haste-map: 28.1.0
      jest-leak-detector: 28.1.0
      jest-message-util: 28.1.0
      jest-resolve: 28.1.0
      jest-runtime: 28.1.0
      jest-util: 28.1.0
      jest-watcher: 28.1.0
      jest-worker: 28.1.0
      source-map-support: 0.5.13
      throat: 6.0.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-runtime/28.1.0:
    resolution: {integrity: sha512-wNYDiwhdH/TV3agaIyVF0lsJ33MhyujOe+lNTUiolqKt8pchy1Hq4+tDMGbtD5P/oNLA3zYrpx73T9dMTOCAcg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.0
      '@jest/fake-timers': 28.1.0
      '@jest/globals': 28.1.0
      '@jest/source-map': 28.0.2
      '@jest/test-result': 28.1.0
      '@jest/transform': 28.1.0
      '@jest/types': 28.1.0
      chalk: 4.1.2
      cjs-module-lexer: 1.2.2
      collect-v8-coverage: 1.0.1
      execa: 5.1.1
      glob: 7.2.2
      graceful-fs: 4.2.10
      jest-haste-map: 28.1.0
      jest-message-util: 28.1.0
      jest-mock: 28.1.0
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.0
      jest-snapshot: 28.1.0
      jest-util: 28.1.0
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-snapshot/28.1.0:
    resolution: {integrity: sha512-ex49M2ZrZsUyQLpLGxQtDbahvgBjlLPgklkqGM0hq/F7W/f8DyqZxVHjdy19QKBm4O93eDp+H5S23EiTbbUmHw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/core': 7.17.10
      '@babel/generator': 7.17.10
      '@babel/plugin-syntax-typescript': 7.17.10_@babel+core@7.17.10
      '@babel/traverse': 7.17.10
      '@babel/types': 7.17.10
      '@jest/expect-utils': 28.1.0
      '@jest/transform': 28.1.0
      '@jest/types': 28.1.0
      '@types/babel__traverse': 7.17.1
      '@types/prettier': 2.6.1
      babel-preset-current-node-syntax: 1.0.1_@babel+core@7.17.10
      chalk: 4.1.2
      expect: 28.1.0
      graceful-fs: 4.2.10
      jest-diff: 28.1.0
      jest-get-type: 28.0.2
      jest-haste-map: 28.1.0
      jest-matcher-utils: 28.1.0
      jest-message-util: 28.1.0
      jest-util: 28.1.0
      natural-compare: 1.4.0
      pretty-format: 28.1.0
      semver: 7.3.7
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-util/28.1.0:
    resolution: {integrity: sha512-qYdCKD77k4Hwkose2YBEqQk7PzUf/NSE+rutzceduFveQREeH6b+89Dc9+wjX9dAwHcgdx4yedGA3FQlU/qCTA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      chalk: 4.1.2
      ci-info: 3.3.1
      graceful-fs: 4.2.10
      picomatch: 2.3.1
    dev: true

  /jest-validate/28.1.0:
    resolution: {integrity: sha512-Lly7CJYih3vQBfjLeANGgBSBJ7pEa18cxpQfQEq2go2xyEzehnHfQTjoUia8xUv4x4J80XKFIDwJJThXtRFQXQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.0
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 28.0.2
      leven: 3.1.0
      pretty-format: 28.1.0
    dev: true

  /jest-watcher/28.1.0:
    resolution: {integrity: sha512-tNHMtfLE8Njcr2IRS+5rXYA4BhU90gAOwI9frTGOqd+jX0P/Au/JfRSNqsf5nUTcWdbVYuLxS1KjnzILSoR5hA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/test-result': 28.1.0
      '@jest/types': 28.1.0
      '@types/node': 17.0.33
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.10.2
      jest-util: 28.1.0
      string-length: 4.0.2
    dev: true

  /jest-worker/28.1.0:
    resolution: {integrity: sha512-ZHwM6mNwaWBR52Snff8ZvsCTqQsvhCxP/bT1I6T6DAnb6ygkshsyLQIMxFwHpYxht0HOoqt23JlC01viI7T03A==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@types/node': 17.0.33
      merge-stream: 2.0.0
      supports-color: 8.1.1
    dev: true

  /jest/28.1.0:
    resolution: {integrity: sha512-TZR+tHxopPhzw3c3560IJXZWLNHgpcz1Zh0w5A65vynLGNcg/5pZ+VildAd7+XGOu6jd58XMY/HNn0IkZIXVXg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/core': 28.1.0
      import-local: 3.1.0
      jest-cli: 28.1.0
    transitivePeerDependencies:
      - '@types/node'
      - supports-color
      - ts-node
    dev: true

  /js-tokens/3.0.2:
    resolution: {integrity: sha1-mGbfOVECEw449/mWvOtlRDIJwls=}
    dev: true

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: true

  /js-yaml/3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: true

  /js-yaml/4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: true

  /jsesc/2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /json-parse-better-errors/1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}
    dev: true

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}
    dev: true

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: true

  /json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}
    dev: true

  /json5/1.0.1:
    resolution: {integrity: sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==}
    hasBin: true
    dependencies:
      minimist: 1.2.6
    dev: true

  /json5/2.2.1:
    resolution: {integrity: sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jsx-ast-utils/2.4.1:
    resolution: {integrity: sha512-z1xSldJ6imESSzOjd3NNkieVJKRlKYSOtMG8SFyCj2FIrvSaSuli/WjpBkEzCBoR9bYYYFgqJw61Xhu7Lcgk+w==}
    engines: {node: '>=4.0'}
    dependencies:
      array-includes: 3.1.5
      object.assign: 4.1.2
    dev: true

  /kleur/3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}
    dev: true

  /latest-version/3.1.0:
    resolution: {integrity: sha1-ogU4P+oyKzO1rjsYq+4NwvNW7hU=}
    engines: {node: '>=4'}
    dependencies:
      package-json: 4.0.1
    dev: true

  /leven/3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}
    dev: true

  /levn/0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}
    dev: true

  /load-json-file/1.1.0:
    resolution: {integrity: sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 2.2.0
      pify: 2.3.0
      pinkie-promise: 2.0.1
      strip-bom: 2.0.0
    dev: true

  /load-json-file/5.3.0:
    resolution: {integrity: sha512-cJGP40Jc/VXUsp8/OrnyKyTZ1y6v/dphm3bioS+RrKXjK2BB6wHUd6JptZEFDGgGahMT+InnZO5i1Ei9mpC8Bw==}
    engines: {node: '>=6'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 4.0.0
      pify: 4.0.1
      strip-bom: 3.0.0
      type-fest: 0.3.1
    dev: true

  /load-yaml-file/0.1.1:
    resolution: {integrity: sha512-G910TofXH7u0NfslAzqv6c9pHpvBzObNOo2hMG0/KUDpdHeFY0wE/fTBMExt0Gb12gg5bXS7Hj6pb0e+xbBXLA==}
    engines: {node: '>=4'}
    dependencies:
      graceful-fs: 4.2.10
      js-yaml: 3.14.1
      pify: 2.3.0
      strip-bom: 3.0.0
    dev: true

  /locate-path/2.0.0:
    resolution: {integrity: sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=}
    engines: {node: '>=4'}
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0
    dev: true

  /locate-path/3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0
    dev: true

  /locate-path/5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0
    dev: true

  /lodash.merge/4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    dev: true

  /lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: true

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: true

  /loud-rejection/1.6.0:
    resolution: {integrity: sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      currently-unhandled: 0.4.1
      signal-exit: 3.0.7
    dev: true

  /lowercase-keys/1.0.1:
    resolution: {integrity: sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /lru-cache/4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2
    dev: true

  /lru-cache/6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /make-dir/1.3.0:
    resolution: {integrity: sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==}
    engines: {node: '>=4'}
    dependencies:
      pify: 3.0.0
    dev: true

  /make-dir/3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.0
    dev: true

  /makeerror/1.0.12:
    resolution: {integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==}
    dependencies:
      tmpl: 1.0.5
    dev: true

  /map-obj/1.0.1:
    resolution: {integrity: sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /meow/3.7.0:
    resolution: {integrity: sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      camelcase-keys: 2.1.0
      decamelize: 1.2.0
      loud-rejection: 1.6.0
      map-obj: 1.0.1
      minimist: 1.2.6
      normalize-package-data: 2.5.0
      object-assign: 4.1.1
      read-pkg-up: 1.0.1
      redent: 1.0.0
      trim-newlines: 1.0.0
    dev: true

  /merge-stream/2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: true

  /micromatch/4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1
    dev: true

  /mimic-fn/2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: true

  /minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11
    dev: true

  /minimist/1.2.6:
    resolution: {integrity: sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q==}
    dev: true

  /ms/2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=}
    dev: true

  /ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: true

  /ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: true

  /mute-stream/0.0.5:
    resolution: {integrity: sha1-j7+rsKmKJT0xhDMfno3rc3L6xsA=}
    dev: true

  /natural-compare/1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}
    dev: true

  /node-emoji/1.11.0:
    resolution: {integrity: sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A==}
    dependencies:
      lodash: 4.17.21
    dev: true

  /node-int64/0.4.0:
    resolution: {integrity: sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=}
    dev: true

  /node-releases/2.0.4:
    resolution: {integrity: sha512-gbMzqQtTtDz/00jQzZ21PQzdI9PyLYqUSvD0p3naOhX4odFji0ZxYdnVwPTxmSwkmxhcFImpozceidSG+AgoPQ==}
    dev: true

  /node-sass-tilde-importer/1.0.2:
    resolution: {integrity: sha512-Swcmr38Y7uB78itQeBm3mThjxBy9/Ah/ykPIaURY/L6Nec9AyRoL/jJ7ECfMR+oZeCTVQNxVMu/aHU+TLRVbdg==}
    dependencies:
      find-parent-dir: 0.3.1
    dev: true

  /normalize-package-data/2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.0
      semver: 5.7.1
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /npm-check/5.9.2:
    resolution: {integrity: sha512-YlTZGP1A8+Rad5wldGil9STYxgZpZl18X6GZI03f4Ch6qTI1TLHIYM0ISco19qgg8M3UHfooEqMfYOpOkF3AeA==}
    engines: {node: '>=0.11.0'}
    hasBin: true
    dependencies:
      babel-runtime: 6.26.0
      callsite-record: 3.2.2
      chalk: 1.1.3
      co: 4.6.0
      depcheck: 0.8.3
      execa: 0.2.2
      giturl: 1.0.1
      global-modules: 1.0.0
      globby: 4.1.0
      inquirer: 0.12.0
      is-ci: 1.2.1
      lodash: 4.17.21
      meow: 3.7.0
      minimatch: 3.1.2
      node-emoji: 1.11.0
      ora: 0.2.3
      package-json: 4.0.1
      path-exists: 2.1.0
      pkg-dir: 1.0.0
      preferred-pm: 1.0.1
      semver: 5.7.1
      semver-diff: 2.1.0
      text-table: 0.2.0
      throat: 2.0.2
      update-notifier: 2.5.0
      xtend: 4.0.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /npm-run-path/1.0.0:
    resolution: {integrity: sha1-9cMr9ZX+ga6Sfa7FLoL4sACsPI8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      path-key: 1.0.0
    dev: true

  /npm-run-path/2.0.2:
    resolution: {integrity: sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=}
    engines: {node: '>=4'}
    dependencies:
      path-key: 2.0.1
    dev: true

  /npm-run-path/4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /number-is-nan/1.0.1:
    resolution: {integrity: sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /object-assign/4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /object-inspect/1.12.0:
    resolution: {integrity: sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g==}
    dev: true

  /object-keys/1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: true

  /object.assign/4.1.2:
    resolution: {integrity: sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      has-symbols: 1.0.3
      object-keys: 1.1.1
    dev: true

  /object.entries/1.1.5:
    resolution: {integrity: sha512-TyxmjUoZggd4OrrU1W66FMDG6CuqJxsFvymeyXI51+vQLN67zYfZseptRge703kKQdo4uccgAKebXFcRCzk4+g==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
    dev: true

  /object.fromentries/2.0.5:
    resolution: {integrity: sha512-CAyG5mWQRRiBU57Re4FKoTBjXfDoNwdFVH2Y1tS9PqCsfUTymAohOkEMSG3aRNKmv4lV3O7p1et7c187q6bynw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
    dev: true

  /object.hasown/1.1.1:
    resolution: {integrity: sha512-LYLe4tivNQzq4JdaWW6WO3HMZZJWzkkH8fnI6EebWl0VZth2wL2Lovm74ep2/gZzlaTdV62JZHEqHQ2yVn8Q/A==}
    dependencies:
      define-properties: 1.1.4
      es-abstract: 1.20.0
    dev: true

  /object.values/1.1.5:
    resolution: {integrity: sha512-QUZRW0ilQ3PnPpbNtgdNV1PDbEqLIiSFB3l+EnGtBQ/8SUTLj1PZwtQHABZtLgwpJZTSZhuGLOGk57Drx2IvYg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
    dev: true

  /once/1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}
    dependencies:
      wrappy: 1.0.2
    dev: true

  /onetime/1.1.0:
    resolution: {integrity: sha1-ofeDj4MUxRbwXs78vEzP4EtO14k=}
    engines: {node: '>=0.10.0'}
    dev: true

  /onetime/5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /optionator/0.9.1:
    resolution: {integrity: sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.3
    dev: true

  /ora/0.2.3:
    resolution: {integrity: sha1-N1J9Igrc1Tw5tzVx11QVbV22V6Q=}
    engines: {node: '>=0.10.0'}
    dependencies:
      chalk: 1.1.3
      cli-cursor: 1.0.2
      cli-spinners: 0.1.2
      object-assign: 4.1.1
    dev: true

  /p-finally/1.0.0:
    resolution: {integrity: sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=}
    engines: {node: '>=4'}
    dev: true

  /p-limit/1.3.0:
    resolution: {integrity: sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==}
    engines: {node: '>=4'}
    dependencies:
      p-try: 1.0.0
    dev: true

  /p-limit/2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0
    dev: true

  /p-locate/2.0.0:
    resolution: {integrity: sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=}
    engines: {node: '>=4'}
    dependencies:
      p-limit: 1.3.0
    dev: true

  /p-locate/3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0
    dev: true

  /p-locate/4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0
    dev: true

  /p-try/1.0.0:
    resolution: {integrity: sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=}
    engines: {node: '>=4'}
    dev: true

  /p-try/2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}
    dev: true

  /package-json/4.0.1:
    resolution: {integrity: sha1-iGmgQBJTZhxMTKPabCEh7VVfXu0=}
    engines: {node: '>=4'}
    dependencies:
      got: 6.7.1
      registry-auth-token: 3.4.0
      registry-url: 3.1.0
      semver: 5.7.1
    dev: true

  /parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-json/2.2.0:
    resolution: {integrity: sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=}
    engines: {node: '>=0.10.0'}
    dependencies:
      error-ex: 1.3.2
    dev: true

  /parse-json/4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=}
    engines: {node: '>=4'}
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2
    dev: true

  /parse-json/5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.16.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: true

  /parse-passwd/1.0.0:
    resolution: {integrity: sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-exists/2.1.0:
    resolution: {integrity: sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie-promise: 2.0.1
    dev: true

  /path-exists/3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=}
    engines: {node: '>=4'}
    dev: true

  /path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: true

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-is-inside/1.0.2:
    resolution: {integrity: sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=}
    dev: true

  /path-key/1.0.0:
    resolution: {integrity: sha1-XVPVeAGWRsDWiADbThRua9wqx68=}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-key/2.0.1:
    resolution: {integrity: sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=}
    engines: {node: '>=4'}
    dev: true

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: true

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: true

  /path-type/1.1.0:
    resolution: {integrity: sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      graceful-fs: 4.2.10
      pify: 2.3.0
      pinkie-promise: 2.0.1
    dev: true

  /picocolors/1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}
    dev: true

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}
    dev: true

  /pify/2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=}
    engines: {node: '>=0.10.0'}
    dev: true

  /pify/3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=}
    engines: {node: '>=4'}
    dev: true

  /pify/4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}
    dev: true

  /pinkie-promise/2.0.1:
    resolution: {integrity: sha1-ITXW36ejWMBprJsXh3YogihFD/o=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie: 2.0.4
    dev: true

  /pinkie/2.0.4:
    resolution: {integrity: sha1-clVrgM+g1IqXToDnckjoDtT3+HA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /pirates/4.0.5:
    resolution: {integrity: sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==}
    engines: {node: '>= 6'}
    dev: true

  /pkg-conf/3.1.0:
    resolution: {integrity: sha512-m0OTbR/5VPNPqO1ph6Fqbj7Hv6QU7gR/tQW40ZqrL1rjgCU85W6C1bJn0BItuJqnR98PWzw7Z8hHeChD1WrgdQ==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
      load-json-file: 5.3.0
    dev: true

  /pkg-dir/1.0.0:
    resolution: {integrity: sha1-ektQio1bstYp1EcFb/TpyTFM89Q=}
    engines: {node: '>=0.10.0'}
    dependencies:
      find-up: 1.1.2
    dev: true

  /pkg-dir/4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
    dev: true

  /please-upgrade-node/3.2.0:
    resolution: {integrity: sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg==}
    dependencies:
      semver-compare: 1.0.0
    dev: true

  /preferred-pm/1.0.1:
    resolution: {integrity: sha512-9Uxgin5Xnsl67DBvlNFsmDIlBuG9/XKK2cVBTj//7/7wW6ZY+IC9/GlLqxyHABpoasAsJ1MARFOdYPxMUtndxA==}
    engines: {node: '>=4'}
    dependencies:
      path-exists: 3.0.0
      which-pm: 1.1.0
    dev: true

  /prelude-ls/1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /prepend-http/1.0.4:
    resolution: {integrity: sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=}
    engines: {node: '>=0.10.0'}
    dev: true

  /pretty-format/28.1.0:
    resolution: {integrity: sha512-79Z4wWOYCdvQkEoEuSlBhHJqWeZ8D8YRPiPctJFCtvuaClGpiwiQYSCUOE6IEKUbbFukKOTFIUAXE8N4EQTo1Q==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/schemas': 28.0.2
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 18.1.0
    dev: true

  /prompts/2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5
    dev: true

  /prop-types/15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1
    dev: true

  /pseudomap/1.0.2:
    resolution: {integrity: sha1-8FKijacOYYkX7wqKw0wa5aaChrM=}
    dev: true

  /punycode/2.1.1:
    resolution: {integrity: sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==}
    engines: {node: '>=6'}
    dev: true

  /rc/1.2.8:
    resolution: {integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==}
    hasBin: true
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.6
      strip-json-comments: 2.0.1
    dev: true

  /react-is/16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}
    dev: true

  /react-is/18.1.0:
    resolution: {integrity: sha512-Fl7FuabXsJnV5Q1qIOQwx/sagGF18kogb4gpfcG4gjLBWO0WDiiz1ko/ExayuxE7InyQkBLkxRFG5oxY6Uu3Kg==}
    dev: true

  /read-pkg-up/1.0.1:
    resolution: {integrity: sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      find-up: 1.1.2
      read-pkg: 1.1.0
    dev: true

  /read-pkg/1.1.0:
    resolution: {integrity: sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      load-json-file: 1.1.0
      normalize-package-data: 2.5.0
      path-type: 1.1.0
    dev: true

  /readline2/1.0.1:
    resolution: {integrity: sha1-QQWWCP/BVHV7cV2ZidGZ/783LjU=}
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      mute-stream: 0.0.5
    dev: true

  /redent/1.0.0:
    resolution: {integrity: sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=}
    engines: {node: '>=0.10.0'}
    dependencies:
      indent-string: 2.1.0
      strip-indent: 1.0.1
    dev: true

  /regenerator-runtime/0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: true

  /regexp.prototype.flags/1.4.3:
    resolution: {integrity: sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      functions-have-names: 1.2.3
    dev: true

  /regexpp/3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}
    dev: true

  /registry-auth-token/3.4.0:
    resolution: {integrity: sha512-4LM6Fw8eBQdwMYcES4yTnn2TqIasbXuwDx3um+QRs7S55aMKCBKBxvPXl2RiUjHwuJLTyYfxSpmfSAjQpcuP+A==}
    dependencies:
      rc: 1.2.8
      safe-buffer: 5.2.1
    dev: true

  /registry-url/3.1.0:
    resolution: {integrity: sha1-PU74cPc93h138M+aOBQyRE4XSUI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      rc: 1.2.8
    dev: true

  /repeating/2.0.1:
    resolution: {integrity: sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-finite: 1.1.0
    dev: true

  /require-directory/2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}
    dev: true

  /require-main-filename/2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}
    dev: true

  /require-package-name/2.0.1:
    resolution: {integrity: sha1-wR6XJ2tluOKSP3Xav1+y7ww4Qbk=}
    dev: true

  /resolve-cwd/3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}
    dependencies:
      resolve-from: 5.0.0
    dev: true

  /resolve-dir/1.0.1:
    resolution: {integrity: sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=}
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: 2.0.2
      global-modules: 1.0.0
    dev: true

  /resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /resolve-from/5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}
    dev: true

  /resolve.exports/1.1.0:
    resolution: {integrity: sha512-J1l+Zxxp4XK3LUDZ9m60LRJF/mAe4z6a4xyabPHk7pvK5t35dACV32iIjJDFeWZFfZlO29w6SZ67knR0tHzJtQ==}
    engines: {node: '>=10'}
    dev: true

  /resolve/1.22.0:
    resolution: {integrity: sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw==}
    hasBin: true
    dependencies:
      is-core-module: 2.9.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /resolve/2.0.0-next.3:
    resolution: {integrity: sha512-W8LucSynKUIDu9ylraa7ueVZ7hc0uAgJBxVsQSKOXOyle8a93qXhcz+XAXZ8bIq2d6i4Ehddn6Evt+0/UwKk6Q==}
    dependencies:
      is-core-module: 2.9.0
      path-parse: 1.0.7
    dev: true

  /restore-cursor/1.0.1:
    resolution: {integrity: sha1-NGYfRohjJ/7SmRR5FSJS35LapUE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      exit-hook: 1.1.1
      onetime: 1.1.0
    dev: true

  /rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true
    dependencies:
      glob: 7.2.2
    dev: true

  /run-async/0.1.0:
    resolution: {integrity: sha1-yK1KXhEGYeQCp9IbUw4AnyX444k=}
    dependencies:
      once: 1.4.0
    dev: true

  /rx-lite/3.1.2:
    resolution: {integrity: sha1-Gc5QLKVyZl87ZHsQk5+X/RYV8QI=}
    dev: true

  /safe-buffer/5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}
    dev: true

  /safe-buffer/5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}
    dev: true

  /semver-compare/1.0.0:
    resolution: {integrity: sha1-De4hahyUGrN+nvsXiPavxf9VN/w=}
    dev: true

  /semver-diff/2.1.0:
    resolution: {integrity: sha1-S7uEN8jTfksM8aaP1ybsbWRdbTY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      semver: 5.7.1
    dev: true

  /semver/5.7.1:
    resolution: {integrity: sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==}
    hasBin: true
    dev: true

  /semver/6.3.0:
    resolution: {integrity: sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==}
    hasBin: true
    dev: true

  /semver/7.3.7:
    resolution: {integrity: sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /set-blocking/2.0.0:
    resolution: {integrity: sha1-BF+XgtARrppoA93TgrJDkrPYkPc=}
    dev: true

  /shebang-command/1.2.0:
    resolution: {integrity: sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: 1.0.0
    dev: true

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex/1.0.0:
    resolution: {integrity: sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: true

  /shell-exec/1.0.2:
    resolution: {integrity: sha512-jyVd+kU2X+mWKMmGhx4fpWbPsjvD53k9ivqetutVW/BQ+WIZoDoP4d8vUMGezV6saZsiNoW2f9GIhg9Dondohg==}
    dev: false

  /side-channel/1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.1
      object-inspect: 1.12.0
    dev: true

  /signal-exit/3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: true

  /sisteransi/1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}
    dev: true

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /source-map-support/0.5.13:
    resolution: {integrity: sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: true

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /spdx-correct/3.1.1:
    resolution: {integrity: sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.11
    dev: true

  /spdx-exceptions/2.3.0:
    resolution: {integrity: sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==}
    dev: true

  /spdx-expression-parse/3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.11
    dev: true

  /spdx-license-ids/3.0.11:
    resolution: {integrity: sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g==}
    dev: true

  /sprintf-js/1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=}
    dev: true

  /stack-utils/2.0.5:
    resolution: {integrity: sha512-xrQcmYhOsn/1kX+Vraq+7j4oE2j/6BFscZ0etmYg81xuM8Gq0022Pxb8+IqgOFUIaxHs0KaSb7T1+OegiNrNFA==}
    engines: {node: '>=10'}
    dependencies:
      escape-string-regexp: 2.0.0
    dev: true

  /stackframe/0.3.1:
    resolution: {integrity: sha1-M6qE8Rd6VUjIk1Uzy/6zQgl19aQ=}
    dev: true

  /standard-engine/15.0.0:
    resolution: {integrity: sha512-4xwUhJNo1g/L2cleysUqUv7/btn7GEbYJvmgKrQ2vd/8pkTmN8cpqAZg+BT8Z1hNeEH787iWUdOpL8fmApLtxA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      get-stdin: 8.0.0
      minimist: 1.2.6
      pkg-conf: 3.1.0
      xdg-basedir: 4.0.0
    dev: true

  /standard/17.0.0:
    resolution: {integrity: sha512-GlCM9nzbLUkr+TYR5I2WQoIah4wHA2lMauqbyPLV/oI5gJxqhHzhjl9EG2N0lr/nRqI3KCbCvm/W3smxvLaChA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true
    dependencies:
      eslint: 8.15.0
      eslint-config-standard: 17.0.0_4b4uawhw2diczlyyjnbxhuzoii
      eslint-config-standard-jsx: 11.0.0_jwaecsryikbooohroqzy6hkyri
      eslint-plugin-import: 2.26.0_eslint@8.15.0
      eslint-plugin-n: 15.2.0_eslint@8.15.0
      eslint-plugin-promise: 6.0.0_eslint@8.15.0
      eslint-plugin-react: 7.29.4_eslint@8.15.0
      standard-engine: 15.0.0
    transitivePeerDependencies:
      - '@typescript-eslint/parser'
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: true

  /string-length/4.0.2:
    resolution: {integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==}
    engines: {node: '>=10'}
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1
    dev: true

  /string-width/1.0.2:
    resolution: {integrity: sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=}
    engines: {node: '>=0.10.0'}
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      strip-ansi: 3.0.1
    dev: true

  /string-width/2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0
    dev: true

  /string-width/3.1.0:
    resolution: {integrity: sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==}
    engines: {node: '>=6'}
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0
    dev: true

  /string-width/4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /string.prototype.matchall/4.0.7:
    resolution: {integrity: sha512-f48okCX7JiwVi1NXCVWcFnZgADDC/n2vePlQ/KUCNqCikLLilQvwjMO8+BHVKvgzH0JB0J9LEPgxOGT02RoETg==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
      get-intrinsic: 1.1.1
      has-symbols: 1.0.3
      internal-slot: 1.0.3
      regexp.prototype.flags: 1.4.3
      side-channel: 1.0.4
    dev: true

  /string.prototype.trimend/1.0.5:
    resolution: {integrity: sha512-I7RGvmjV4pJ7O3kdf+LXFpVfdNOxtCW/2C8f6jNiW4+PQchwxkCDzlk1/7p+Wl4bqFIZeF47qAHXLuHHWKAxog==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
    dev: true

  /string.prototype.trimstart/1.0.5:
    resolution: {integrity: sha512-THx16TJCGlsN0o6dl2o6ncWUsdgnLRSA23rRE5pyGBw/mLr3Ej/R2LaqCtgP8VNMGZsvMWnf9ooZPyY2bHvUFg==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.0
    dev: true

  /strip-ansi/3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /strip-ansi/4.0.0:
    resolution: {integrity: sha1-qEeQIusaw2iocTibY1JixQXuNo8=}
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: 3.0.1
    dev: true

  /strip-ansi/5.2.0:
    resolution: {integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==}
    engines: {node: '>=6'}
    dependencies:
      ansi-regex: 4.1.1
    dev: true

  /strip-ansi/6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-bom/2.0.0:
    resolution: {integrity: sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-utf8: 0.2.1
    dev: true

  /strip-bom/3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=}
    engines: {node: '>=4'}
    dev: true

  /strip-bom/4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}
    dev: true

  /strip-eof/1.0.0:
    resolution: {integrity: sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /strip-final-newline/2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}
    dev: true

  /strip-indent/1.0.1:
    resolution: {integrity: sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      get-stdin: 4.0.1
    dev: true

  /strip-json-comments/2.0.1:
    resolution: {integrity: sha1-PFMZQukIwml8DsNEhYwobHygpgo=}
    engines: {node: '>=0.10.0'}
    dev: true

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: true

  /supports-color/2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=}
    engines: {node: '>=0.8.0'}
    dev: true

  /supports-color/5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-color/8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-hyperlinks/2.2.0:
    resolution: {integrity: sha512-6sXEzV5+I5j8Bmq9/vUphGRM/RJNT9SCURJLjwfOg51heRtguGWDzcaBlgAzKhQa0EVNpPEKzQuBwZ8S8WaCeQ==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0
    dev: true

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: true

  /term-size/1.2.0:
    resolution: {integrity: sha1-RYuDiH8oj8Vtb/+/rSYuJmOO+mk=}
    engines: {node: '>=4'}
    dependencies:
      execa: 0.7.0
    dev: true

  /terminal-link/2.1.1:
    resolution: {integrity: sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==}
    engines: {node: '>=8'}
    dependencies:
      ansi-escapes: 4.3.2
      supports-hyperlinks: 2.2.0
    dev: true

  /test-exclude/6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.2
      minimatch: 3.1.2
    dev: true

  /text-table/0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=}
    dev: true

  /throat/2.0.2:
    resolution: {integrity: sha1-qfzoCLaeEzpjJZB4DzQsMKYkmwI=}
    dev: true

  /throat/6.0.1:
    resolution: {integrity: sha512-8hmiGIJMDlwjg7dlJ4yKGLK8EsYqKgPWbG3b4wjJddKNwc7N7Dpn08Df4szr/sZdMVeOstrdYSsqzX6BYbcB+w==}
    dev: true

  /through/2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}
    dev: true

  /timed-out/4.0.1:
    resolution: {integrity: sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /tmpl/1.0.5:
    resolution: {integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==}
    dev: true

  /to-fast-properties/2.0.0:
    resolution: {integrity: sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=}
    engines: {node: '>=4'}
    dev: true

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /trim-newlines/1.0.0:
    resolution: {integrity: sha1-WIeWa7WCpFA6QetST301ARgVphM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /tsconfig-paths/3.14.1:
    resolution: {integrity: sha512-fxDhWnFSLt3VuTwtvJt5fpwxBHg5AdKWMsgcPOOIilyjymcYVZoCQF8fvFRezCNfblEXmi+PcM1eYHeOAgXCOQ==}
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.1
      minimist: 1.2.6
      strip-bom: 3.0.0
    dev: true

  /type-check/0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /type-detect/4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}
    dev: true

  /type-fest/0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.3.1:
    resolution: {integrity: sha512-cUGJnCdr4STbePCgqNFbpVNCepa+kAVohJs1sLhxzdH+gnEoOd8VhbYa7pD3zZYGiURWM2xzEII3fQcRizDkYQ==}
    engines: {node: '>=6'}
    dev: true

  /unbox-primitive/1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.2
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2
    dev: true

  /unique-string/1.0.0:
    resolution: {integrity: sha1-nhBXzKhRq7kzmPizOuGHuZyuwRo=}
    engines: {node: '>=4'}
    dependencies:
      crypto-random-string: 1.0.0
    dev: true

  /unzip-response/2.0.1:
    resolution: {integrity: sha1-0vD3N9FrBhXnKmk17QQhRXLVb5c=}
    engines: {node: '>=4'}
    dev: true

  /update-notifier/2.5.0:
    resolution: {integrity: sha512-gwMdhgJHGuj/+wHJJs9e6PcCszpxR1b236igrOkUofGhqJuG+amlIKwApH1IW1WWl7ovZxsX49lMBWLxSdm5Dw==}
    engines: {node: '>=4'}
    dependencies:
      boxen: 1.3.0
      chalk: 2.4.2
      configstore: 3.1.5
      import-lazy: 2.1.0
      is-ci: 1.2.1
      is-installed-globally: 0.1.0
      is-npm: 1.0.0
      latest-version: 3.1.0
      semver-diff: 2.1.0
      xdg-basedir: 3.0.0
    dev: true

  /uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.1.1
    dev: true

  /url-parse-lax/1.0.0:
    resolution: {integrity: sha1-evjzA2Rem9eaJy56FKxovAYJ2nM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      prepend-http: 1.0.4
    dev: true

  /v8-compile-cache/2.3.0:
    resolution: {integrity: sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==}
    dev: true

  /v8-to-istanbul/9.0.0:
    resolution: {integrity: sha512-HcvgY/xaRm7isYmyx+lFKA4uQmfUbN0J4M0nNItvzTvH/iQ9kW5j/t4YSR+Ge323/lrgDAWJoF46tzGQHwBHFw==}
    engines: {node: '>=10.12.0'}
    dependencies:
      '@jridgewell/trace-mapping': 0.3.13
      '@types/istanbul-lib-coverage': 2.0.4
      convert-source-map: 1.8.0
    dev: true

  /validate-npm-package-license/3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.1.1
      spdx-expression-parse: 3.0.1
    dev: true

  /vue-template-compiler/2.6.14:
    resolution: {integrity: sha512-ODQS1SyMbjKoO1JBJZojSw6FE4qnh9rIpUZn2EUT86FKizx9uH5z6uXiIrm4/Nb/gwxTi/o17ZDEGWAXHvtC7g==}
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0
    dev: true

  /walkdir/0.3.2:
    resolution: {integrity: sha512-0Twghia4Z5wDGDYWURlhZmI47GvERMCsXIu0QZWVVZyW9ZjpbbZvD9Zy9M6cWiQQRRbAcYajIyKNavaZZDt1Uw==}
    engines: {node: '>=6.0.0'}
    dev: true

  /walker/1.0.8:
    resolution: {integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==}
    dependencies:
      makeerror: 1.0.12
    dev: true

  /which-boxed-primitive/1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4
    dev: true

  /which-module/2.0.0:
    resolution: {integrity: sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=}
    dev: true

  /which-pm/1.1.0:
    resolution: {integrity: sha512-7GHHJQpALk7BWMD8I+xSILSbHyngvBlfSXlwGpdRFY2voFwVCx+eJAybXTzTnUYmt7zio6B9SEdI81T0fBjxNA==}
    engines: {node: '>=4'}
    dependencies:
      load-yaml-file: 0.1.1
      path-exists: 3.0.0
    dev: true

  /which/1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /widest-line/2.0.1:
    resolution: {integrity: sha512-Ba5m9/Fa4Xt9eb2ELXt77JxVDV8w7qQrH0zS/TWSJdLyAwQjWoOzpzj5lwVftDz6n/EOu3tNACS84v509qwnJA==}
    engines: {node: '>=4'}
    dependencies:
      string-width: 2.1.1
    dev: true

  /word-wrap/1.2.3:
    resolution: {integrity: sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /wrap-ansi/5.1.0:
    resolution: {integrity: sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      string-width: 3.1.0
      strip-ansi: 5.2.0
    dev: true

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrappy/1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}
    dev: true

  /write-file-atomic/2.4.3:
    resolution: {integrity: sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ==}
    dependencies:
      graceful-fs: 4.2.10
      imurmurhash: 0.1.4
      signal-exit: 3.0.7
    dev: true

  /write-file-atomic/4.0.1:
    resolution: {integrity: sha512-nSKUxgAbyioruk6hU87QzVbY279oYT6uiwgDoujth2ju4mJ+TZau7SQBhtbTmUyuNYTuXnSyRn66FV0+eCgcrQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16}
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7
    dev: true

  /xdg-basedir/3.0.0:
    resolution: {integrity: sha1-SWsswQnsqNus/i3HK2A8F8WHCtQ=}
    engines: {node: '>=4'}
    dev: true

  /xdg-basedir/4.0.0:
    resolution: {integrity: sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==}
    engines: {node: '>=8'}
    dev: true

  /xtend/4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}
    dev: true

  /y18n/4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}
    dev: true

  /y18n/5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}
    dev: true

  /yallist/2.1.2:
    resolution: {integrity: sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=}
    dev: true

  /yallist/4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: true

  /yargs-parser/13.1.2:
    resolution: {integrity: sha512-3lbsNRf/j+A4QuSZfDRA7HRSfWrzO0YjqTJd5kjAq37Zep1CEgaYmrH9Q3GwPiB9cHyd1Y1UwggGhJGoxipbzg==}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0
    dev: true

  /yargs-parser/21.0.1:
    resolution: {integrity: sha512-9BK1jFpLzJROCI5TzwZL/TU4gqjK5xiHV/RfWLOahrjAko/e4DJkRDZQXfvqAsiZzzYhgAzbgz6lg48jcm4GLg==}
    engines: {node: '>=12'}
    dev: true

  /yargs/13.3.2:
    resolution: {integrity: sha512-AX3Zw5iPruN5ie6xGRIDgqkT+ZhnRlZMLMHAs8tg7nRruy2Nb+i5o9bwghAogtM08q1dpr2LVoS8KSTMYpWXUw==}
    dependencies:
      cliui: 5.0.0
      find-up: 3.0.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 3.1.0
      which-module: 2.0.0
      y18n: 4.0.3
      yargs-parser: 13.1.2
    dev: true

  /yargs/17.5.0:
    resolution: {integrity: sha512-3sLxVhbAB5OC8qvVRebCLWuouhwh/rswsiDYx3WGxajUk/l4G20SKfrKKFeNIHboUFt2JFgv2yfn+5cgOr/t5A==}
    engines: {node: '>=12'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.0.1
    dev: true
