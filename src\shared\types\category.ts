/**
 * 分类相关类型定义
 */

// 分类类型枚举
export enum CategoryType {
  PROJECT = 'project',
  AUTHOR = 'author'
}

// 分类接口
export interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  weight: number; // 权重，用于排序
  parentId?: string; // 父分类ID，支持层级分类
  type: CategoryType; // 分类类型：项目分类或作者分类
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  metadata?: CategoryMetadata;
}

// 分类元数据接口
export interface CategoryMetadata {
  authorCount?: number; // 该分类下的作者数量
  projectCount?: number; // 该分类下的项目数量
  lastUpdateCheck?: string; // 最后更新检查时间
  tags?: string[]; // 标签
  notes?: string; // 备注
}

// 分类创建请求接口
export interface CreateCategoryRequest {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  weight?: number;
  parentId?: string;
  type: CategoryType; // 必须指定分类类型
  tags?: string[];
  notes?: string;
}

// 分类更新请求接口
export interface UpdateCategoryRequest {
  id: string;
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  weight?: number;
  parentId?: string;
  type?: CategoryType; // 可选更新分类类型
  isActive?: boolean;
  tags?: string[];
  notes?: string;
}

// 分类树节点接口
export interface CategoryTreeNode {
  category: Category;
  children: CategoryTreeNode[];
  level: number;
  isExpanded?: boolean;
  isSelected?: boolean;
}

// 分类统计接口
export interface CategoryStats {
  id: string;
  name: string;
  authorCount: number;
  projectCount: number;
  totalStars: number;
  totalForks: number;
  lastActivity: string;
  updateFrequency: number; // 更新频率（天）
}

// 分类排序选项
export type CategorySortField = 'name' | 'weight' | 'createdAt' | 'updatedAt' | 'authorCount' | 'projectCount';

// 分类过滤选项接口
export interface CategoryFilter {
  isActive?: boolean;
  parentId?: string | null;
  type?: CategoryType; // 按分类类型过滤
  hasAuthors?: boolean;
  hasProjects?: boolean;
  tags?: string[];
  search?: string;
}

// 分类操作类型
export type CategoryAction = 
  | 'create'
  | 'update'
  | 'delete'
  | 'activate'
  | 'deactivate'
  | 'move'
  | 'duplicate';

// 分类操作历史接口
export interface CategoryActionHistory {
  id: string;
  categoryId: string;
  action: CategoryAction;
  oldData?: Partial<Category>;
  newData?: Partial<Category>;
  userId?: string;
  timestamp: string;
  description?: string;
}

// 分类导入/导出接口
export interface CategoryExportData {
  categories: Category[];
  metadata: {
    exportedAt: string;
    version: string;
    totalCount: number;
  };
}

// 分类验证错误接口
export interface CategoryValidationError {
  field: string;
  message: string;
  code: string;
}

// 分类批量操作接口
export interface CategoryBatchOperation {
  action: CategoryAction;
  categoryIds: string[];
  data?: Partial<Category>;
}

// 分类搜索结果接口
export interface CategorySearchResult {
  categories: Category[];
  total: number;
  hasMore: boolean;
  searchTime: number;
}

// 预定义分类颜色
export const CATEGORY_COLORS = [
  '#1890ff', // 蓝色
  '#52c41a', // 绿色
  '#faad14', // 黄色
  '#f5222d', // 红色
  '#722ed1', // 紫色
  '#fa541c', // 橙色
  '#13c2c2', // 青色
  '#eb2f96', // 品红
  '#666666', // 灰色
  '#2f54eb', // 深蓝
] as const;

// 预定义分类图标
export const CATEGORY_ICONS = [
  'folder',
  'tag',
  'star',
  'heart',
  'bookmark',
  'flag',
  'trophy',
  'diamond',
  'crown',
  'fire',
] as const;

export type CategoryColor = typeof CATEGORY_COLORS[number];
export type CategoryIcon = typeof CATEGORY_ICONS[number];
