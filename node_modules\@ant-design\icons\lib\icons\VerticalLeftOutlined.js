"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _VerticalLeftOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/VerticalLeftOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var VerticalLeftOutlined = function VerticalLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _VerticalLeftOutlined.default
  }));
};

/**![vertical-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc2MiAxNjRoLTY0Yy00LjQgMC04IDMuNi04IDh2Njg4YzAgNC40IDMuNiA4IDggOGg2NGM0LjQgMCA4LTMuNiA4LThWMTcyYzAtNC40LTMuNi04LTgtOHptLTUwOCAwdjcyLjRjMCA5LjUgNC4yIDE4LjQgMTEuNCAyNC41TDU2NC42IDUxMiAyNjUuNCA3NjMuMWMtNy4yIDYuMS0xMS40IDE1LTExLjQgMjQuNVY4NjBjMCA2LjggNy45IDEwLjUgMTMuMSA2LjFMNjg5IDUxMiAyNjcuMSAxNTcuOUE3Ljk1IDcuOTUgMCAwMDI1NCAxNjR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(VerticalLeftOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'VerticalLeftOutlined';
}
var _default = exports.default = RefIcon;