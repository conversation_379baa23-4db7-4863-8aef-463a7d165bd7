/**
 * 作者管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tag,
  Typography,
  Avatar,
  Rate,
} from 'antd';
import ResizableTable from '@/renderer/components/ResizableTable';
import AuthorProjectsExpanded from '@/renderer/components/AuthorProjectsExpanded';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  GithubOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  UpOutlined,
  DownOutlined,
  LinkOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import {
  selectAuthors,
  selectAuthorsLoading,
  setAuthors,
  setLoading,
} from '@/renderer/store/slices/authorSlice';
import { Author, CreateAuthorRequest, UpdateAuthorRequest, CategoryType } from '@/shared/types';

const { Title } = Typography;
const { TextArea } = Input;

const Authors: <AUTHORS>
  const dispatch = useAppDispatch();
  const authors = useAppSelector(selectAuthors);
  const categories = useAppSelector(selectCategories);
  const loading = useAppSelector(selectAuthorsLoading);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAuthor, setEditingAuthor] = useState<Author | null>(null);
  const [isLoadingUserInfo, setIsLoadingUserInfo] = useState(false);
  const [sortedAuthors, setSortedAuthors] = useState<Author[]>([]);
  const [expandedAuthor, setExpandedAuthor] = useState<string | null>(null);
  const [authorProjects, setAuthorProjects] = useState<Record<string, any[]>>({});
  const [authorUpdateCounts, setAuthorUpdateCounts] = useState<Record<string, { newCount: number; updateCount: number }>>({});
  const [authorHasUnread, setAuthorHasUnread] = useState<Record<string, boolean>>({});
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string>('all');
  const [form] = Form.useForm();

  useEffect(() => {
    // 从后端加载作者数据
    const loadAuthors = async () => {
      dispatch(setLoading(true));
      try {
        if (window.electronAPI) {
          const response = await window.electronAPI.data.getAuthors();
          if (response.success) {
            dispatch(setAuthors(response.data || []));
          } else {
            console.error('Failed to load authors:', response.error);
            dispatch(setAuthors([]));
          }
        } else {
          dispatch(setAuthors([]));
        }
      } catch (error) {
        console.error('Error loading authors:', error);
        dispatch(setAuthors([]));
      } finally {
        dispatch(setLoading(false));
      }
    };

    loadAuthors();
  }, [dispatch]);

  // 当authors或筛选条件变化时更新sortedAuthors
  useEffect(() => {
    let filteredAuthors = [...authors];

    // 分类筛选
    if (selectedCategoryFilter !== 'all') {
      filteredAuthors = filteredAuthors.filter(author =>
        author.categoryId === selectedCategoryFilter
      );
    }

    setSortedAuthors(filteredAuthors);

    // 加载所有作者的更新数量
    authors.forEach(author => {
      getAuthorUpdateCounts(author.id);
    });
  }, [authors, selectedCategoryFilter]);

  const handleAdd = () => {
    setEditingAuthor(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 从用户链接获取用户信息
  const handleFetchUserInfo = async () => {
    const userUrl = form.getFieldValue('userUrl');

    if (!userUrl) {
      message.warning('请先输入用户链接');
      return;
    }

    setIsLoadingUserInfo(true);
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.github.getUserInfoFromUrl(userUrl);

        if (response.success && response.data) {
          const userInfo = response.data;

          form.setFieldsValue({
            username: userInfo.login,
            displayName: userInfo.name || userInfo.login,
            bio: userInfo.bio || '',
          });
          message.success('用户信息获取成功');
        } else {
          message.error('无法获取用户信息，请检查链接是否正确');
        }
      } else {
        message.error('系统错误：无法访问GitHub API');
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      message.error('获取用户信息失败');
    } finally {
      setIsLoadingUserInfo(false);
    }
  };

  const handleEdit = (author: Author) => {
    setEditingAuthor(author);
    form.setFieldsValue({
      userUrl: `https://github.com/${author.username}`,
      username: author.username,
      displayName: author.displayName,
      email: author.email || '',
      bio: author.bio || '',
      location: author.location || '',
      company: author.company || '',
      website: author.website || '',
      categoryId: author.categoryId,
      isWatching: author.isWatching,
      priority: author.metadata?.priority || 3,
      notes: author.metadata?.notes,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (authorId: string) => {
    try {
      const updatedAuthors = authors.filter(a => a.id !== authorId);

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setAuthors(updatedAuthors);
        if (response.success) {
          dispatch(setAuthors(updatedAuthors));
          message.success('作者删除成功');
        } else {
          throw new Error('删除失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Delete author error:', error);
      message.error('作者删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      // 验证是否已获取用户信息
      if (!values.username) {
        message.warning('请先点击"获取信息"按钮获取用户信息');
        return;
      }

      let updatedAuthors: <AUTHORS>

      if (editingAuthor) {
        // 更新作者
        updatedAuthors = authors.map(author =>
          author.id === editingAuthor.id
            ? {
                ...author,
                username: values.username,
                displayName: values.displayName,
                bio: values.bio,
                categoryId: values.categoryId,
                isWatching: values.isWatching,
                updatedAt: new Date().toISOString(),
                metadata: {
                  ...author.metadata,
                  priority: values.priority,
                  notes: values.notes,
                },
              }
            : author
        );
      } else {
        // 创建作者
        const newAuthor: Author = {
          id: Date.now().toString(),
          username: values.username,
          displayName: values.displayName,
          email: '', // 设为空字符串
          bio: values.bio || '',
          location: '', // 设为空字符串
          company: '', // 设为空字符串
          website: '', // 设为空字符串
          categoryId: values.categoryId,
          isActive: true,
          isWatching: values.isWatching,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            priority: values.priority,
            notes: values.notes,
          },
        };
        updatedAuthors = [...authors, newAuthor];
      }

      // 保存到后端
      if (window.electronAPI) {
        const response = await window.electronAPI.data.setAuthors(updatedAuthors);
        if (response.success) {
          dispatch(setAuthors(updatedAuthors));
          message.success(editingAuthor ? '作者更新成功' : '作者创建成功');
          setIsModalVisible(false);
          form.resetFields();
        } else {
          throw new Error('保存失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Save author error:', error);
      message.error(editingAuthor ? '作者更新失败' : '作者创建失败');
    }
  };

  const toggleWatching = async (authorId: string, isWatching: boolean) => {
    try {
      const updatedAuthors = authors.map(author =>
        author.id === authorId
          ? { ...author, isWatching, updatedAt: new Date().toISOString() }
          : author
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setAuthors(updatedAuthors);
        if (response.success) {
          dispatch(setAuthors(updatedAuthors));
          message.success(`已${isWatching ? '开启' : '关闭'}监控`);
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Toggle watching error:', error);
      message.error('操作失败');
    }
  };

  // 处理展开/收起作者项目
  const handleToggleExpand = async (author: Author) => {
    if (expandedAuthor === author.id) {
      setExpandedAuthor(null);
    } else {
      setExpandedAuthor(author.id);
      // 如果还没有加载过这个作者的项目，则加载
      if (!authorProjects[author.id]) {
        await loadAuthorProjects(author);
      }
      // 刷新更新数量
      await getAuthorUpdateCounts(author.id);
    }
  };

  // 加载作者的所有项目
  const loadAuthorProjects = async (author: Author) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.github.getUserRepositories(author.username);
        if (response.success) {
          setAuthorProjects(prev => ({
            ...prev,
            [author.id]: response.data || []
          }));
        }
      }
    } catch (error) {
      console.error('Failed to load author projects:', error);
      message.error('加载作者项目失败');
    }
  };

  // 获取作者的更新数量
  const getAuthorUpdateCounts = async (authorId: string) => {
    try {
      if (window.electronAPI) {
        const [newProjectsResponse, updatedProjectsResponse] = await Promise.all([
          window.electronAPI.github.getAuthorNewProjects(authorId),
          window.electronAPI.github.getAuthorUpdatedProjects(authorId)
        ]);

        const savedReadProjects = localStorage.getItem(`read-author-projects-${authorId}`);
        let readProjects = savedReadProjects ? new Set(JSON.parse(savedReadProjects)) : new Set();

        // 处理新项目
        const newProjects = (newProjectsResponse.success ? newProjectsResponse.data : [])
          .filter((project: any) => !readProjects.has(project.id.toString()));

        // 处理更新项目 - 如果项目有实际更新，从已读列表中移除
        const allUpdatedProjects = updatedProjectsResponse.success ? updatedProjectsResponse.data : [];
        const updatedProjectIds = allUpdatedProjects.map((project: any) => project.id.toString());
        let hasRemovedReadProjects = false;

        updatedProjectIds.forEach(projectId => {
          if (readProjects.has(projectId)) {
            readProjects.delete(projectId);
            hasRemovedReadProjects = true;
            console.log(`Project ${projectId} has updates, removing from read list`);
          }
        });

        // 如果有项目被从已读列表中移除，更新localStorage
        if (hasRemovedReadProjects) {
          localStorage.setItem(`read-author-projects-${authorId}`, JSON.stringify([...readProjects]));
        }

        // 过滤出未读的更新项目
        const updatedProjects = allUpdatedProjects.filter((project: any) =>
          !readProjects.has(project.id.toString())
        );

        const counts = {
          newCount: newProjects.length,
          updateCount: updatedProjects.length
        };

        setAuthorUpdateCounts(prev => ({
          ...prev,
          [authorId]: counts
        }));

        // 更新是否有未读项目的状态
        const hasUnread = counts.newCount > 0 || counts.updateCount > 0;
        setAuthorHasUnread(prev => ({
          ...prev,
          [authorId]: hasUnread
        }));

        return counts;
      }
      return { newCount: 0, updateCount: 0 };
    } catch (error) {
      console.error('Failed to get author update counts:', error);
      return { newCount: 0, updateCount: 0 };
    }
  };

  // 处理作者分类变更
  const handleAuthorCategoryChange = async (authorId: string, newCategoryId: string) => {
    try {
      const updatedAuthors = authors.map(author =>
        author.id === authorId
          ? { ...author, categoryId: newCategoryId, updatedAt: new Date().toISOString() }
          : author
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setAuthors(updatedAuthors);
        if (response.success) {
          dispatch(setAuthors(updatedAuthors));
          message.success('分类更新成功');
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Update author category error:', error);
      message.error('分类更新失败');
    }
  };

  const columns = [
    {
      title: '作者',
      dataIndex: 'username',
      key: 'username',
      width: 200,
      resizable: true,
      sorter: (a: Author, b: Author) => (a.displayName || a.username).localeCompare(b.displayName || b.username),
      render: (text: string, record: Author) => (
        <Space>
          <Avatar
            src={record.avatar}
            icon={<UserOutlined />}
            size="small"
          />
          <div style={{ minWidth: 0, flex: 1 }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
              {record.displayName || text}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>@{text}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '简介',
      dataIndex: 'bio',
      key: 'bio',
      width: 250,
      resizable: true,
      ellipsis: true,
      sorter: (a: Author, b: Author) => (a.bio || '').localeCompare(b.bio || ''),
      render: (text: string) => (
        <div style={{
          maxWidth: '230px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {text || '暂无简介'}
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      key: 'categoryId',
      width: 120,
      resizable: true,
      sorter: (a: Author, b: Author) => {
        const categoryA = categories.find(c => c.id === a.categoryId)?.name || '未分类';
        const categoryB = categories.find(c => c.id === b.categoryId)?.name || '未分类';
        return categoryA.localeCompare(categoryB);
      },
      render: (categoryId: string) => {
        const category = categories.find(c => c.id === categoryId);
        return category ? (
          <Tag color={category.color}>{category.name}</Tag>
        ) : (
          <Tag>未分类</Tag>
        );
      },
    },
    {
      title: '优先级',
      dataIndex: ['metadata', 'priority'],
      key: 'priority',
      width: 80,
      resizable: true,
      sorter: (a: Author, b: Author) => (b.metadata?.priority || 3) - (a.metadata?.priority || 3),
      render: (priority: number = 3) => (
        <div style={{ textAlign: 'center' }}>
          <Tag color={priority >= 4 ? 'red' : priority >= 3 ? 'orange' : priority >= 2 ? 'blue' : 'green'}>
            {priority}
          </Tag>
        </div>
      ),
    },

    {
      title: '是否更新',
      key: 'hasUpdate',
      width: 100,
      resizable: true,
      sorter: (a: Author, b: Author) => {
        const hasUnreadA = authorHasUnread[a.id] ? 1 : 0;
        const hasUnreadB = authorHasUnread[b.id] ? 1 : 0;
        return hasUnreadB - hasUnreadA; // 有未读的在前
      },
      render: (_: any, record: Author) => {
        // 基于已读状态判断是否有更新：只要有未读项目就显示"有更新"
        const hasUnread = authorHasUnread[record.id] || false;

        return (
          <div style={{ textAlign: 'center' }}>
            {hasUnread ? (
              <Tag color="orange">有更新</Tag>
            ) : (
              <Tag color="green">无更新</Tag>
            )}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 280,
      fixed: 'right' as const,
      render: (_: any, record: Author) => (
        <Space size="small">
          <Button
            type="primary"
            icon={expandedAuthor === record.id ? <UpOutlined /> : <DownOutlined />}
            onClick={() => handleToggleExpand(record)}
            size="small"
          >
            {expandedAuthor === record.id ? '收起' : '展开'}
          </Button>
          <Button
            type="link"
            icon={<GithubOutlined />}
            onClick={() => {
              const url = `https://github.com/${record.username}`;
              if (window.electronAPI) {
                window.electronAPI.system.openExternal(url);
              } else {
                window.open(url, '_blank');
              }
            }}
            size="small"
          >
            GitHub
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个作者吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Title level={3} style={{ margin: 0 }}>作者管理</Title>
            <Select
              value={selectedCategoryFilter}
              onChange={setSelectedCategoryFilter}
              style={{ width: 200 }}
              placeholder="选择分类筛选"
            >
              <Select.Option value="all">所有分类</Select.Option>
              {categories
                .filter(category => category.type === CategoryType.AUTHOR)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    <Tag color={category.color} style={{ margin: 0 }}>{category.name}</Tag>
                  </Select.Option>
                ))}
            </Select>
          </div>
          <Space>
            <Button
              onClick={() => {
                localStorage.removeItem('table-widths-authors');
                window.location.reload();
              }}
              size="small"
            >
              重置列宽
            </Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加作者
            </Button>
          </Space>
        </div>

        <ResizableTable
          tableKey="authors"
          columns={columns}
          dataSource={sortedAuthors}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1000, y: 'calc(100vh - 300px)' }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          size="middle"
        />
      </Card>

      {/* 展开的作者项目详情 */}
      {expandedAuthor && (
        <Card style={{ marginTop: '16px' }}>
          <AuthorProjectsExpanded
            authorId={expandedAuthor}
            projects={authorProjects[expandedAuthor] || []}
            onMarkAsRead={(projectId: string) => {
              // 项目标记为已读后，重新检查作者的未读状态
              console.log('Mark as read:', projectId);
              // 延迟一点时间再检查，确保状态已更新
              setTimeout(() => {
                getAuthorUpdateCounts(expandedAuthor);
              }, 100);
            }}
            onUpdateCounts={(authorId: string, counts: { newCount: number; updateCount: number }) => {
              setAuthorUpdateCounts(prev => ({
                ...prev,
                [authorId]: counts
              }));

              // 同时更新是否有未读项目的状态
              const hasUnread = counts.newCount > 0 || counts.updateCount > 0;
              setAuthorHasUnread(prev => ({
                ...prev,
                [authorId]: hasUnread
              }));
            }}
          />
        </Card>
      )}

      <Modal
        title={editingAuthor ? '编辑作者' : '添加作者'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isWatching: true,
            priority: 3,
          }}
        >
          <Form.Item
            name="userUrl"
            label="用户链接"
            rules={[{ required: true, message: '请输入GitHub用户链接' }]}
            extra="输入GitHub用户链接，例如：https://github.com/username"
          >
            <Input
              placeholder="https://github.com/username"
              addonAfter={
                <Button
                  type="primary"
                  loading={isLoadingUserInfo}
                  onClick={handleFetchUserInfo}
                  size="small"
                >
                  获取信息
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            name="username"
            label="GitHub用户名"
            rules={[{ required: true, message: '请输入GitHub用户名' }]}
          >
            <Input placeholder="请输入GitHub用户名" disabled />
          </Form.Item>

          <Form.Item name="displayName" label="显示名称">
            <Input placeholder="显示名称（可选）" disabled />
          </Form.Item>

          <Form.Item name="bio" label="个人简介">
            <Input.TextArea placeholder="个人简介" disabled rows={2} />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择作者分类">
              {categories
                .filter(category => category.type === CategoryType.AUTHOR)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    {category.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item name="priority" label="优先级">
            <Rate count={5} />
          </Form.Item>

          <Form.Item name="isWatching" label="监控状态" valuePropName="checked">
            <Switch checkedChildren="监控" unCheckedChildren="不监控" />
          </Form.Item>

          <Form.Item name="notes" label="备注">
            <TextArea rows={3} placeholder="备注信息（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAuthor ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Authors;
