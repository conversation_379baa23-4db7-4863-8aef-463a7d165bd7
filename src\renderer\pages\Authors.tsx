/**
 * 作者管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tag,
  Typography,
  Avatar,
  Rate,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  GithubOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import {
  selectAuthors,
  selectAuthorsLoading,
  setAuthors,
  setLoading,
} from '@/renderer/store/slices/authorSlice';
import { Author, CreateAuthorRequest, UpdateAuthorRequest, CategoryType } from '@/shared/types';

const { Title } = Typography;
const { TextArea } = Input;

const Authors: <AUTHORS>
  const dispatch = useAppDispatch();
  const authors = useAppSelector(selectAuthors);
  const categories = useAppSelector(selectCategories);
  const loading = useAppSelector(selectAuthorsLoading);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAuthor, setEditingAuthor] = useState<Author | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    // 模拟加载作者数据
    dispatch(setLoading(true));
    setTimeout(() => {
      dispatch(setAuthors([]));
      dispatch(setLoading(false));
    }, 1000);
  }, [dispatch]);

  const handleAdd = () => {
    setEditingAuthor(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (author: Author) => {
    setEditingAuthor(author);
    form.setFieldsValue({
      username: author.username,
      displayName: author.displayName,
      categoryId: author.categoryId,
      isWatching: author.isWatching,
      priority: author.metadata?.priority || 3,
      notes: author.metadata?.notes,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (authorId: string) => {
    try {
      const updatedAuthors = authors.filter(a => a.id !== authorId);
      dispatch(setAuthors(updatedAuthors));
      message.success('作者删除成功');
    } catch (error) {
      message.error('作者删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingAuthor) {
        // 更新作者
        const updatedAuthors = authors.map(author =>
          author.id === editingAuthor.id
            ? {
                ...author,
                ...values,
                updatedAt: new Date().toISOString(),
                metadata: {
                  ...author.metadata,
                  priority: values.priority,
                  notes: values.notes,
                },
              }
            : author
        );
        dispatch(setAuthors(updatedAuthors));
        message.success('作者更新成功');
      } else {
        // 创建作者
        const newAuthor: Author = {
          id: Date.now().toString(),
          username: values.username,
          displayName: values.displayName,
          categoryId: values.categoryId,
          isActive: true,
          isWatching: values.isWatching,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            priority: values.priority,
            notes: values.notes,
          },
        };
        dispatch(setAuthors([...authors, newAuthor]));
        message.success('作者创建成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error(editingAuthor ? '作者更新失败' : '作者创建失败');
    }
  };

  const toggleWatching = (authorId: string, isWatching: boolean) => {
    const updatedAuthors = authors.map(author =>
      author.id === authorId
        ? { ...author, isWatching, updatedAt: new Date().toISOString() }
        : author
    );
    dispatch(setAuthors(updatedAuthors));
    message.success(`已${isWatching ? '开启' : '关闭'}监控`);
  };

  const columns = [
    {
      title: '作者',
      dataIndex: 'username',
      key: 'username',
      render: (text: string, record: Author) => (
        <Space>
          <Avatar
            src={record.avatar}
            icon={<UserOutlined />}
            size="small"
          />
          <div>
            <div>{record.displayName || text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>@{text}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      key: 'categoryId',
      render: (categoryId: string) => {
        const category = categories.find(c => c.id === categoryId);
        return category ? (
          <Tag color={category.color}>{category.name}</Tag>
        ) : (
          <Tag>未分类</Tag>
        );
      },
    },
    {
      title: '优先级',
      dataIndex: ['metadata', 'priority'],
      key: 'priority',
      render: (priority: number = 3) => (
        <Rate disabled value={priority} count={5} />
      ),
    },
    {
      title: '监控状态',
      dataIndex: 'isWatching',
      key: 'isWatching',
      render: (isWatching: boolean, record: Author) => (
        <Switch
          checked={isWatching}
          onChange={(checked) => toggleWatching(record.id, checked)}
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheckedAt',
      key: 'lastCheckedAt',
      render: (date: string) => date ? new Date(date).toLocaleString() : '从未',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Author) => (
        <Space>
          <Button
            type="link"
            icon={<GithubOutlined />}
            onClick={() => window.open(`https://github.com/${record.username}`, '_blank')}
          >
            GitHub
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个作者吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>作者管理</Title>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加作者
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={authors}
          rowKey="id"
          loading={loading}
          scroll={{ y: 'calc(100vh - 300px)' }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingAuthor ? '编辑作者' : '添加作者'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isWatching: true,
            priority: 3,
          }}
        >
          <Form.Item
            name="username"
            label="GitHub用户名"
            rules={[{ required: true, message: '请输入GitHub用户名' }]}
          >
            <Input placeholder="请输入GitHub用户名" />
          </Form.Item>

          <Form.Item name="displayName" label="显示名称">
            <Input placeholder="显示名称（可选）" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择作者分类">
              {categories
                .filter(category => category.type === CategoryType.AUTHOR)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    {category.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item name="priority" label="优先级">
            <Rate count={5} />
          </Form.Item>

          <Form.Item name="isWatching" label="监控状态" valuePropName="checked">
            <Switch checkedChildren="监控" unCheckedChildren="不监控" />
          </Form.Item>

          <Form.Item name="notes" label="备注">
            <TextArea rows={3} placeholder="备注信息（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAuthor ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Authors;
