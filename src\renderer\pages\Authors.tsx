/**
 * 作者管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tag,
  Typography,
  Avatar,
  Rate,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  GithubOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import {
  selectAuthors,
  selectAuthorsLoading,
  setAuthors,
  setLoading,
} from '@/renderer/store/slices/authorSlice';
import { Author, CreateAuthorRequest, UpdateAuthorRequest, CategoryType } from '@/shared/types';

const { Title } = Typography;
const { TextArea } = Input;

const Authors: <AUTHORS>
  const dispatch = useAppDispatch();
  const authors = useAppSelector(selectAuthors);
  const categories = useAppSelector(selectCategories);
  const loading = useAppSelector(selectAuthorsLoading);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAuthor, setEditingAuthor] = useState<Author | null>(null);
  const [isLoadingUserInfo, setIsLoadingUserInfo] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    // 模拟加载作者数据
    dispatch(setLoading(true));
    setTimeout(() => {
      dispatch(setAuthors([]));
      dispatch(setLoading(false));
    }, 1000);
  }, [dispatch]);

  const handleAdd = () => {
    setEditingAuthor(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 从用户链接获取用户信息
  const handleFetchUserInfo = async () => {
    const userUrl = form.getFieldValue('userUrl');

    if (!userUrl) {
      message.warning('请先输入用户链接');
      return;
    }

    setIsLoadingUserInfo(true);
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.github.getUserInfoFromUrl(userUrl);

        if (response.success && response.data) {
          const userInfo = response.data;

          form.setFieldsValue({
            username: userInfo.login,
            displayName: userInfo.name || userInfo.login,
            bio: userInfo.bio || '',
          });
          message.success('用户信息获取成功');
        } else {
          message.error('无法获取用户信息，请检查链接是否正确');
        }
      } else {
        message.error('系统错误：无法访问GitHub API');
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      message.error('获取用户信息失败');
    } finally {
      setIsLoadingUserInfo(false);
    }
  };

  const handleEdit = (author: Author) => {
    setEditingAuthor(author);
    form.setFieldsValue({
      userUrl: `https://github.com/${author.username}`,
      username: author.username,
      displayName: author.displayName,
      email: author.email || '',
      bio: author.bio || '',
      location: author.location || '',
      company: author.company || '',
      website: author.website || '',
      categoryId: author.categoryId,
      isWatching: author.isWatching,
      priority: author.metadata?.priority || 3,
      notes: author.metadata?.notes,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (authorId: string) => {
    try {
      const updatedAuthors = authors.filter(a => a.id !== authorId);

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setAuthors(updatedAuthors);
        if (response.success) {
          dispatch(setAuthors(updatedAuthors));
          message.success('作者删除成功');
        } else {
          throw new Error('删除失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Delete author error:', error);
      message.error('作者删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      // 验证是否已获取用户信息
      if (!values.username) {
        message.warning('请先点击"获取信息"按钮获取用户信息');
        return;
      }

      let updatedAuthors: <AUTHORS>

      if (editingAuthor) {
        // 更新作者
        updatedAuthors = authors.map(author =>
          author.id === editingAuthor.id
            ? {
                ...author,
                username: values.username,
                displayName: values.displayName,
                bio: values.bio,
                categoryId: values.categoryId,
                isWatching: values.isWatching,
                updatedAt: new Date().toISOString(),
                metadata: {
                  ...author.metadata,
                  priority: values.priority,
                  notes: values.notes,
                },
              }
            : author
        );
      } else {
        // 创建作者
        const newAuthor: Author = {
          id: Date.now().toString(),
          username: values.username,
          displayName: values.displayName,
          email: '', // 设为空字符串
          bio: values.bio || '',
          location: '', // 设为空字符串
          company: '', // 设为空字符串
          website: '', // 设为空字符串
          categoryId: values.categoryId,
          isActive: true,
          isWatching: values.isWatching,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            priority: values.priority,
            notes: values.notes,
          },
        };
        updatedAuthors = [...authors, newAuthor];
      }

      // 保存到后端
      if (window.electronAPI) {
        const response = await window.electronAPI.data.setAuthors(updatedAuthors);
        if (response.success) {
          dispatch(setAuthors(updatedAuthors));
          message.success(editingAuthor ? '作者更新成功' : '作者创建成功');
          setIsModalVisible(false);
          form.resetFields();
        } else {
          throw new Error('保存失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Save author error:', error);
      message.error(editingAuthor ? '作者更新失败' : '作者创建失败');
    }
  };

  const toggleWatching = async (authorId: string, isWatching: boolean) => {
    try {
      const updatedAuthors = authors.map(author =>
        author.id === authorId
          ? { ...author, isWatching, updatedAt: new Date().toISOString() }
          : author
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setAuthors(updatedAuthors);
        if (response.success) {
          dispatch(setAuthors(updatedAuthors));
          message.success(`已${isWatching ? '开启' : '关闭'}监控`);
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Toggle watching error:', error);
      message.error('操作失败');
    }
  };

  const columns = [
    {
      title: '作者',
      dataIndex: 'username',
      key: 'username',
      render: (text: string, record: Author) => (
        <Space>
          <Avatar
            src={record.avatar}
            icon={<UserOutlined />}
            size="small"
          />
          <div>
            <div>{record.displayName || text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>@{text}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      key: 'categoryId',
      render: (categoryId: string) => {
        const category = categories.find(c => c.id === categoryId);
        return category ? (
          <Tag color={category.color}>{category.name}</Tag>
        ) : (
          <Tag>未分类</Tag>
        );
      },
    },
    {
      title: '优先级',
      dataIndex: ['metadata', 'priority'],
      key: 'priority',
      render: (priority: number = 3) => (
        <Rate disabled value={priority} count={5} />
      ),
    },
    {
      title: '监控状态',
      dataIndex: 'isWatching',
      key: 'isWatching',
      render: (isWatching: boolean, record: Author) => (
        <Switch
          checked={isWatching}
          onChange={(checked) => toggleWatching(record.id, checked)}
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheckedAt',
      key: 'lastCheckedAt',
      render: (date: string) => date ? new Date(date).toLocaleString() : '从未',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Author) => (
        <Space>
          <Button
            type="link"
            icon={<GithubOutlined />}
            onClick={() => window.open(`https://github.com/${record.username}`, '_blank')}
          >
            GitHub
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个作者吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>作者管理</Title>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加作者
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={authors}
          rowKey="id"
          loading={loading}
          scroll={{ y: 'calc(100vh - 300px)' }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingAuthor ? '编辑作者' : '添加作者'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isWatching: true,
            priority: 3,
          }}
        >
          <Form.Item
            name="userUrl"
            label="用户链接"
            rules={[{ required: true, message: '请输入GitHub用户链接' }]}
            extra="输入GitHub用户链接，例如：https://github.com/username"
          >
            <Input
              placeholder="https://github.com/username"
              addonAfter={
                <Button
                  type="primary"
                  loading={isLoadingUserInfo}
                  onClick={handleFetchUserInfo}
                  size="small"
                >
                  获取信息
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            name="username"
            label="GitHub用户名"
            rules={[{ required: true, message: '请输入GitHub用户名' }]}
          >
            <Input placeholder="请输入GitHub用户名" disabled />
          </Form.Item>

          <Form.Item name="displayName" label="显示名称">
            <Input placeholder="显示名称（可选）" disabled />
          </Form.Item>

          <Form.Item name="bio" label="个人简介">
            <Input.TextArea placeholder="个人简介" disabled rows={2} />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择作者分类">
              {categories
                .filter(category => category.type === CategoryType.AUTHOR)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    {category.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item name="priority" label="优先级">
            <Rate count={5} />
          </Form.Item>

          <Form.Item name="isWatching" label="监控状态" valuePropName="checked">
            <Switch checkedChildren="监控" unCheckedChildren="不监控" />
          </Form.Item>

          <Form.Item name="notes" label="备注">
            <TextArea rows={3} placeholder="备注信息（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAuthor ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Authors;
