/**
 * 项目管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tag,
  Typography,
  Rate,
  Statistic,
} from 'antd';
import ResizableTable from '@/renderer/components/ResizableTable';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ProjectOutlined,
  GithubOutlined,
  StarOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  HeartOutlined,
  HeartFilled,
  LinkOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import {
  selectProjects,
  selectProjectsLoading,
  setProjects,
  setLoading,
} from '@/renderer/store/slices/projectSlice';
import { Project, CreateProjectRequest, UpdateProjectRequest, CategoryType } from '@/shared/types';

const { Title } = Typography;
const { TextArea } = Input;

const Projects: React.FC = () => {
  const dispatch = useAppDispatch();
  const projects = useAppSelector(selectProjects);
  const categories = useAppSelector(selectCategories);
  const loading = useAppSelector(selectProjectsLoading);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [isLoadingRepoInfo, setIsLoadingRepoInfo] = useState(false);
  const [sortedProjects, setSortedProjects] = useState<Project[]>([]);
  const [readProjects, setReadProjects] = useState<Set<string>>(new Set());
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string>('all');
  const [form] = Form.useForm();

  useEffect(() => {
    // 从后端加载项目数据
    const loadProjects = async () => {
      dispatch(setLoading(true));
      try {
        if (window.electronAPI) {
          const response = await window.electronAPI.data.getProjects();
          if (response.success) {
            dispatch(setProjects(response.data || []));
          } else {
            console.error('Failed to load projects:', response.error);
            dispatch(setProjects([]));
          }
        } else {
          dispatch(setProjects([]));
        }
      } catch (error) {
        console.error('Error loading projects:', error);
        dispatch(setProjects([]));
      } finally {
        dispatch(setLoading(false));
      }
    };

    loadProjects();
  }, [dispatch]);

  // 当projects变化时更新sortedProjects
  useEffect(() => {
    let filteredProjects = [...projects];

    // 分类筛选
    if (selectedCategoryFilter !== 'all') {
      filteredProjects = filteredProjects.filter(project =>
        project.categoryId === selectedCategoryFilter
      );
    }

    setSortedProjects(filteredProjects);
  }, [projects, selectedCategoryFilter]);

  // 加载已读项目状态
  useEffect(() => {
    const savedReadProjects = localStorage.getItem('read-projects');
    if (savedReadProjects) {
      try {
        setReadProjects(new Set(JSON.parse(savedReadProjects)));
      } catch (error) {
        console.error('Failed to load read projects:', error);
      }
    }
  }, []);

  // 保存已读项目状态
  const saveReadProjects = (newReadProjects: Set<string>) => {
    localStorage.setItem('read-projects', JSON.stringify([...newReadProjects]));
    setReadProjects(newReadProjects);
  };

  const handleAdd = () => {
    setEditingProject(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 标记项目为已读
  const handleMarkAsRead = (projectId: string) => {
    const newReadProjects = new Set(readProjects);
    newReadProjects.add(projectId);
    saveReadProjects(newReadProjects);
    message.success('已标记为已读');
  };

  // 打开项目（同时标记为已读）
  const handleOpenProject = (project: Project) => {
    // 标记为已读
    const newReadProjects = new Set(readProjects);
    newReadProjects.add(project.id);
    saveReadProjects(newReadProjects);

    // 打开链接
    if (window.electronAPI) {
      window.electronAPI.system.openExternal(project.url);
    } else {
      window.open(project.url, '_blank');
    }
  };

  // 处理分类变更
  const handleCategoryChange = async (projectId: string, newCategoryId: string) => {
    try {
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? { ...project, categoryId: newCategoryId, updatedAt: new Date().toISOString() }
          : project
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success('分类更新成功');
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Update category error:', error);
      message.error('分类更新失败');
    }
  };

  const handleEdit = (project: Project) => {
    setEditingProject(project);
    form.setFieldsValue({
      projectUrl: project.url,
      name: project.name,
      owner: project.owner,
      description: project.description,
      categoryId: project.categoryId,
      isWatching: project.isWatching,
      isFavorite: project.isFavorite,
      priority: project.metadata?.priority || 3,
      notes: project.metadata?.notes,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (projectId: string) => {
    try {
      const updatedProjects = projects.filter(p => p.id !== projectId);

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success('项目删除成功');
        } else {
          throw new Error('删除失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Delete project error:', error);
      message.error('项目删除失败');
    }
  };

  // 从项目链接获取项目信息
  const handleFetchRepoInfo = async () => {
    const projectUrl = form.getFieldValue('projectUrl');

    if (!projectUrl) {
      message.warning('请先输入项目链接');
      return;
    }

    setIsLoadingRepoInfo(true);
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.github.getRepoInfoFromUrl(projectUrl);

        if (response.success && response.data) {
          const repoInfo = response.data;

          form.setFieldsValue({
            name: repoInfo.name,
            owner: repoInfo.owner,
            description: repoInfo.description || '',
          });
          message.success('项目信息获取成功');
        } else {
          message.error('无法获取项目信息，请检查链接是否正确');
        }
      } else {
        message.error('系统错误：无法访问GitHub API');
      }
    } catch (error) {
      console.error('Failed to fetch repo info:', error);
      message.error('获取项目信息失败');
    } finally {
      setIsLoadingRepoInfo(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      // 验证是否已获取项目信息
      if (!values.owner || !values.name) {
        message.warning('请先点击"获取信息"按钮获取项目信息');
        return;
      }

      let updatedProjects: Project[];

      if (editingProject) {
        // 更新项目
        updatedProjects = projects.map(project =>
          project.id === editingProject.id
            ? {
                ...project,
                name: values.name,
                owner: values.owner,
                fullName: `${values.owner}/${values.name}`,
                url: values.projectUrl,
                description: values.description,
                categoryId: values.categoryId,
                isWatching: values.isWatching,
                isFavorite: values.isFavorite,
                updatedAt: new Date().toISOString(),
                metadata: {
                  ...project.metadata,
                  priority: values.priority,
                  notes: values.notes,
                },
              }
            : project
        );
      } else {
        // 创建项目
        const newProject: Project = {
          id: Date.now().toString(),
          name: values.name,
          fullName: `${values.owner}/${values.name}`,
          owner: values.owner,
          description: values.description,
          url: values.projectUrl,
          categoryId: values.categoryId,
          isActive: true,
          isWatching: values.isWatching,
          isFavorite: values.isFavorite,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            priority: values.priority,
            notes: values.notes,
          },
        };
        updatedProjects = [...projects, newProject];
      }

      // 保存到后端
      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success(editingProject ? '项目更新成功' : '项目创建成功');
          setIsModalVisible(false);
          form.resetFields();
        } else {
          throw new Error('保存失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Save project error:', error);
      message.error(editingProject ? '项目更新失败' : '项目创建失败');
    }
  };

  const toggleWatching = async (projectId: string, isWatching: boolean) => {
    try {
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? { ...project, isWatching, updatedAt: new Date().toISOString() }
          : project
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success(`已${isWatching ? '开启' : '关闭'}监控`);
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Toggle watching error:', error);
      message.error('操作失败');
    }
  };

  const toggleFavorite = async (projectId: string, isFavorite: boolean) => {
    try {
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? { ...project, isFavorite, updatedAt: new Date().toISOString() }
          : project
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success(`已${isFavorite ? '添加到' : '从'}收藏${isFavorite ? '' : '中移除'}`);
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Toggle favorite error:', error);
      message.error('操作失败');
    }
  };

  const columns = [
    {
      title: '项目',
      dataIndex: 'name',
      key: 'name',
      width: 250,
      resizable: true,
      sorter: (a: Project, b: Project) => a.name.localeCompare(b.name),
      render: (text: string, record: Project) => (
        <Space>
          <ProjectOutlined />
          <div style={{ minWidth: 0, flex: 1 }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666', wordBreak: 'break-all' }}>
              {record.fullName}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 300,
      resizable: true,
      sorter: (a: Project, b: Project) => (a.description || '').localeCompare(b.description || ''),
      render: (text: string) => (
        <div style={{
          maxWidth: '280px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {text || '暂无描述'}
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      key: 'categoryId',
      width: 120,
      resizable: true,
      sorter: (a: Project, b: Project) => {
        const categoryA = categories.find(c => c.id === a.categoryId)?.name || '未分类';
        const categoryB = categories.find(c => c.id === b.categoryId)?.name || '未分类';
        return categoryA.localeCompare(categoryB);
      },
      render: (categoryId: string) => {
        const category = categories.find(c => c.id === categoryId);
        return category ? (
          <Tag color={category.color}>{category.name}</Tag>
        ) : (
          <Tag>未分类</Tag>
        );
      },
    },

    {
      title: '优先级',
      dataIndex: ['metadata', 'priority'],
      key: 'priority',
      width: 80,
      resizable: true,
      sorter: (a: Project, b: Project) => (b.metadata?.priority || 3) - (a.metadata?.priority || 3),
      render: (priority: number = 3) => (
        <div style={{ textAlign: 'center' }}>
          <Tag color={priority >= 4 ? 'red' : priority >= 3 ? 'orange' : priority >= 2 ? 'blue' : 'green'}>
            {priority}
          </Tag>
        </div>
      ),
    },

    {
      title: '是否更新',
      key: 'hasUpdate',
      width: 100,
      resizable: true,
      sorter: (a: Project, b: Project) => {
        const hasUpdateA = (a.metadata?.hasUpdate && !readProjects.has(a.id)) ? 1 : 0;
        const hasUpdateB = (b.metadata?.hasUpdate && !readProjects.has(b.id)) ? 1 : 0;
        return hasUpdateB - hasUpdateA; // 有更新且未读的在前
      },
      render: (_: any, record: Project) => {
        const hasUpdate = record.metadata?.hasUpdate && !readProjects.has(record.id);
        return (
          <div style={{ textAlign: 'center' }}>
            {hasUpdate ? (
              <Tag color="orange">有更新</Tag>
            ) : (
              <Tag color="green">无更新</Tag>
            )}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 320,
      fixed: 'right' as const,
      render: (_: any, record: Project) => {
        const isRead = readProjects.has(record.id);
        const hasUpdate = record.metadata?.hasUpdate && !isRead;

        return (
          <Space size="small">
            <Button
              type={hasUpdate ? "primary" : "default"}
              icon={<CheckOutlined />}
              onClick={() => handleMarkAsRead(record.id)}
              size="small"
              style={{
                backgroundColor: hasUpdate ? '#faad14' : '#d9d9d9',
                borderColor: hasUpdate ? '#faad14' : '#d9d9d9',
                color: hasUpdate ? '#fff' : '#666'
              }}
            >
              已阅
            </Button>
            <Button
              type="primary"
              icon={<LinkOutlined />}
              onClick={() => handleOpenProject(record)}
              size="small"
            >
              打开
            </Button>
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这个项目吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<DeleteOutlined />} size="small">
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Title level={3} style={{ margin: 0 }}>项目管理</Title>
            <Select
              value={selectedCategoryFilter}
              onChange={setSelectedCategoryFilter}
              style={{ width: 200 }}
              placeholder="选择分类筛选"
            >
              <Select.Option value="all">所有分类</Select.Option>
              {categories
                .filter(category => category.type === CategoryType.PROJECT)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    <Tag color={category.color} style={{ margin: 0 }}>{category.name}</Tag>
                  </Select.Option>
                ))}
            </Select>
          </div>
          <Space>
            <Button
              onClick={() => {
                localStorage.removeItem('table-widths-projects');
                window.location.reload();
              }}
              size="small"
            >
              重置列宽
            </Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加项目
            </Button>
          </Space>
        </div>

        <ResizableTable
          tableKey="projects"
          columns={columns}
          dataSource={sortedProjects}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200, y: 'calc(100vh - 300px)' }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          size="middle"
        />
      </Card>

      <Modal
        title={editingProject ? '编辑项目' : '添加项目'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isWatching: true,
            isFavorite: false,
            priority: 3,
          }}
        >
          <Form.Item
            name="projectUrl"
            label="项目链接"
            rules={[{ required: true, message: '请输入GitHub项目链接' }]}
            extra="输入GitHub项目链接，例如：https://github.com/facebook/react"
          >
            <Input
              placeholder="https://github.com/owner/repo"
              addonAfter={
                <Button
                  type="primary"
                  loading={isLoadingRepoInfo}
                  onClick={handleFetchRepoInfo}
                  size="small"
                >
                  获取信息
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            name="owner"
            label="项目所有者"
            rules={[{ required: true, message: '请输入项目所有者' }]}
          >
            <Input placeholder="例如：facebook" disabled />
          </Form.Item>

          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="例如：react" disabled />
          </Form.Item>

          <Form.Item name="description" label="项目描述">
            <TextArea rows={3} placeholder="项目描述（可选）" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择项目分类">
              {categories
                .filter(category => category.type === CategoryType.PROJECT)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    {category.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item name="priority" label="优先级">
            <Rate count={5} />
          </Form.Item>

          <Space>
            <Form.Item name="isWatching" label="监控状态" valuePropName="checked">
              <Switch checkedChildren="监控" unCheckedChildren="不监控" />
            </Form.Item>

            <Form.Item name="isFavorite" label="收藏" valuePropName="checked">
              <Switch checkedChildren="收藏" unCheckedChildren="不收藏" />
            </Form.Item>
          </Space>

          <Form.Item name="notes" label="备注">
            <TextArea rows={3} placeholder="备注信息（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingProject ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Projects;
