/**
 * 项目管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tag,
  Typography,
  Rate,
  Statistic,
} from 'antd';
import ResizableTable from '@/renderer/components/ResizableTable';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ProjectOutlined,
  GithubOutlined,
  StarOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  HeartOutlined,
  HeartFilled,
  LinkOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import {
  selectProjects,
  selectProjectsLoading,
  setProjects,
  setLoading,
} from '@/renderer/store/slices/projectSlice';
import { Project, CreateProjectRequest, UpdateProjectRequest, CategoryType } from '@/shared/types';

const { Title } = Typography;
const { TextArea } = Input;

const Projects: React.FC = () => {
  const dispatch = useAppDispatch();
  const projects = useAppSelector(selectProjects);
  const categories = useAppSelector(selectCategories);
  const loading = useAppSelector(selectProjectsLoading);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [isLoadingRepoInfo, setIsLoadingRepoInfo] = useState(false);
  const [sortedProjects, setSortedProjects] = useState<Project[]>([]);
  const [readProjects, setReadProjects] = useState<Set<string>>(new Set());
  const [readProjectsTimestamps, setReadProjectsTimestamps] = useState<Record<string, string>>({});
  const [forceUpdate, setForceUpdate] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string>('all');
  const [form] = Form.useForm();

  useEffect(() => {
    // 从后端加载项目数据
    const loadProjects = async () => {
      dispatch(setLoading(true));
      try {
        if (window.electronAPI) {
          const response = await window.electronAPI.data.getProjects();
          if (response.success) {
            dispatch(setProjects(response.data || []));
          } else {
            console.error('Failed to load projects:', response.error);
            dispatch(setProjects([]));
          }
        } else {
          dispatch(setProjects([]));
        }
      } catch (error) {
        console.error('Error loading projects:', error);
        dispatch(setProjects([]));
      } finally {
        dispatch(setLoading(false));
      }
    };

    loadProjects();
  }, [dispatch]);

  // 当projects变化时更新sortedProjects
  useEffect(() => {
    let filteredProjects = [...projects];

    // 分类筛选
    if (selectedCategoryFilter !== 'all') {
      filteredProjects = filteredProjects.filter(project =>
        project.categoryId === selectedCategoryFilter
      );
    }

    setSortedProjects(filteredProjects);
  }, [projects, selectedCategoryFilter]);

  // 加载已读项目状态
  useEffect(() => {
    const savedReadProjects = localStorage.getItem('read-projects');
    if (savedReadProjects) {
      try {
        const readData = JSON.parse(savedReadProjects);
        // 兼容旧格式（数组）和新格式（对象）
        if (Array.isArray(readData)) {
          // 旧格式：直接使用项目ID集合
          setReadProjects(new Set(readData));
          setReadProjectsTimestamps({});
        } else {
          // 新格式：从对象键中提取项目ID，同时保存时间戳
          setReadProjects(new Set(Object.keys(readData)));
          setReadProjectsTimestamps(readData);
        }
      } catch (error) {
        console.error('Failed to load read projects:', error);
        setReadProjects(new Set());
        setReadProjectsTimestamps({});
      }
    }
  }, []);

  // 保存已读项目状态
  const saveReadProjects = (newReadProjects: Set<string>) => {
    // 注意：这里只更新状态，实际的localStorage保存在handleMarkAsRead中处理
    setReadProjects(newReadProjects);
  };

  // 辅助函数：获取项目的最新更新时间
  const getProjectLatestTime = (project: Project): Date => {
    // 使用项目的最新时间：最后提交时间 > 最后发布时间 > 项目更新时间
    const timeString = project.metadata?.lastCommitAt ||
                      project.metadata?.lastReleaseAt ||
                      project.updatedAt ||
                      '';

    const resultDate = new Date(timeString);

    console.log(`Getting latest time for ${project.name}:`, {
      lastCommitAt: project.metadata?.lastCommitAt,
      lastReleaseAt: project.metadata?.lastReleaseAt,
      updatedAt: project.updatedAt,
      selectedTime: timeString,
      resultDate: resultDate.toISOString(),
      isValidDate: !isNaN(resultDate.getTime())
    });

    return resultDate;
  };

  // 辅助函数：检查项目是否需要重新查看（基于时间戳比较）
  const checkProjectNeedsReview = (project: Project): boolean => {
    // 使用状态中的数据，而不是直接读取localStorage
    const readTimestamp = readProjectsTimestamps[project.id];

    if (!readTimestamp) {
      // 如果没有该项目的已读时间戳，显示金色（需要查看）
      console.log(`Project ${project.name}: 没有已读时间戳 → 需要查看`);
      return true;
    }

    const readTime = new Date(readTimestamp);
    const projectUpdatedAt = getProjectLatestTime(project);

    // 核心逻辑：项目更新时间 > 已读时间 = 需要重新查看（显示金色）
    const needsReview = projectUpdatedAt > readTime;

    console.log(`Project ${project.name} 时间戳比较:`, {
      projectUpdatedAt: projectUpdatedAt.toISOString(),
      readTime: readTime.toISOString(),
      comparison: `${projectUpdatedAt.toISOString()} > ${readTime.toISOString()}`,
      needsReview: needsReview,
      fromState: true // 标记这是从状态中获取的数据
    });

    return needsReview;
  };

  const handleAdd = () => {
    setEditingProject(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 测试函数：检查时间戳比较逻辑
  const testTimestampLogic = () => {
    console.log('\n=== 测试时间戳比较逻辑 ===');

    // 先检查localStorage中的数据
    const savedReadProjects = localStorage.getItem('read-projects');
    console.log('localStorage read-projects:', savedReadProjects);

    projects.forEach(project => {
      console.log(`\n--- 检查项目: ${project.name} ---`);
      console.log('项目数据:', {
        id: project.id,
        updatedAt: project.updatedAt,
        metadata: project.metadata
      });

      const needsReview = checkProjectNeedsReview(project);
      console.log(`最终结果: 需要查看 = ${needsReview}`);
      console.log(`UI状态: 按钮颜色 = ${needsReview ? '金色' : '灰色'}, 更新状态 = ${needsReview ? '有更新' : '无更新'}`);
    });

    console.log('=== 测试完成 ===\n');
  };

  // 显示当前状态的调试函数
  const showCurrentState = () => {
    console.log('\n=== 当前状态信息 ===');
    console.log('readProjects (Set):', Array.from(readProjects));
    console.log('readProjectsTimestamps (Object):', readProjectsTimestamps);
    console.log('localStorage read-projects:', localStorage.getItem('read-projects'));

    const claudeFlow = projects.find(p => p.name === 'claude-flow');
    if (claudeFlow) {
      console.log('\nclaude-flow 项目信息:');
      console.log('- ID:', claudeFlow.id);
      console.log('- updatedAt:', claudeFlow.updatedAt);
      console.log('- 是否在已读Set中:', readProjects.has(claudeFlow.id));
      console.log('- 已读时间戳:', readProjectsTimestamps[claudeFlow.id]);

      const needsReview = checkProjectNeedsReview(claudeFlow);
      console.log('- 需要查看:', needsReview);
      console.log('- 按钮颜色:', needsReview ? '金色' : '灰色');
    }
    console.log('=== 状态信息完成 ===\n');
  };

  // 强制刷新数据
  const forceRefreshData = () => {
    console.log('\n=== 强制刷新数据 ===');
    window.location.reload();
  };

  // 模拟项目更新的测试函数
  const simulateProjectUpdate = () => {
    console.log('\n=== 模拟项目更新 ===');
    const newUpdateTime = new Date().toISOString();
    console.log(`模拟项目更新时间: ${newUpdateTime}`);

    console.log('我已经将项目时间修改为: 2025-07-20T19:05:00.000Z');
    console.log('这个时间比您的已读时间更新，应该显示金色按钮');
    console.log('请点击"刷新数据"按钮来重新加载项目数据');
    console.log('=== 模拟说明完成 ===\n');
  };

  // 标记项目为已读
  const handleMarkAsRead = (projectId: string) => {
    const currentTime = new Date().toISOString();

    console.log(`标记项目 ${projectId} 为已读，时间戳: ${currentTime}`);

    // 更新内存中的已读状态
    const newReadProjects = new Set(readProjects);
    newReadProjects.add(projectId);

    // 更新时间戳状态
    const newReadTimestamps = { ...readProjectsTimestamps };
    newReadTimestamps[projectId] = currentTime;

    // 保存到localStorage
    localStorage.setItem('read-projects', JSON.stringify(newReadTimestamps));

    console.log('更新后的已读时间戳:', newReadTimestamps);

    // 同时更新两个状态，触发组件重新渲染
    setReadProjects(newReadProjects);
    setReadProjectsTimestamps(newReadTimestamps);

    message.success(`已标记为已读 (${currentTime})`);
  };

  // 打开项目（同时标记为已读）
  const handleOpenProject = (project: Project) => {
    // 使用与handleMarkAsRead相同的逻辑标记为已读
    handleMarkAsRead(project.id);

    // 打开链接
    if (window.electronAPI) {
      window.electronAPI.system.openExternal(project.url);
    } else {
      window.open(project.url, '_blank');
    }
  };

  // 处理分类变更
  const handleCategoryChange = async (projectId: string, newCategoryId: string) => {
    try {
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? { ...project, categoryId: newCategoryId, updatedAt: new Date().toISOString() }
          : project
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success('分类更新成功');
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Update category error:', error);
      message.error('分类更新失败');
    }
  };

  const handleEdit = (project: Project) => {
    setEditingProject(project);
    form.setFieldsValue({
      projectUrl: project.url,
      name: project.name,
      owner: project.owner,
      description: project.description,
      categoryId: project.categoryId,
      isWatching: project.isWatching,
      isFavorite: project.isFavorite,
      priority: project.metadata?.priority || 3,
      notes: project.metadata?.notes,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (projectId: string) => {
    try {
      const updatedProjects = projects.filter(p => p.id !== projectId);

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success('项目删除成功');
        } else {
          throw new Error('删除失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Delete project error:', error);
      message.error('项目删除失败');
    }
  };

  // 从项目链接获取项目信息
  const handleFetchRepoInfo = async () => {
    const projectUrl = form.getFieldValue('projectUrl');

    if (!projectUrl) {
      message.warning('请先输入项目链接');
      return;
    }

    setIsLoadingRepoInfo(true);
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.github.getRepoInfoFromUrl(projectUrl);

        if (response.success && response.data) {
          const repoInfo = response.data;

          form.setFieldsValue({
            name: repoInfo.name,
            owner: repoInfo.owner,
            description: repoInfo.description || '',
          });
          message.success('项目信息获取成功');
        } else {
          message.error('无法获取项目信息，请检查链接是否正确');
        }
      } else {
        message.error('系统错误：无法访问GitHub API');
      }
    } catch (error) {
      console.error('Failed to fetch repo info:', error);
      message.error('获取项目信息失败');
    } finally {
      setIsLoadingRepoInfo(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      // 验证是否已获取项目信息
      if (!values.owner || !values.name) {
        message.warning('请先点击"获取信息"按钮获取项目信息');
        return;
      }

      let updatedProjects: Project[];

      if (editingProject) {
        // 更新项目
        updatedProjects = projects.map(project =>
          project.id === editingProject.id
            ? {
                ...project,
                name: values.name,
                owner: values.owner,
                fullName: `${values.owner}/${values.name}`,
                url: values.projectUrl,
                description: values.description,
                categoryId: values.categoryId,
                isWatching: values.isWatching,
                isFavorite: values.isFavorite,
                updatedAt: new Date().toISOString(),
                metadata: {
                  ...project.metadata,
                  priority: values.priority,
                  notes: values.notes,
                },
              }
            : project
        );
      } else {
        // 创建项目
        const newProject: Project = {
          id: Date.now().toString(),
          name: values.name,
          fullName: `${values.owner}/${values.name}`,
          owner: values.owner,
          description: values.description,
          url: values.projectUrl,
          categoryId: values.categoryId,
          isActive: true,
          isWatching: values.isWatching,
          isFavorite: values.isFavorite,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            priority: values.priority,
            notes: values.notes,
          },
        };
        updatedProjects = [...projects, newProject];
      }

      // 保存到后端
      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success(editingProject ? '项目更新成功' : '项目创建成功');
          setIsModalVisible(false);
          form.resetFields();
        } else {
          throw new Error('保存失败');
        }
      } else {
        throw new Error('系统错误：无法访问数据API');
      }
    } catch (error) {
      console.error('Save project error:', error);
      message.error(editingProject ? '项目更新失败' : '项目创建失败');
    }
  };

  const toggleWatching = async (projectId: string, isWatching: boolean) => {
    try {
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? { ...project, isWatching, updatedAt: new Date().toISOString() }
          : project
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success(`已${isWatching ? '开启' : '关闭'}监控`);
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Toggle watching error:', error);
      message.error('操作失败');
    }
  };

  const toggleFavorite = async (projectId: string, isFavorite: boolean) => {
    try {
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? { ...project, isFavorite, updatedAt: new Date().toISOString() }
          : project
      );

      if (window.electronAPI) {
        const response = await window.electronAPI.data.setProjects(updatedProjects);
        if (response.success) {
          dispatch(setProjects(updatedProjects));
          message.success(`已${isFavorite ? '添加到' : '从'}收藏${isFavorite ? '' : '中移除'}`);
        } else {
          throw new Error('保存失败');
        }
      }
    } catch (error) {
      console.error('Toggle favorite error:', error);
      message.error('操作失败');
    }
  };

  const columns = [
    {
      title: '项目',
      dataIndex: 'name',
      key: 'name',
      width: 250,
      resizable: true,
      sorter: (a: Project, b: Project) => a.name.localeCompare(b.name),
      render: (text: string, record: Project) => (
        <Space>
          <ProjectOutlined />
          <div style={{ minWidth: 0, flex: 1 }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666', wordBreak: 'break-all' }}>
              {record.fullName}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 300,
      resizable: true,
      sorter: (a: Project, b: Project) => (a.description || '').localeCompare(b.description || ''),
      render: (text: string) => (
        <div style={{
          maxWidth: '280px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {text || '暂无描述'}
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      key: 'categoryId',
      width: 120,
      resizable: true,
      sorter: (a: Project, b: Project) => {
        const categoryA = categories.find(c => c.id === a.categoryId)?.name || '未分类';
        const categoryB = categories.find(c => c.id === b.categoryId)?.name || '未分类';
        return categoryA.localeCompare(categoryB);
      },
      render: (categoryId: string) => {
        const category = categories.find(c => c.id === categoryId);
        return category ? (
          <Tag color={category.color}>{category.name}</Tag>
        ) : (
          <Tag>未分类</Tag>
        );
      },
    },

    {
      title: '优先级',
      dataIndex: ['metadata', 'priority'],
      key: 'priority',
      width: 80,
      resizable: true,
      sorter: (a: Project, b: Project) => (b.metadata?.priority || 3) - (a.metadata?.priority || 3),
      render: (priority: number = 3) => (
        <div style={{ textAlign: 'center' }}>
          <Tag color={priority >= 4 ? 'red' : priority >= 3 ? 'orange' : priority >= 2 ? 'blue' : 'green'}>
            {priority}
          </Tag>
        </div>
      ),
    },

    {
      title: '是否更新',
      key: 'hasUpdate',
      width: 100,
      resizable: true,
      sorter: (a: Project, b: Project) => {
        const hasUpdateA = checkProjectNeedsReview(a) ? 1 : 0;
        const hasUpdateB = checkProjectNeedsReview(b) ? 1 : 0;
        return hasUpdateB - hasUpdateA; // 有更新且未读的在前
      },
      render: (_: any, record: Project) => {
        const hasUpdate = checkProjectNeedsReview(record);

        return (
          <div style={{ textAlign: 'center' }}>
            {hasUpdate ? (
              <Tag color="orange">有更新</Tag>
            ) : (
              <Tag color="green">无更新</Tag>
            )}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 320,
      fixed: 'right' as const,
      render: (_: any, record: Project) => {
        // 使用辅助函数检查是否需要重新查看
        const needsReview = checkProjectNeedsReview(record);

        // 按钮样式：金色表示需要查看，灰色表示已查看且无更新
        const shouldShowGold = needsReview;

        return (
          <Space size="small">
            <Button
              type={shouldShowGold ? "primary" : "default"}
              icon={<CheckOutlined />}
              onClick={() => handleMarkAsRead(record.id)}
              size="small"
              style={{
                backgroundColor: shouldShowGold ? '#faad14' : '#d9d9d9',
                borderColor: shouldShowGold ? '#faad14' : '#d9d9d9',
                color: shouldShowGold ? '#fff' : '#666'
              }}
            >
              已阅
            </Button>
            <Button
              type="primary"
              icon={<LinkOutlined />}
              onClick={() => handleOpenProject(record)}
              size="small"
            >
              打开
            </Button>
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这个项目吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<DeleteOutlined />} size="small">
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Title level={3} style={{ margin: 0 }}>项目管理</Title>
            <Select
              value={selectedCategoryFilter}
              onChange={setSelectedCategoryFilter}
              style={{ width: 200 }}
              placeholder="选择分类筛选"
            >
              <Select.Option value="all">所有分类</Select.Option>
              {categories
                .filter(category => category.type === CategoryType.PROJECT)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    <Tag color={category.color} style={{ margin: 0 }}>{category.name}</Tag>
                  </Select.Option>
                ))}
            </Select>
          </div>
          <Space>
            <Button
              onClick={testTimestampLogic}
              size="small"
              style={{ backgroundColor: '#ff9800', borderColor: '#ff9800', color: '#fff' }}
            >
              测试时间戳
            </Button>
            <Button
              onClick={showCurrentState}
              size="small"
              style={{ backgroundColor: '#1890ff', borderColor: '#1890ff', color: '#fff' }}
            >
              显示状态
            </Button>
            <Button
              onClick={simulateProjectUpdate}
              size="small"
              style={{ backgroundColor: '#52c41a', borderColor: '#52c41a', color: '#fff' }}
            >
              模拟更新
            </Button>
            <Button
              onClick={forceRefreshData}
              size="small"
              style={{ backgroundColor: '#722ed1', borderColor: '#722ed1', color: '#fff' }}
            >
              刷新数据
            </Button>
            <Button
              onClick={() => {
                localStorage.removeItem('read-projects');
                setReadProjects(new Set());
                setReadProjectsTimestamps({});
                console.log('已清除 read-projects localStorage 和状态');
                message.success('已清除所有已读状态');
              }}
              size="small"
              style={{ backgroundColor: '#f50', borderColor: '#f50', color: '#fff' }}
            >
              清除已读
            </Button>
            <Button
              onClick={() => {
                localStorage.removeItem('table-widths-projects');
                window.location.reload();
              }}
              size="small"
            >
              重置列宽
            </Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加项目
            </Button>
          </Space>
        </div>

        <ResizableTable
          tableKey="projects"
          columns={columns}
          dataSource={sortedProjects}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200, y: 'calc(100vh - 300px)' }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          size="middle"
        />
      </Card>

      <Modal
        title={editingProject ? '编辑项目' : '添加项目'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isWatching: true,
            isFavorite: false,
            priority: 3,
          }}
        >
          <Form.Item
            name="projectUrl"
            label="项目链接"
            rules={[{ required: true, message: '请输入GitHub项目链接' }]}
            extra="输入GitHub项目链接，例如：https://github.com/facebook/react"
          >
            <Input
              placeholder="https://github.com/owner/repo"
              addonAfter={
                <Button
                  type="primary"
                  loading={isLoadingRepoInfo}
                  onClick={handleFetchRepoInfo}
                  size="small"
                >
                  获取信息
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            name="owner"
            label="项目所有者"
            rules={[{ required: true, message: '请输入项目所有者' }]}
          >
            <Input placeholder="例如：facebook" disabled />
          </Form.Item>

          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="例如：react" disabled />
          </Form.Item>

          <Form.Item name="description" label="项目描述">
            <TextArea rows={3} placeholder="项目描述（可选）" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择项目分类">
              {categories
                .filter(category => category.type === CategoryType.PROJECT)
                .map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    {category.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item name="priority" label="优先级">
            <Rate count={5} />
          </Form.Item>

          <Space>
            <Form.Item name="isWatching" label="监控状态" valuePropName="checked">
              <Switch checkedChildren="监控" unCheckedChildren="不监控" />
            </Form.Item>

            <Form.Item name="isFavorite" label="收藏" valuePropName="checked">
              <Switch checkedChildren="收藏" unCheckedChildren="不收藏" />
            </Form.Item>
          </Space>

          <Form.Item name="notes" label="备注">
            <TextArea rows={3} placeholder="备注信息（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingProject ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Projects;
