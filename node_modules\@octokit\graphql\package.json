{"name": "@octokit/graphql", "version": "9.0.1", "publishConfig": {"access": "public", "provenance": true}, "type": "module", "description": "GitHub GraphQL API client for browsers and Node", "repository": "github:octokit/graphql.js", "keywords": ["octokit", "github", "api", "graphql"], "author": "<PERSON> (https://github.com/gr2m)", "license": "MIT", "dependencies": {"@octokit/request": "^10.0.2", "@octokit/types": "^14.0.0", "universal-user-agent": "^7.0.0"}, "devDependencies": {"@octokit/tsconfig": "^4.0.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^3.0.0", "esbuild": "^0.25.0", "fetch-mock": "^12.0.0", "glob": "^11.0.0", "prettier": "3.5.3", "semantic-release-plugin-update-version-in-files": "^2.0.0", "typescript": "^5.3.0", "vitest": "^3.0.0"}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "./dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-bundle/index.js", "default": "./dist-bundle/index.js"}, "./types": {"types": "./dist-types/types.d.ts"}}, "sideEffects": false}