/**
 * 可调整列宽的表格组件
 */

import React, { useState } from 'react';
import { Table } from 'antd';
import { Resizable } from 'react-resizable';
import type { ColumnsType } from 'antd/es/table';
import 'react-resizable/css/styles.css';
import '@/renderer/styles/resizable-table.css';

interface ResizableTableProps {
  columns: ColumnsType<any>;
  dataSource: any[];
  [key: string]: any;
}

const ResizableTitle = (props: any) => {
  const { onResize, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

const ResizableTable: React.FC<ResizableTableProps> = ({ columns, ...props }) => {
  const [tableColumns, setTableColumns] = useState(columns);

  const handleResize = (index: number) => (e: any, { size }: any) => {
    const newColumns = [...tableColumns];
    newColumns[index] = {
      ...newColumns[index],
      width: size.width,
    };
    setTableColumns(newColumns);
  };

  const resizableColumns = tableColumns.map((col: any, index: number) => ({
    ...col,
    onHeaderCell: (column: any) => ({
      width: column.width,
      onResize: handleResize(index),
    }),
  }));

  const components = {
    header: {
      cell: ResizableTitle,
    },
  };

  return (
    <Table
      {...props}
      columns={resizableColumns}
      components={components}
      className="resizable-table"
    />
  );
};

export default ResizableTable;
