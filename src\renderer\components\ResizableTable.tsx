/**
 * 可调整列宽的表格组件
 */

import React, { useState, useEffect } from 'react';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import '@/renderer/styles/resizable-table.css';

interface ResizableTableProps {
  columns: ColumnsType<any>;
  dataSource: any[];
  tableKey: string; // 用于区分不同表格的唯一标识
  onResetWidths?: () => void; // 重置列宽的回调
  [key: string]: any;
}

const ResizableTitle = (props: any) => {
  const { onResize, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <th {...restProps} style={{ ...restProps.style, width, minWidth: width, maxWidth: width, position: 'relative' }}>
      {restProps.children}
      <div
        className="custom-resize-handle"
        onMouseDown={(e) => {
          e.preventDefault();
          e.stopPropagation();

          const startX = e.clientX;
          const startWidth = width;

          const handleMouseMove = (moveEvent: MouseEvent) => {
            const deltaX = moveEvent.clientX - startX;
            const newWidth = Math.max(50, Math.min(800, startWidth + deltaX));
            onResize(null, { size: { width: newWidth } });
          };

          const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
          };

          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
          document.body.style.cursor = 'col-resize';
          document.body.style.userSelect = 'none';
        }}
      />
    </th>
  );
};

const ResizableTable: React.FC<ResizableTableProps> = ({ columns, tableKey, onResetWidths, ...props }) => {
  const [tableColumns, setTableColumns] = useState(columns);

  // 从localStorage加载列宽配置
  useEffect(() => {
    const savedWidths = localStorage.getItem(`table-widths-${tableKey}`);
    if (savedWidths) {
      try {
        const widthsMap = JSON.parse(savedWidths);
        const updatedColumns = columns.map((col: any) => ({
          ...col,
          width: widthsMap[col.key] || col.width,
        }));
        setTableColumns(updatedColumns);
      } catch (error) {
        console.error('Failed to load table widths:', error);
        setTableColumns(columns);
      }
    } else {
      // 确保所有列都有默认宽度
      const newColumns = columns.map((col: any) => ({
        ...col,
        width: col.width || 150,
      }));
      setTableColumns(newColumns);
    }
  }, [columns, tableKey]);

  // 保存列宽到localStorage
  const saveColumnWidths = (newColumns: any[]) => {
    const widthsMap: Record<string, number> = {};
    newColumns.forEach((col: any) => {
      if (col.key && col.width) {
        widthsMap[col.key] = col.width;
      }
    });
    localStorage.setItem(`table-widths-${tableKey}`, JSON.stringify(widthsMap));
  };

  // 重置列宽
  const resetColumnWidths = () => {
    localStorage.removeItem(`table-widths-${tableKey}`);
    setTableColumns(columns);
    if (onResetWidths) {
      onResetWidths();
    }
  };

  // 暴露重置方法给父组件
  React.useImperativeHandle(onResetWidths, () => ({
    resetWidths: resetColumnWidths,
  }));

  const handleResize = (index: number) => (e: any, { size }: any) => {
    const newColumns = [...tableColumns];
    newColumns[index] = {
      ...newColumns[index],
      width: Math.max(size.width, 50), // 设置最小宽度为50px
    };
    setTableColumns(newColumns);
    saveColumnWidths(newColumns);
  };

  const resizableColumns = tableColumns.map((col: any, index: number) => ({
    ...col,
    onHeaderCell: (column: any) => ({
      width: column.width,
      onResize: handleResize(index),
    }),
  }));

  const components = {
    header: {
      cell: ResizableTitle,
    },
  };

  // 计算表格总宽度
  const totalWidth = tableColumns.reduce((sum, col: any) => sum + (col.width || 150), 0);

  return (
    <div style={{ width: '100%', overflow: 'auto' }}>
      <Table
        {...props}
        columns={resizableColumns}
        components={components}
        className="resizable-table"
        scroll={{
          x: totalWidth, // 设置水平滚动宽度
          y: undefined // 保持垂直滚动为默认
        }}
        tableLayout="fixed" // 使用固定布局，防止列宽自动调整
        style={{ minWidth: totalWidth }} // 确保表格有最小宽度
      />
    </div>
  );
};

export default ResizableTable;
