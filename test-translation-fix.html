<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .result {
            background-color: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 3px;
            border-left: 4px solid #1890ff;
        }
        .success {
            border-left-color: #52c41a;
            background-color: #f6ffed;
        }
        .error {
            border-left-color: #ff4d4f;
            background-color: #fff2f0;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .loading {
            color: #999;
            font-style: italic;
        }
        .text-sample {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
        }
        h3 {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 翻译功能修复测试</h1>
    
    <div class="test-section">
        <h3>🌐 语言检测测试</h3>
        <p>测试智能语言检测功能是否正常工作：</p>
        
        <div class="text-sample">
            <strong>中文文本：</strong>这是一个基于React开发的项目监控工具
        </div>
        <div class="text-sample">
            <strong>英文文本：</strong>Claude-Flow represents a revolutionary leap in AI-powered development
        </div>
        <div class="text-sample">
            <strong>日文文本：</strong>これはテストテキストです
        </div>
        
        <button onclick="testLanguageDetection()">测试语言检测</button>
        <div id="detection-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>🔄 双向翻译测试</h3>
        <p>测试中文↔英文双向翻译：</p>
        
        <button onclick="testTranslation('zh', 'en')">中文 → 英文</button>
        <button onclick="testTranslation('en', 'zh')">英文 → 中文</button>
        <button onclick="testTranslation('zh', 'ja')">中文 → 日文</button>
        <button onclick="testTranslation('en', 'ja')">英文 → 日文</button>
        
        <div id="translation-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>🌍 MyMemory API 实际测试</h3>
        <p>测试真实的MyMemory翻译API：</p>
        
        <button onclick="testRealAPI('en', 'zh')">英文 → 中文 (API)</button>
        <button onclick="testRealAPI('zh', 'en')">中文 → 英文 (API)</button>
        <button onclick="testRealAPI('en', 'ja')">英文 → 日文 (API)</button>
        
        <div id="api-result" class="result" style="display: none;"></div>
    </div>

    <script>
        // 模拟语言检测函数
        function detectSourceLanguage(text) {
            const chineseRegex = /[\u4e00-\u9fff]/;
            const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
            const koreanRegex = /[\uac00-\ud7af]/;
            const englishRegex = /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/;

            if (chineseRegex.test(text)) {
                return 'zh-CN';
            } else if (japaneseRegex.test(text)) {
                return 'ja';
            } else if (koreanRegex.test(text)) {
                return 'ko';
            } else if (englishRegex.test(text)) {
                return 'en';
            } else {
                return 'auto';
            }
        }

        function testLanguageDetection() {
            const resultDiv = document.getElementById('detection-result');
            resultDiv.style.display = 'block';
            
            const testTexts = [
                '这是一个基于React开发的项目监控工具',
                'Claude-Flow represents a revolutionary leap in AI-powered development',
                'これはテストテキストです'
            ];
            
            let results = '<h4>语言检测结果：</h4>';
            testTexts.forEach(text => {
                const detected = detectSourceLanguage(text);
                results += `<p><strong>文本：</strong>${text}<br><strong>检测结果：</strong>${detected}</p>`;
            });
            
            resultDiv.innerHTML = results;
            resultDiv.className = 'result success';
        }

        async function testTranslation(from, to) {
            const resultDiv = document.getElementById('translation-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<div class="loading">翻译中...</div>';
            
            const testTexts = {
                'zh': '这是一个基于React和TypeScript开发的GitHub项目监控工具',
                'en': 'Claude-Flow represents a revolutionary leap in AI-powered development orchestration'
            };
            
            const text = testTexts[from];
            
            try {
                // 模拟翻译结果
                const translations = {
                    'zh-en': 'This is a GitHub project monitoring tool developed based on React and TypeScript',
                    'en-zh': 'Claude-Flow代表了AI驱动开发编排的革命性飞跃',
                    'zh-ja': 'これは、ReactとTypeScriptに基づいて開発されたGitHubプロジェクト監視ツールです',
                    'en-ja': 'Claude-Flowは、AI駆動開発オーケストレーションにおける革命的な飛躍を表しています'
                };
                
                const key = `${from}-${to}`;
                const result = translations[key] || `[模拟翻译] ${text}`;
                
                resultDiv.innerHTML = `
                    <h4>翻译结果：</h4>
                    <p><strong>原文 (${from}):</strong><br>${text}</p>
                    <p><strong>译文 (${to}):</strong><br>${result}</p>
                    <p><strong>检测到的源语言:</strong> ${detectSourceLanguage(text)}</p>
                `;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">翻译失败: ${error.message}</div>`;
                resultDiv.className = 'result error';
            }
        }

        async function testRealAPI(from, to) {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<div class="loading">调用API中...</div>';
            
            const testTexts = {
                'zh': '这是一个测试文本',
                'en': 'This is a test text'
            };
            
            const text = testTexts[from];
            const sourceLang = from === 'zh' ? 'zh-CN' : from;
            const targetLang = to === 'zh' ? 'zh-CN' : to;
            
            try {
                const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${sourceLang}|${targetLang}`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.responseStatus === 200 && data.responseData) {
                    resultDiv.innerHTML = `
                        <h4>MyMemory API 测试结果：</h4>
                        <p><strong>原文:</strong> ${text}</p>
                        <p><strong>译文:</strong> ${data.responseData.translatedText}</p>
                        <p><strong>语言对:</strong> ${sourceLang} → ${targetLang}</p>
                        <p><strong>匹配度:</strong> ${data.responseData.match}</p>
                        <details>
                            <summary>完整API响应</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error('API返回错误状态');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h4>API测试失败：</h4>
                    <p style="color: red;">${error.message}</p>
                    <p>这可能是由于网络问题或API限制导致的。</p>
                `;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
