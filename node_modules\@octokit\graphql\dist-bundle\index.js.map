{"version": 3, "sources": ["../dist-src/index.js", "../dist-src/version.js", "../dist-src/with-defaults.js", "../dist-src/graphql.js", "../dist-src/error.js"], "sourcesContent": ["import { request } from \"@octokit/request\";\nimport { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version.js\";\nimport { withDefaults } from \"./with-defaults.js\";\nconst graphql = withDefaults(request, {\n  headers: {\n    \"user-agent\": `octokit-graphql.js/${VERSION} ${getUserAgent()}`\n  },\n  method: \"POST\",\n  url: \"/graphql\"\n});\nimport { GraphqlResponseError } from \"./error.js\";\nfunction withCustomRequest(customRequest) {\n  return withDefaults(customRequest, {\n    method: \"POST\",\n    url: \"/graphql\"\n  });\n}\nexport {\n  GraphqlResponseError,\n  graphql,\n  withCustomRequest\n};\n", "const VERSION = \"0.0.0-development\";\nexport {\n  VERSION\n};\n", "import { request as Request } from \"@octokit/request\";\nimport { graphql } from \"./graphql.js\";\nfunction withDefaults(request, newDefaults) {\n  const newRequest = request.defaults(newDefaults);\n  const newApi = (query, options) => {\n    return graphql(newRequest, query, options);\n  };\n  return Object.assign(newApi, {\n    defaults: withDefaults.bind(null, newRequest),\n    endpoint: newRequest.endpoint\n  });\n}\nexport {\n  withDefaults\n};\n", "import { request as Request } from \"@octokit/request\";\nimport { GraphqlResponseError } from \"./error.js\";\nconst NON_VARIABLE_OPTIONS = [\n  \"method\",\n  \"baseUrl\",\n  \"url\",\n  \"headers\",\n  \"request\",\n  \"query\",\n  \"mediaType\",\n  \"operationName\"\n];\nconst FORBIDDEN_VARIABLE_OPTIONS = [\"query\", \"method\", \"url\"];\nconst GHES_V3_SUFFIX_REGEX = /\\/api\\/v3\\/?$/;\nfunction graphql(request, query, options) {\n  if (options) {\n    if (typeof query === \"string\" && \"query\" in options) {\n      return Promise.reject(\n        new Error(`[@octokit/graphql] \"query\" cannot be used as variable name`)\n      );\n    }\n    for (const key in options) {\n      if (!FORBIDDEN_VARIABLE_OPTIONS.includes(key)) continue;\n      return Promise.reject(\n        new Error(\n          `[@octokit/graphql] \"${key}\" cannot be used as variable name`\n        )\n      );\n    }\n  }\n  const parsedOptions = typeof query === \"string\" ? Object.assign({ query }, options) : query;\n  const requestOptions = Object.keys(\n    parsedOptions\n  ).reduce((result, key) => {\n    if (NON_VARIABLE_OPTIONS.includes(key)) {\n      result[key] = parsedOptions[key];\n      return result;\n    }\n    if (!result.variables) {\n      result.variables = {};\n    }\n    result.variables[key] = parsedOptions[key];\n    return result;\n  }, {});\n  const baseUrl = parsedOptions.baseUrl || request.endpoint.DEFAULTS.baseUrl;\n  if (GHES_V3_SUFFIX_REGEX.test(baseUrl)) {\n    requestOptions.url = baseUrl.replace(GHES_V3_SUFFIX_REGEX, \"/api/graphql\");\n  }\n  return request(requestOptions).then((response) => {\n    if (response.data.errors) {\n      const headers = {};\n      for (const key of Object.keys(response.headers)) {\n        headers[key] = response.headers[key];\n      }\n      throw new GraphqlResponseError(\n        requestOptions,\n        headers,\n        response.data\n      );\n    }\n    return response.data.data;\n  });\n}\nexport {\n  graphql\n};\n", "function _buildMessageForResponseErrors(data) {\n  return `Request failed due to following response errors:\n` + data.errors.map((e) => ` - ${e.message}`).join(\"\\n\");\n}\nclass GraphqlResponseError extends Error {\n  constructor(request, headers, response) {\n    super(_buildMessageForResponseErrors(response));\n    this.request = request;\n    this.headers = headers;\n    this.response = response;\n    this.errors = response.errors;\n    this.data = response.data;\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n  name = \"GraphqlResponseError\";\n  errors;\n  data;\n}\nexport {\n  GraphqlResponseError\n};\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,oBAAoB;;;ACD7B,IAAM,UAAU;;;ACAhB,SAAS,WAAWA,gBAAe;;;ACAnC,SAAS,WAAW,eAAe;;;ACAnC,SAAS,+BAA+B,MAAM;AAC5C,SAAO;AAAA,IACL,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,IAAI;AACvD;AACA,IAAM,uBAAN,cAAmC,MAAM;AAAA,EACvC,YAAYC,UAAS,SAAS,UAAU;AACtC,UAAM,+BAA+B,QAAQ,CAAC;AAC9C,SAAK,UAAUA;AACf,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS,SAAS;AACvB,SAAK,OAAO,SAAS;AACrB,QAAI,MAAM,mBAAmB;AAC3B,YAAM,kBAAkB,MAAM,KAAK,WAAW;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AACF;;;ADjBA,IAAM,uBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,6BAA6B,CAAC,SAAS,UAAU,KAAK;AAC5D,IAAM,uBAAuB;AAC7B,SAAS,QAAQC,UAAS,OAAO,SAAS;AACxC,MAAI,SAAS;AACX,QAAI,OAAO,UAAU,YAAY,WAAW,SAAS;AACnD,aAAO,QAAQ;AAAA,QACb,IAAI,MAAM,4DAA4D;AAAA,MACxE;AAAA,IACF;AACA,eAAW,OAAO,SAAS;AACzB,UAAI,CAAC,2BAA2B,SAAS,GAAG,EAAG;AAC/C,aAAO,QAAQ;AAAA,QACb,IAAI;AAAA,UACF,uBAAuB,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,OAAO,UAAU,WAAW,OAAO,OAAO,EAAE,MAAM,GAAG,OAAO,IAAI;AACtF,QAAM,iBAAiB,OAAO;AAAA,IAC5B;AAAA,EACF,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACxB,QAAI,qBAAqB,SAAS,GAAG,GAAG;AACtC,aAAO,GAAG,IAAI,cAAc,GAAG;AAC/B,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,WAAW;AACrB,aAAO,YAAY,CAAC;AAAA,IACtB;AACA,WAAO,UAAU,GAAG,IAAI,cAAc,GAAG;AACzC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,QAAM,UAAU,cAAc,WAAWA,SAAQ,SAAS,SAAS;AACnE,MAAI,qBAAqB,KAAK,OAAO,GAAG;AACtC,mBAAe,MAAM,QAAQ,QAAQ,sBAAsB,cAAc;AAAA,EAC3E;AACA,SAAOA,SAAQ,cAAc,EAAE,KAAK,CAAC,aAAa;AAChD,QAAI,SAAS,KAAK,QAAQ;AACxB,YAAM,UAAU,CAAC;AACjB,iBAAW,OAAO,OAAO,KAAK,SAAS,OAAO,GAAG;AAC/C,gBAAQ,GAAG,IAAI,SAAS,QAAQ,GAAG;AAAA,MACrC;AACA,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB,CAAC;AACH;;;AD5DA,SAAS,aAAaC,UAAS,aAAa;AAC1C,QAAM,aAAaA,SAAQ,SAAS,WAAW;AAC/C,QAAM,SAAS,CAAC,OAAO,YAAY;AACjC,WAAO,QAAQ,YAAY,OAAO,OAAO;AAAA,EAC3C;AACA,SAAO,OAAO,OAAO,QAAQ;AAAA,IAC3B,UAAU,aAAa,KAAK,MAAM,UAAU;AAAA,IAC5C,UAAU,WAAW;AAAA,EACvB,CAAC;AACH;;;AFPA,IAAMC,WAAU,aAAa,SAAS;AAAA,EACpC,SAAS;AAAA,IACP,cAAc,sBAAsB,OAAO,IAAI,aAAa,CAAC;AAAA,EAC/D;AAAA,EACA,QAAQ;AAAA,EACR,KAAK;AACP,CAAC;AAED,SAAS,kBAAkB,eAAe;AACxC,SAAO,aAAa,eAAe;AAAA,IACjC,QAAQ;AAAA,IACR,KAAK;AAAA,EACP,CAAC;AACH;", "names": ["Request", "request", "request", "request", "graphql"]}