/**
 * 设置页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Typography,
  Divider,
  InputNumber,
  message,
  Tabs,
} from 'antd';
import {
  SettingOutlined,
  GithubOutlined,
  BgColorsOutlined,
  SyncOutlined,
  SecurityScanOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { selectSettings, updateSettings } from '@/renderer/store/slices/settingsSlice';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;
const { Password } = Input;

const Settings: React.FC = () => {
  const dispatch = useAppDispatch();
  const settings = useAppSelector(selectSettings);
  const [generalForm] = Form.useForm();
  const [githubForm] = Form.useForm();
  const [uiForm] = Form.useForm();
  const [syncForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    generalForm.setFieldsValue(settings);
    githubForm.setFieldsValue(settings);
    uiForm.setFieldsValue(settings);
    syncForm.setFieldsValue(settings);
  }, [settings, generalForm, githubForm, uiForm, syncForm]);

  const handleSave = async (values: any, formType: string) => {
    setLoading(true);
    try {
      // 合并当前设置和新值
      const updatedSettings = {
        ...settings,
        ...values,
      };

      // 保存到Redux store
      dispatch(updateSettings(updatedSettings));

      // 保存到本地存储或发送到主进程
      if (window.electronAPI) {
        await window.electronAPI.data.setConfig(updatedSettings);
      }

      message.success(`${formType}设置保存成功`);
    } catch (error) {
      console.error('设置保存失败:', error);
      message.error(`${formType}设置保存失败`);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = (form: any, formType: string) => {
    form.resetFields();
    message.info(`${formType}设置已重置`);
  };

  const generalSettings = (
    <Card>
      <Form
        form={generalForm}
        layout="vertical"
        initialValues={settings}
        onFinish={(values) => handleSave(values, '常规')}
      >
        <Title level={4}>基础设置</Title>

        <Form.Item name={['general', 'language']} label="语言">
          <Select>
            <Select.Option value="zh">中文</Select.Option>
            <Select.Option value="en">English</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name={['general', 'theme']} label="主题">
          <Select>
            <Select.Option value="light">浅色</Select.Option>
            <Select.Option value="dark">深色</Select.Option>
            <Select.Option value="auto">跟随系统</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name={['general', 'startWithSystem']} label="开机启动" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['general', 'minimizeToTray']} label="最小化到托盘" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['general', 'autoUpdate']} label="自动更新" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Divider />

        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            保存设置
          </Button>
          <Button onClick={() => handleReset(generalForm, '常规')}>
            重置
          </Button>
        </Space>
      </Form>
    </Card>
  );

  const githubSettings = (
    <Card>
      <Form
        form={githubForm}
        layout="vertical"
        initialValues={settings}
        onFinish={(values) => handleSave(values, 'GitHub')}
      >
        <Title level={4}>GitHub 设置</Title>
        <Paragraph type="secondary">
          配置 GitHub API 访问令牌以获取更高的请求限制和访问私有仓库。
        </Paragraph>

        <Form.Item
          name={['github', 'token']}
          label="GitHub Token"
          extra="在 GitHub Settings > Developer settings > Personal access tokens 中创建"
        >
          <Password placeholder="ghp_xxxxxxxxxxxxxxxxxxxx" />
        </Form.Item>

        <Form.Item name={['github', 'apiUrl']} label="API 地址">
          <Input placeholder="https://api.github.com" />
        </Form.Item>

        <Form.Item name={['github', 'timeout']} label="请求超时 (秒)">
          <InputNumber min={5} max={300} />
        </Form.Item>

        <Form.Item name={['github', 'retryAttempts']} label="重试次数">
          <InputNumber min={0} max={10} />
        </Form.Item>

        <Title level={5}>速率限制</Title>

        <Form.Item name={['github', 'rateLimit', 'enabled']} label="启用速率限制" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['github', 'rateLimit', 'requestsPerHour']} label="每小时请求数">
          <InputNumber min={100} max={10000} />
        </Form.Item>

        <Divider />

        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            保存设置
          </Button>
          <Button onClick={() => handleReset(githubForm, 'GitHub')}>
            重置
          </Button>
        </Space>
      </Form>
    </Card>
  );

  const uiSettings = (
    <Card>
      <Form
        form={uiForm}
        layout="vertical"
        initialValues={settings}
        onFinish={(values) => handleSave(values, '界面')}
      >
        <Title level={4}>界面设置</Title>

        <Title level={5}>主题颜色</Title>
        
        <Form.Item name={['ui', 'theme', 'primaryColor']} label="主色调">
          <Input type="color" />
        </Form.Item>

        <Form.Item name={['ui', 'theme', 'accentColor']} label="强调色">
          <Input type="color" />
        </Form.Item>

        <Title level={5}>布局设置</Title>

        <Form.Item name={['ui', 'layout', 'sidebarWidth']} label="侧边栏宽度">
          <InputNumber min={200} max={400} addonAfter="px" />
        </Form.Item>

        <Form.Item name={['ui', 'layout', 'compactMode']} label="紧凑模式" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['ui', 'layout', 'showStatusBar']} label="显示状态栏" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Title level={5}>字体设置</Title>

        <Form.Item name={['ui', 'typography', 'fontSize']} label="字体大小">
          <Select>
            <Select.Option value="small">小</Select.Option>
            <Select.Option value="medium">中</Select.Option>
            <Select.Option value="large">大</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name={['ui', 'typography', 'fontFamily']} label="字体">
          <Select>
            <Select.Option value="system-ui">系统默认</Select.Option>
            <Select.Option value="Arial">Arial</Select.Option>
            <Select.Option value="Helvetica">Helvetica</Select.Option>
          </Select>
        </Form.Item>

        <Divider />

        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            保存设置
          </Button>
          <Button onClick={() => handleReset(uiForm, '界面')}>
            重置
          </Button>
        </Space>
      </Form>
    </Card>
  );

  const syncSettings = (
    <Card>
      <Form
        form={syncForm}
        layout="vertical"
        initialValues={settings}
        onFinish={(values) => handleSave(values, '同步')}
      >
        <Title level={4}>同步设置</Title>

        <Form.Item name={['sync', 'enabled']} label="启用自动同步" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['sync', 'interval']} label="同步间隔 (分钟)">
          <InputNumber min={5} max={1440} />
        </Form.Item>

        <Form.Item name={['sync', 'autoSync']} label="启动时自动同步" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['sync', 'batchSize']} label="批处理大小">
          <InputNumber min={10} max={100} />
        </Form.Item>

        <Form.Item name={['sync', 'concurrent']} label="并发数">
          <InputNumber min={1} max={10} />
        </Form.Item>

        <Title level={5}>过滤设置</Title>

        <Form.Item name={['sync', 'filters', 'excludeArchived']} label="排除已归档项目" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['sync', 'filters', 'excludeForks']} label="排除分叉项目" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name={['sync', 'filters', 'minStars']} label="最小星标数">
          <InputNumber min={0} />
        </Form.Item>

        <Divider />

        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            保存设置
          </Button>
          <Button onClick={() => handleReset(syncForm, '同步')}>
            重置
          </Button>
        </Space>
      </Form>
    </Card>
  );

  const tabItems = [
    {
      key: 'general',
      label: '常规',
      icon: <SettingOutlined />,
      children: generalSettings,
    },
    {
      key: 'github',
      label: 'GitHub',
      icon: <GithubOutlined />,
      children: githubSettings,
    },
    {
      key: 'ui',
      label: '界面',
      icon: <BgColorsOutlined />,
      children: uiSettings,
    },
    {
      key: 'sync',
      label: '同步',
      icon: <SyncOutlined />,
      children: syncSettings,
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      <Card>
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <SettingOutlined style={{ fontSize: '48px', color: '#f5222d', marginBottom: '16px' }} />
          <Title level={2}>设置</Title>
        </div>

        <Tabs
          defaultActiveKey="general"
          items={tabItems}
          tabPosition="left"
        />
      </Card>
    </div>
  );
};

export default Settings;
