{"version": 3, "sources": ["../dist-src/defaults.js", "../dist-src/version.js", "../dist-src/util/lowercase-keys.js", "../dist-src/util/is-plain-object.js", "../dist-src/util/merge-deep.js", "../dist-src/util/remove-undefined-properties.js", "../dist-src/merge.js", "../dist-src/util/add-query-parameters.js", "../dist-src/util/extract-url-variable-names.js", "../dist-src/util/omit.js", "../dist-src/util/url-template.js", "../dist-src/parse.js", "../dist-src/endpoint-with-defaults.js", "../dist-src/with-defaults.js", "../dist-src/index.js"], "sourcesContent": ["import { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version.js\";\nconst userAgent = `octokit-endpoint.js/${VERSION} ${getUserAgent()}`;\nconst DEFAULTS = {\n  method: \"GET\",\n  baseUrl: \"https://api.github.com\",\n  headers: {\n    accept: \"application/vnd.github.v3+json\",\n    \"user-agent\": userAgent\n  },\n  mediaType: {\n    format: \"\"\n  }\n};\nexport {\n  DEFAULTS\n};\n", "const VERSION = \"0.0.0-development\";\nexport {\n  VERSION\n};\n", "function lowercaseKeys(object) {\n  if (!object) {\n    return {};\n  }\n  return Object.keys(object).reduce((newObj, key) => {\n    newObj[key.toLowerCase()] = object[key];\n    return newObj;\n  }, {});\n}\nexport {\n  lowercaseKeys\n};\n", "function isPlainObject(value) {\n  if (typeof value !== \"object\" || value === null) return false;\n  if (Object.prototype.toString.call(value) !== \"[object Object]\") return false;\n  const proto = Object.getPrototypeOf(value);\n  if (proto === null) return true;\n  const Ctor = Object.prototype.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  return typeof Ctor === \"function\" && Ctor instanceof Ctor && Function.prototype.call(Ctor) === Function.prototype.call(value);\n}\nexport {\n  isPlainObject\n};\n", "import { isPlainObject } from \"./is-plain-object.js\";\nfunction mergeDeep(defaults, options) {\n  const result = Object.assign({}, defaults);\n  Object.keys(options).forEach((key) => {\n    if (isPlainObject(options[key])) {\n      if (!(key in defaults)) Object.assign(result, { [key]: options[key] });\n      else result[key] = mergeDeep(defaults[key], options[key]);\n    } else {\n      Object.assign(result, { [key]: options[key] });\n    }\n  });\n  return result;\n}\nexport {\n  mergeDeep\n};\n", "function removeUndefinedProperties(obj) {\n  for (const key in obj) {\n    if (obj[key] === void 0) {\n      delete obj[key];\n    }\n  }\n  return obj;\n}\nexport {\n  removeUndefinedProperties\n};\n", "import { lowercaseKeys } from \"./util/lowercase-keys.js\";\nimport { mergeDeep } from \"./util/merge-deep.js\";\nimport { removeUndefinedProperties } from \"./util/remove-undefined-properties.js\";\nfunction merge(defaults, route, options) {\n  if (typeof route === \"string\") {\n    let [method, url] = route.split(\" \");\n    options = Object.assign(url ? { method, url } : { url: method }, options);\n  } else {\n    options = Object.assign({}, route);\n  }\n  options.headers = lowercaseKeys(options.headers);\n  removeUndefinedProperties(options);\n  removeUndefinedProperties(options.headers);\n  const mergedOptions = mergeDeep(defaults || {}, options);\n  if (options.url === \"/graphql\") {\n    if (defaults && defaults.mediaType.previews?.length) {\n      mergedOptions.mediaType.previews = defaults.mediaType.previews.filter(\n        (preview) => !mergedOptions.mediaType.previews.includes(preview)\n      ).concat(mergedOptions.mediaType.previews);\n    }\n    mergedOptions.mediaType.previews = (mergedOptions.mediaType.previews || []).map((preview) => preview.replace(/-preview/, \"\"));\n  }\n  return mergedOptions;\n}\nexport {\n  merge\n};\n", "function addQueryParameters(url, parameters) {\n  const separator = /\\?/.test(url) ? \"&\" : \"?\";\n  const names = Object.keys(parameters);\n  if (names.length === 0) {\n    return url;\n  }\n  return url + separator + names.map((name) => {\n    if (name === \"q\") {\n      return \"q=\" + parameters.q.split(\"+\").map(encodeURIComponent).join(\"+\");\n    }\n    return `${name}=${encodeURIComponent(parameters[name])}`;\n  }).join(\"&\");\n}\nexport {\n  addQueryParameters\n};\n", "const urlVariableRegex = /\\{[^{}}]+\\}/g;\nfunction removeNonChars(variableName) {\n  return variableName.replace(/(?:^\\W+)|(?:(?<!\\W)\\W+$)/g, \"\").split(/,/);\n}\nfunction extractUrlVariableNames(url) {\n  const matches = url.match(urlVariableRegex);\n  if (!matches) {\n    return [];\n  }\n  return matches.map(removeNonChars).reduce((a, b) => a.concat(b), []);\n}\nexport {\n  extractUrlVariableNames\n};\n", "function omit(object, keysToOmit) {\n  const result = { __proto__: null };\n  for (const key of Object.keys(object)) {\n    if (keysToOmit.indexOf(key) === -1) {\n      result[key] = object[key];\n    }\n  }\n  return result;\n}\nexport {\n  omit\n};\n", "function encodeReserved(str) {\n  return str.split(/(%[0-9A-Fa-f]{2})/g).map(function(part) {\n    if (!/%[0-9A-Fa-f]/.test(part)) {\n      part = encodeURI(part).replace(/%5B/g, \"[\").replace(/%5D/g, \"]\");\n    }\n    return part;\n  }).join(\"\");\n}\nfunction encodeUnreserved(str) {\n  return encodeURIComponent(str).replace(/[!'()*]/g, function(c) {\n    return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\nfunction encodeValue(operator, value, key) {\n  value = operator === \"+\" || operator === \"#\" ? encodeReserved(value) : encodeUnreserved(value);\n  if (key) {\n    return encodeUnreserved(key) + \"=\" + value;\n  } else {\n    return value;\n  }\n}\nfunction isDefined(value) {\n  return value !== void 0 && value !== null;\n}\nfunction isKeyOperator(operator) {\n  return operator === \";\" || operator === \"&\" || operator === \"?\";\n}\nfunction getValues(context, operator, key, modifier) {\n  var value = context[key], result = [];\n  if (isDefined(value) && value !== \"\") {\n    if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n      value = value.toString();\n      if (modifier && modifier !== \"*\") {\n        value = value.substring(0, parseInt(modifier, 10));\n      }\n      result.push(\n        encodeValue(operator, value, isKeyOperator(operator) ? key : \"\")\n      );\n    } else {\n      if (modifier === \"*\") {\n        if (Array.isArray(value)) {\n          value.filter(isDefined).forEach(function(value2) {\n            result.push(\n              encodeValue(operator, value2, isKeyOperator(operator) ? key : \"\")\n            );\n          });\n        } else {\n          Object.keys(value).forEach(function(k) {\n            if (isDefined(value[k])) {\n              result.push(encodeValue(operator, value[k], k));\n            }\n          });\n        }\n      } else {\n        const tmp = [];\n        if (Array.isArray(value)) {\n          value.filter(isDefined).forEach(function(value2) {\n            tmp.push(encodeValue(operator, value2));\n          });\n        } else {\n          Object.keys(value).forEach(function(k) {\n            if (isDefined(value[k])) {\n              tmp.push(encodeUnreserved(k));\n              tmp.push(encodeValue(operator, value[k].toString()));\n            }\n          });\n        }\n        if (isKeyOperator(operator)) {\n          result.push(encodeUnreserved(key) + \"=\" + tmp.join(\",\"));\n        } else if (tmp.length !== 0) {\n          result.push(tmp.join(\",\"));\n        }\n      }\n    }\n  } else {\n    if (operator === \";\") {\n      if (isDefined(value)) {\n        result.push(encodeUnreserved(key));\n      }\n    } else if (value === \"\" && (operator === \"&\" || operator === \"?\")) {\n      result.push(encodeUnreserved(key) + \"=\");\n    } else if (value === \"\") {\n      result.push(\"\");\n    }\n  }\n  return result;\n}\nfunction parseUrl(template) {\n  return {\n    expand: expand.bind(null, template)\n  };\n}\nfunction expand(template, context) {\n  var operators = [\"+\", \"#\", \".\", \"/\", \";\", \"?\", \"&\"];\n  template = template.replace(\n    /\\{([^\\{\\}]+)\\}|([^\\{\\}]+)/g,\n    function(_, expression, literal) {\n      if (expression) {\n        let operator = \"\";\n        const values = [];\n        if (operators.indexOf(expression.charAt(0)) !== -1) {\n          operator = expression.charAt(0);\n          expression = expression.substr(1);\n        }\n        expression.split(/,/g).forEach(function(variable) {\n          var tmp = /([^:\\*]*)(?::(\\d+)|(\\*))?/.exec(variable);\n          values.push(getValues(context, operator, tmp[1], tmp[2] || tmp[3]));\n        });\n        if (operator && operator !== \"+\") {\n          var separator = \",\";\n          if (operator === \"?\") {\n            separator = \"&\";\n          } else if (operator !== \"#\") {\n            separator = operator;\n          }\n          return (values.length !== 0 ? operator : \"\") + values.join(separator);\n        } else {\n          return values.join(\",\");\n        }\n      } else {\n        return encodeReserved(literal);\n      }\n    }\n  );\n  if (template === \"/\") {\n    return template;\n  } else {\n    return template.replace(/\\/$/, \"\");\n  }\n}\nexport {\n  parseUrl\n};\n", "import { addQueryParameters } from \"./util/add-query-parameters.js\";\nimport { extractUrlVariableNames } from \"./util/extract-url-variable-names.js\";\nimport { omit } from \"./util/omit.js\";\nimport { parseUrl } from \"./util/url-template.js\";\nfunction parse(options) {\n  let method = options.method.toUpperCase();\n  let url = (options.url || \"/\").replace(/:([a-z]\\w+)/g, \"{$1}\");\n  let headers = Object.assign({}, options.headers);\n  let body;\n  let parameters = omit(options, [\n    \"method\",\n    \"baseUrl\",\n    \"url\",\n    \"headers\",\n    \"request\",\n    \"mediaType\"\n  ]);\n  const urlVariableNames = extractUrlVariableNames(url);\n  url = parseUrl(url).expand(parameters);\n  if (!/^http/.test(url)) {\n    url = options.baseUrl + url;\n  }\n  const omittedParameters = Object.keys(options).filter((option) => urlVariableNames.includes(option)).concat(\"baseUrl\");\n  const remainingParameters = omit(parameters, omittedParameters);\n  const isBinaryRequest = /application\\/octet-stream/i.test(headers.accept);\n  if (!isBinaryRequest) {\n    if (options.mediaType.format) {\n      headers.accept = headers.accept.split(/,/).map(\n        (format) => format.replace(\n          /application\\/vnd(\\.\\w+)(\\.v3)?(\\.\\w+)?(\\+json)?$/,\n          `application/vnd$1$2.${options.mediaType.format}`\n        )\n      ).join(\",\");\n    }\n    if (url.endsWith(\"/graphql\")) {\n      if (options.mediaType.previews?.length) {\n        const previewsFromAcceptHeader = headers.accept.match(/(?<![\\w-])[\\w-]+(?=-preview)/g) || [];\n        headers.accept = previewsFromAcceptHeader.concat(options.mediaType.previews).map((preview) => {\n          const format = options.mediaType.format ? `.${options.mediaType.format}` : \"+json\";\n          return `application/vnd.github.${preview}-preview${format}`;\n        }).join(\",\");\n      }\n    }\n  }\n  if ([\"GET\", \"HEAD\"].includes(method)) {\n    url = addQueryParameters(url, remainingParameters);\n  } else {\n    if (\"data\" in remainingParameters) {\n      body = remainingParameters.data;\n    } else {\n      if (Object.keys(remainingParameters).length) {\n        body = remainingParameters;\n      }\n    }\n  }\n  if (!headers[\"content-type\"] && typeof body !== \"undefined\") {\n    headers[\"content-type\"] = \"application/json; charset=utf-8\";\n  }\n  if ([\"PATCH\", \"PUT\"].includes(method) && typeof body === \"undefined\") {\n    body = \"\";\n  }\n  return Object.assign(\n    { method, url, headers },\n    typeof body !== \"undefined\" ? { body } : null,\n    options.request ? { request: options.request } : null\n  );\n}\nexport {\n  parse\n};\n", "import { DEFAULTS } from \"./defaults.js\";\nimport { merge } from \"./merge.js\";\nimport { parse } from \"./parse.js\";\nfunction endpointWithDefaults(defaults, route, options) {\n  return parse(merge(defaults, route, options));\n}\nexport {\n  endpointWithDefaults\n};\n", "import { endpointWithDefaults } from \"./endpoint-with-defaults.js\";\nimport { merge } from \"./merge.js\";\nimport { parse } from \"./parse.js\";\nfunction withDefaults(oldDefaults, newDefaults) {\n  const DEFAULTS = merge(oldDefaults, newDefaults);\n  const endpoint = endpointWithDefaults.bind(null, DEFAULTS);\n  return Object.assign(endpoint, {\n    DEFAULTS,\n    defaults: withDefaults.bind(null, DEFAULTS),\n    merge: merge.bind(null, DEFAULTS),\n    parse\n  });\n}\nexport {\n  withDefaults\n};\n", "import { withDefaults } from \"./with-defaults.js\";\nimport { DEFAULTS } from \"./defaults.js\";\nconst endpoint = withDefaults(null, DEFAULTS);\nexport {\n  endpoint\n};\n"], "mappings": ";AAAA,SAAS,oBAAoB;;;ACA7B,IAAM,UAAU;;;ADEhB,IAAM,YAAY,uBAAuB,OAAO,IAAI,aAAa,CAAC;AAClE,IAAM,WAAW;AAAA,EACf,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB;AAAA,EACA,WAAW;AAAA,IACT,QAAQ;AAAA,EACV;AACF;;;AEbA,SAAS,cAAc,QAAQ;AAC7B,MAAI,CAAC,QAAQ;AACX,WAAO,CAAC;AAAA,EACV;AACA,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACjD,WAAO,IAAI,YAAY,CAAC,IAAI,OAAO,GAAG;AACtC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;;;ACRA,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AACxD,MAAI,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,kBAAmB,QAAO;AACxE,QAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,MAAI,UAAU,KAAM,QAAO;AAC3B,QAAM,OAAO,OAAO,UAAU,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AACjF,SAAO,OAAO,SAAS,cAAc,gBAAgB,QAAQ,SAAS,UAAU,KAAK,IAAI,MAAM,SAAS,UAAU,KAAK,KAAK;AAC9H;;;ACNA,SAAS,UAAU,UAAU,SAAS;AACpC,QAAM,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ;AACzC,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpC,QAAI,cAAc,QAAQ,GAAG,CAAC,GAAG;AAC/B,UAAI,EAAE,OAAO,UAAW,QAAO,OAAO,QAAQ,EAAE,CAAC,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC;AAAA,UAChE,QAAO,GAAG,IAAI,UAAU,SAAS,GAAG,GAAG,QAAQ,GAAG,CAAC;AAAA,IAC1D,OAAO;AACL,aAAO,OAAO,QAAQ,EAAE,CAAC,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC;AAAA,IAC/C;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACZA,SAAS,0BAA0B,KAAK;AACtC,aAAW,OAAO,KAAK;AACrB,QAAI,IAAI,GAAG,MAAM,QAAQ;AACvB,aAAO,IAAI,GAAG;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;;;ACJA,SAAS,MAAM,UAAU,OAAO,SAAS;AACvC,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,MAAM,GAAG;AACnC,cAAU,OAAO,OAAO,MAAM,EAAE,QAAQ,IAAI,IAAI,EAAE,KAAK,OAAO,GAAG,OAAO;AAAA,EAC1E,OAAO;AACL,cAAU,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,EACnC;AACA,UAAQ,UAAU,cAAc,QAAQ,OAAO;AAC/C,4BAA0B,OAAO;AACjC,4BAA0B,QAAQ,OAAO;AACzC,QAAM,gBAAgB,UAAU,YAAY,CAAC,GAAG,OAAO;AACvD,MAAI,QAAQ,QAAQ,YAAY;AAC9B,QAAI,YAAY,SAAS,UAAU,UAAU,QAAQ;AACnD,oBAAc,UAAU,WAAW,SAAS,UAAU,SAAS;AAAA,QAC7D,CAAC,YAAY,CAAC,cAAc,UAAU,SAAS,SAAS,OAAO;AAAA,MACjE,EAAE,OAAO,cAAc,UAAU,QAAQ;AAAA,IAC3C;AACA,kBAAc,UAAU,YAAY,cAAc,UAAU,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,QAAQ,QAAQ,YAAY,EAAE,CAAC;AAAA,EAC9H;AACA,SAAO;AACT;;;ACvBA,SAAS,mBAAmB,KAAK,YAAY;AAC3C,QAAM,YAAY,KAAK,KAAK,GAAG,IAAI,MAAM;AACzC,QAAM,QAAQ,OAAO,KAAK,UAAU;AACpC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS;AAC3C,QAAI,SAAS,KAAK;AAChB,aAAO,OAAO,WAAW,EAAE,MAAM,GAAG,EAAE,IAAI,kBAAkB,EAAE,KAAK,GAAG;AAAA,IACxE;AACA,WAAO,GAAG,IAAI,IAAI,mBAAmB,WAAW,IAAI,CAAC,CAAC;AAAA,EACxD,CAAC,EAAE,KAAK,GAAG;AACb;;;ACZA,IAAM,mBAAmB;AACzB,SAAS,eAAe,cAAc;AACpC,SAAO,aAAa,QAAQ,6BAA6B,EAAE,EAAE,MAAM,GAAG;AACxE;AACA,SAAS,wBAAwB,KAAK;AACpC,QAAM,UAAU,IAAI,MAAM,gBAAgB;AAC1C,MAAI,CAAC,SAAS;AACZ,WAAO,CAAC;AAAA,EACV;AACA,SAAO,QAAQ,IAAI,cAAc,EAAE,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACrE;;;ACVA,SAAS,KAAK,QAAQ,YAAY;AAChC,QAAM,SAAS,EAAE,WAAW,KAAK;AACjC,aAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,QAAI,WAAW,QAAQ,GAAG,MAAM,IAAI;AAClC,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;;;ACRA,SAAS,eAAe,KAAK;AAC3B,SAAO,IAAI,MAAM,oBAAoB,EAAE,IAAI,SAAS,MAAM;AACxD,QAAI,CAAC,eAAe,KAAK,IAAI,GAAG;AAC9B,aAAO,UAAU,IAAI,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG;AAAA,IACjE;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,EAAE;AACZ;AACA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,mBAAmB,GAAG,EAAE,QAAQ,YAAY,SAAS,GAAG;AAC7D,WAAO,MAAM,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,EACxD,CAAC;AACH;AACA,SAAS,YAAY,UAAU,OAAO,KAAK;AACzC,UAAQ,aAAa,OAAO,aAAa,MAAM,eAAe,KAAK,IAAI,iBAAiB,KAAK;AAC7F,MAAI,KAAK;AACP,WAAO,iBAAiB,GAAG,IAAI,MAAM;AAAA,EACvC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,UAAU,UAAU;AACvC;AACA,SAAS,cAAc,UAAU;AAC/B,SAAO,aAAa,OAAO,aAAa,OAAO,aAAa;AAC9D;AACA,SAAS,UAAU,SAAS,UAAU,KAAK,UAAU;AACnD,MAAI,QAAQ,QAAQ,GAAG,GAAG,SAAS,CAAC;AACpC,MAAI,UAAU,KAAK,KAAK,UAAU,IAAI;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AACxF,cAAQ,MAAM,SAAS;AACvB,UAAI,YAAY,aAAa,KAAK;AAChC,gBAAQ,MAAM,UAAU,GAAG,SAAS,UAAU,EAAE,CAAC;AAAA,MACnD;AACA,aAAO;AAAA,QACL,YAAY,UAAU,OAAO,cAAc,QAAQ,IAAI,MAAM,EAAE;AAAA,MACjE;AAAA,IACF,OAAO;AACL,UAAI,aAAa,KAAK;AACpB,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAM,OAAO,SAAS,EAAE,QAAQ,SAAS,QAAQ;AAC/C,mBAAO;AAAA,cACL,YAAY,UAAU,QAAQ,cAAc,QAAQ,IAAI,MAAM,EAAE;AAAA,YAClE;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,KAAK,KAAK,EAAE,QAAQ,SAAS,GAAG;AACrC,gBAAI,UAAU,MAAM,CAAC,CAAC,GAAG;AACvB,qBAAO,KAAK,YAAY,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,YAChD;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,cAAM,MAAM,CAAC;AACb,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAM,OAAO,SAAS,EAAE,QAAQ,SAAS,QAAQ;AAC/C,gBAAI,KAAK,YAAY,UAAU,MAAM,CAAC;AAAA,UACxC,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,KAAK,KAAK,EAAE,QAAQ,SAAS,GAAG;AACrC,gBAAI,UAAU,MAAM,CAAC,CAAC,GAAG;AACvB,kBAAI,KAAK,iBAAiB,CAAC,CAAC;AAC5B,kBAAI,KAAK,YAAY,UAAU,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAAA,YACrD;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,cAAc,QAAQ,GAAG;AAC3B,iBAAO,KAAK,iBAAiB,GAAG,IAAI,MAAM,IAAI,KAAK,GAAG,CAAC;AAAA,QACzD,WAAW,IAAI,WAAW,GAAG;AAC3B,iBAAO,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,aAAa,KAAK;AACpB,UAAI,UAAU,KAAK,GAAG;AACpB,eAAO,KAAK,iBAAiB,GAAG,CAAC;AAAA,MACnC;AAAA,IACF,WAAW,UAAU,OAAO,aAAa,OAAO,aAAa,MAAM;AACjE,aAAO,KAAK,iBAAiB,GAAG,IAAI,GAAG;AAAA,IACzC,WAAW,UAAU,IAAI;AACvB,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO;AAAA,IACL,QAAQ,OAAO,KAAK,MAAM,QAAQ;AAAA,EACpC;AACF;AACA,SAAS,OAAO,UAAU,SAAS;AACjC,MAAI,YAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAClD,aAAW,SAAS;AAAA,IAClB;AAAA,IACA,SAAS,GAAG,YAAY,SAAS;AAC/B,UAAI,YAAY;AACd,YAAI,WAAW;AACf,cAAM,SAAS,CAAC;AAChB,YAAI,UAAU,QAAQ,WAAW,OAAO,CAAC,CAAC,MAAM,IAAI;AAClD,qBAAW,WAAW,OAAO,CAAC;AAC9B,uBAAa,WAAW,OAAO,CAAC;AAAA,QAClC;AACA,mBAAW,MAAM,IAAI,EAAE,QAAQ,SAAS,UAAU;AAChD,cAAI,MAAM,4BAA4B,KAAK,QAAQ;AACnD,iBAAO,KAAK,UAAU,SAAS,UAAU,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,QACpE,CAAC;AACD,YAAI,YAAY,aAAa,KAAK;AAChC,cAAI,YAAY;AAChB,cAAI,aAAa,KAAK;AACpB,wBAAY;AAAA,UACd,WAAW,aAAa,KAAK;AAC3B,wBAAY;AAAA,UACd;AACA,kBAAQ,OAAO,WAAW,IAAI,WAAW,MAAM,OAAO,KAAK,SAAS;AAAA,QACtE,OAAO;AACL,iBAAO,OAAO,KAAK,GAAG;AAAA,QACxB;AAAA,MACF,OAAO;AACL,eAAO,eAAe,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,KAAK;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,QAAQ,OAAO,EAAE;AAAA,EACnC;AACF;;;AC7HA,SAAS,MAAM,SAAS;AACtB,MAAI,SAAS,QAAQ,OAAO,YAAY;AACxC,MAAI,OAAO,QAAQ,OAAO,KAAK,QAAQ,gBAAgB,MAAM;AAC7D,MAAI,UAAU,OAAO,OAAO,CAAC,GAAG,QAAQ,OAAO;AAC/C,MAAI;AACJ,MAAI,aAAa,KAAK,SAAS;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,wBAAwB,GAAG;AACpD,QAAM,SAAS,GAAG,EAAE,OAAO,UAAU;AACrC,MAAI,CAAC,QAAQ,KAAK,GAAG,GAAG;AACtB,UAAM,QAAQ,UAAU;AAAA,EAC1B;AACA,QAAM,oBAAoB,OAAO,KAAK,OAAO,EAAE,OAAO,CAAC,WAAW,iBAAiB,SAAS,MAAM,CAAC,EAAE,OAAO,SAAS;AACrH,QAAM,sBAAsB,KAAK,YAAY,iBAAiB;AAC9D,QAAM,kBAAkB,6BAA6B,KAAK,QAAQ,MAAM;AACxE,MAAI,CAAC,iBAAiB;AACpB,QAAI,QAAQ,UAAU,QAAQ;AAC5B,cAAQ,SAAS,QAAQ,OAAO,MAAM,GAAG,EAAE;AAAA,QACzC,CAAC,WAAW,OAAO;AAAA,UACjB;AAAA,UACA,uBAAuB,QAAQ,UAAU,MAAM;AAAA,QACjD;AAAA,MACF,EAAE,KAAK,GAAG;AAAA,IACZ;AACA,QAAI,IAAI,SAAS,UAAU,GAAG;AAC5B,UAAI,QAAQ,UAAU,UAAU,QAAQ;AACtC,cAAM,2BAA2B,QAAQ,OAAO,MAAM,+BAA+B,KAAK,CAAC;AAC3F,gBAAQ,SAAS,yBAAyB,OAAO,QAAQ,UAAU,QAAQ,EAAE,IAAI,CAAC,YAAY;AAC5F,gBAAM,SAAS,QAAQ,UAAU,SAAS,IAAI,QAAQ,UAAU,MAAM,KAAK;AAC3E,iBAAO,0BAA0B,OAAO,WAAW,MAAM;AAAA,QAC3D,CAAC,EAAE,KAAK,GAAG;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,OAAO,MAAM,EAAE,SAAS,MAAM,GAAG;AACpC,UAAM,mBAAmB,KAAK,mBAAmB;AAAA,EACnD,OAAO;AACL,QAAI,UAAU,qBAAqB;AACjC,aAAO,oBAAoB;AAAA,IAC7B,OAAO;AACL,UAAI,OAAO,KAAK,mBAAmB,EAAE,QAAQ;AAC3C,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,cAAc,KAAK,OAAO,SAAS,aAAa;AAC3D,YAAQ,cAAc,IAAI;AAAA,EAC5B;AACA,MAAI,CAAC,SAAS,KAAK,EAAE,SAAS,MAAM,KAAK,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT;AACA,SAAO,OAAO;AAAA,IACZ,EAAE,QAAQ,KAAK,QAAQ;AAAA,IACvB,OAAO,SAAS,cAAc,EAAE,KAAK,IAAI;AAAA,IACzC,QAAQ,UAAU,EAAE,SAAS,QAAQ,QAAQ,IAAI;AAAA,EACnD;AACF;;;AC/DA,SAAS,qBAAqB,UAAU,OAAO,SAAS;AACtD,SAAO,MAAM,MAAM,UAAU,OAAO,OAAO,CAAC;AAC9C;;;ACFA,SAAS,aAAa,aAAa,aAAa;AAC9C,QAAMA,YAAW,MAAM,aAAa,WAAW;AAC/C,QAAMC,YAAW,qBAAqB,KAAK,MAAMD,SAAQ;AACzD,SAAO,OAAO,OAAOC,WAAU;AAAA,IAC7B,UAAAD;AAAA,IACA,UAAU,aAAa,KAAK,MAAMA,SAAQ;AAAA,IAC1C,OAAO,MAAM,KAAK,MAAMA,SAAQ;AAAA,IAChC;AAAA,EACF,CAAC;AACH;;;ACVA,IAAM,WAAW,aAAa,MAAM,QAAQ;", "names": ["DEFAULTS", "endpoint"]}