"use strict";
/**
 * Preload脚本
 * 在渲染进程中暴露安全的API接口
 */
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// 实现API接口
const electronAPI = {
    data: {
        getConfig: () => electron_1.ipcRenderer.invoke('data:getConfig'),
        setConfig: (config) => electron_1.ipcRenderer.invoke('data:setConfig', config),
        getCategories: () => electron_1.ipcRenderer.invoke('data:getCategories'),
        setCategories: (categories) => electron_1.ipcRenderer.invoke('data:setCategories', categories),
        getAuthors: <AUTHORS>
        setAuthors: <AUTHORS>
        getProjects: () => electron_1.ipcRenderer.invoke('data:getProjects'),
        setProjects: (projects) => electron_1.ipcRenderer.invoke('data:setProjects', projects),
    },
    github: {
        checkRateLimit: () => electron_1.ipcRenderer.invoke('github:checkRateLimit'),
        getUser: (username) => electron_1.ipcRenderer.invoke('github:getUser', username),
        getRepository: (owner, repo) => electron_1.ipcRenderer.invoke('github:getRepository', owner, repo),
        getUserRepositories: (username) => electron_1.ipcRenderer.invoke('github:getUserRepositories', username),
        parseUrl: (url) => electron_1.ipcRenderer.invoke('github:parseUrl', url),
        getRepoInfoFromUrl: (url) => electron_1.ipcRenderer.invoke('github:getRepoInfoFromUrl', url),
        getUserInfoFromUrl: (url) => electron_1.ipcRenderer.invoke('github:getUserInfoFromUrl', url),
        checkProjectUpdate: (projectId) => electron_1.ipcRenderer.invoke('github:checkProjectUpdate', projectId),
        checkAuthorUpdate: (authorId) => electron_1.ipcRenderer.invoke('github:checkAuthorUpdate', authorId),
        getAuthorNewProjects: (authorId, readProjectIds) => electron_1.ipcRenderer.invoke('github:getAuthorNewProjects', authorId, readProjectIds),
        getAuthorUpdatedProjects: (authorId, readProjectIds) => electron_1.ipcRenderer.invoke('github:getAuthorUpdatedProjects', authorId, readProjectIds),
        markProjectAsRead: (authorId, projectId) => electron_1.ipcRenderer.invoke('github:markProjectAsRead', authorId, projectId),
        checkAuthorHasUnreadProjects: (authorId, readProjectIds) => electron_1.ipcRenderer.invoke('github:checkAuthorHasUnreadProjects', authorId, readProjectIds),
        getAuthorProjects: (authorId) => electron_1.ipcRenderer.invoke('github:getAuthorProjects', authorId),
        checkAllProjectUpdates: () => electron_1.ipcRenderer.invoke('github:checkAllProjectUpdates'),
        checkAllAuthorUpdates: () => electron_1.ipcRenderer.invoke('github:checkAllAuthorUpdates'),
    },
    system: {
        openExternal: (url) => electron_1.ipcRenderer.invoke('system:openExternal', url),
        showMessageBox: (options) => electron_1.ipcRenderer.invoke('system:showMessageBox', options),
        showOpenDialog: (options) => electron_1.ipcRenderer.invoke('system:showOpenDialog', options),
        showSaveDialog: (options) => electron_1.ipcRenderer.invoke('system:showSaveDialog', options),
    },
    fs: {
        readFile: (filePath) => electron_1.ipcRenderer.invoke('fs:readFile', filePath),
        writeFile: (filePath, data) => electron_1.ipcRenderer.invoke('fs:writeFile', filePath, data),
        ensureDir: (dirPath) => electron_1.ipcRenderer.invoke('fs:ensureDir', dirPath),
        pathExists: (filePath) => electron_1.ipcRenderer.invoke('fs:pathExists', filePath),
    },
    app: {
        getVersion: () => electron_1.ipcRenderer.invoke('app:getVersion'),
        quit: () => electron_1.ipcRenderer.send('app:quit'),
        minimize: () => electron_1.ipcRenderer.send('app:minimize'),
        maximize: () => electron_1.ipcRenderer.send('app:maximize'),
        close: () => electron_1.ipcRenderer.send('app:close'),
    },
};
// 将API暴露给渲染进程
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
