{"name": "@octokit/auth-token", "publishConfig": {"access": "public", "provenance": true}, "type": "module", "version": "6.0.0", "description": "GitHub API token authentication for browsers and Node.js", "repository": "github:octokit/auth-token.js", "keywords": ["github", "octokit", "authentication", "api"], "author": "<PERSON> (https://github.com/gr2m)", "license": "MIT", "devDependencies": {"@octokit/request": "^9.0.0", "@octokit/tsconfig": "^4.0.0", "@octokit/types": "^14.0.0", "@vitest/coverage-v8": "^3.0.0", "esbuild": "^0.25.0", "fetch-mock": "^12.0.0", "glob": "^11.0.0", "prettier": "3.5.3", "typescript": "^5.3.0", "vitest": "^3.0.0"}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "./dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-bundle/index.js", "default": "./dist-bundle/index.js"}, "./types": {"types": "./dist-types/index.d.ts"}}, "sideEffects": false}