[{"id": "995029641", "name": "claude-flow", "description": "Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.", "url": "https://github.com/ruvnet/claude-flow", "language": "TypeScript", "stars": 2658, "forks": 399, "updatedAt": "2025-07-20T20:32:35Z", "createdAt": "2025-06-02T21:24:20Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-07-20T17:51:08Z", "lastReleaseAt": "2025-07-20T20:32:35Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1020477827", "name": "code-mesh", "description": "Code Mesh", "url": "https://github.com/ruvnet/code-mesh", "language": "Rust", "stars": 12, "forks": 6, "updatedAt": "2025-07-20T20:22:08Z", "createdAt": "2025-07-16T00:02:39Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-07-17T15:54:06Z", "lastReleaseAt": "2025-07-20T20:22:08Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "868947806", "name": "sparc", "description": "", "url": "https://github.com/ruvnet/sparc", "language": "Python", "stars": 338, "forks": 76, "updatedAt": "2025-07-20T20:19:12Z", "createdAt": "2024-10-07T13:11:07Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-29T16:49:03Z", "lastReleaseAt": "2025-07-20T20:19:12Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1008497251", "name": "yyz-agentics-june", "description": " AI-Powered Neural Network Libraries built with Claude-Flow Swarm Orchestration. Live demo from Agentics   Foundation meetup in Toronto - showcasing how 10 specialized AI agents built production-ready neural network   frameworks worth $882K+ in a single session.", "url": "https://github.com/ruvnet/yyz-agentics-june", "language": "Python", "stars": 20, "forks": 11, "updatedAt": "2025-07-20T14:56:51Z", "createdAt": "2025-06-25T16:25:57Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-25T16:46:04Z", "lastReleaseAt": "2025-07-20T14:56:51Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "937189446", "name": "dspy.ts", "description": "DS.js (Declarative Self‑learning JavaScript", "url": "https://github.com/ruvnet/dspy.ts", "language": "TypeScript", "stars": 104, "forks": 12, "updatedAt": "2025-07-20T13:12:21Z", "createdAt": "2025-02-22T14:51:48Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-02-23T15:01:01Z", "lastReleaseAt": "2025-07-20T13:12:21Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1018759832", "name": "Synaptic-Mesh", "description": "Synaptic Neural Mesh: a self-evolving, peer to peer neural fabric where every element is an agent, learning and communicating across a globally coordinated DAG substrate.", "url": "https://github.com/ruvnet/Synaptic-Mesh", "language": "Rust", "stars": 25, "forks": 10, "updatedAt": "2025-07-20T09:25:36Z", "createdAt": "2025-07-13T01:31:00Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-07-14T01:58:21Z", "lastReleaseAt": "2025-07-20T09:25:36Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "782143036", "name": "rUv-dev", "description": "Ai power Dev using the rUv approach", "url": "https://github.com/ruvnet/rUv-dev", "language": "JavaScript", "stars": 396, "forks": 69, "updatedAt": "2025-07-20T07:38:35Z", "createdAt": "2024-04-04T18:07:24Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-27T15:58:50Z", "lastReleaseAt": "2025-07-20T07:38:35Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1009254201", "name": "ruv-FANN", "description": "A blazing-fast, memory-safe neural network library for Rust that brings the power of FANN to the modern world.", "url": "https://github.com/ruvnet/ruv-FANN", "language": "Rust", "stars": 188, "forks": 71, "updatedAt": "2025-07-19T23:45:06Z", "createdAt": "2025-06-26T20:38:15Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-07-19T04:16:12Z", "lastReleaseAt": "2025-07-19T23:45:06Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "895270916", "name": "symbolic-scribe", "description": "", "url": "https://github.com/ruvnet/symbolic-scribe", "language": "TypeScript", "stars": 77, "forks": 20, "updatedAt": "2025-07-19T16:29:38Z", "createdAt": "2024-11-27T22:15:27Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-11-28T19:37:15Z", "lastReleaseAt": "2025-07-19T16:29:38Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "984296555", "name": "Agent-Name-Service", "description": "Agent Name Service (ANS) Protocol, introduced by the OWASP GenAI Security Project, is a foundational framework designed to facilitate secure discovery and interaction among AI agents. ", "url": "https://github.com/ruvnet/Agent-Name-Service", "language": "TypeScript", "stars": 29, "forks": 6, "updatedAt": "2025-07-19T13:56:49Z", "createdAt": "2025-05-15T17:45:15Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-16T12:34:26Z", "lastReleaseAt": "2025-07-19T13:56:49Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "968651953", "name": "quantum-magnetic-navigation", "description": "A navigation system that uses quantum magnetometers to provide precise positioning in GPS-denied environment", "url": "https://github.com/ruvnet/quantum-magnetic-navigation", "language": "Python", "stars": 19, "forks": 11, "updatedAt": "2025-07-19T09:38:29Z", "createdAt": "2025-04-18T13:27:38Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-18T14:48:07Z", "lastReleaseAt": "2025-07-19T09:38:29Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "622702308", "name": "Bot-Generator-Bo<PERSON>", "description": "The ultimate bot generator bot prompt. Use this prompt to create powerful ChatGPT bots for anything you can imagine.", "url": "https://github.com/ruvnet/Bot-Generator-Bot", "language": "<PERSON>", "stars": 528, "forks": 113, "updatedAt": "2025-07-19T08:27:15Z", "createdAt": "2023-04-02T21:45:02Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-04-29T20:38:56Z", "lastReleaseAt": "2025-07-19T08:27:15Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "972900319", "name": "dynamo-mcp", "description": "A dyamic MCP Registry using Cookiecutter templates", "url": "https://github.com/ruvnet/dynamo-mcp", "language": "Python", "stars": 29, "forks": 8, "updatedAt": "2025-07-19T08:20:04Z", "createdAt": "2025-04-25T21:27:28Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-26T15:17:36Z", "lastReleaseAt": "2025-07-19T08:20:04Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1018554801", "name": "swarm-world", "description": "Ai Game Builder", "url": "https://github.com/ruvnet/swarm-world", "language": "C#", "stars": 3, "forks": 0, "updatedAt": "2025-07-19T04:24:32Z", "createdAt": "2025-07-12T14:09:56Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-07-12T16:08:19Z", "lastReleaseAt": "2025-07-19T04:24:32Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "802231174", "name": "agileagents", "description": "Agile Agents (A2) is an open-source framework for the creation and deployment of serverless intelligent agents using public and private container repositories. The framework supports serverless Ai Agent deployments to AWS, Azure, and GCP, with optional configurations for Azure and GCP", "url": "https://github.com/ruvnet/agileagents", "language": "Python", "stars": 24, "forks": 8, "updatedAt": "2025-07-19T04:15:20Z", "createdAt": "2024-05-17T19:30:21Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-06-27T15:14:34Z", "lastReleaseAt": "2025-07-19T04:15:20Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "901362641", "name": "quantum_cryptocurrency", "description": " Quantum-Secured Cryptocurrency", "url": "https://github.com/ruvnet/quantum_cryptocurrency", "language": "Python", "stars": 18, "forks": 8, "updatedAt": "2025-07-19T00:36:47Z", "createdAt": "2024-12-10T14:17:43Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-12-10T21:21:38Z", "lastReleaseAt": "2025-07-19T00:36:47Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "805862950", "name": "ai-gist", "description": "This project provides a FastAPI application to create and update GitHub gists using the GitHub API. It includes SQLite for persistence and is designed to run in a GitHub Codespace.", "url": "https://github.com/ruvnet/ai-gist", "language": "Python", "stars": 8, "forks": 4, "updatedAt": "2025-07-19T00:36:26Z", "createdAt": "2024-05-25T17:16:19Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-05-26T05:10:16Z", "lastReleaseAt": "2025-07-19T00:36:26Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "998648206", "name": "ultrasonic", "description": "A comprehensive steganography framework for embedding and extracting agentic commands in audio and video media using ultrasonic frequencies. This project provides tools for covert communication and command transmission through multimedia channels.", "url": "https://github.com/ruvnet/ultrasonic", "language": "TypeScript", "stars": 24, "forks": 13, "updatedAt": "2025-07-18T07:57:56Z", "createdAt": "2025-06-09T03:00:35Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-09T04:26:31Z", "lastReleaseAt": "2025-07-18T07:57:56Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "937678230", "name": "onnx-agent", "description": "A complete set of requirements—covering UX, CLI, and code—that builds on the previous pipeline for training and optimizing ONNX models with test‑time compute methods using DSPy. This document specifies user stories, command‐line interface arguments, and sample code snippets to guide implementation.", "url": "https://github.com/ruvnet/onnx-agent", "language": "Python", "stars": 4, "forks": 2, "updatedAt": "2025-07-17T03:33:55Z", "createdAt": "2025-02-23T16:43:08Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-02-24T02:34:24Z", "lastReleaseAt": "2025-07-17T03:33:55Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "611963846", "name": "daa", "description": "Decentralized Autonomous Applications (DAAs).  Building the Future with Self-Managing Applications.", "url": "https://github.com/ruvnet/daa", "language": "Rust", "stars": 159, "forks": 30, "updatedAt": "2025-07-17T03:29:56Z", "createdAt": "2023-03-09T22:54:05Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-26T14:37:51Z", "lastReleaseAt": "2025-07-17T03:29:56Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "993871216", "name": "SAFLA", "description": "Self-Aware Feedback Loop Algorithm (python)", "url": "https://github.com/ruvnet/SAFLA", "language": "Python", "stars": 97, "forks": 34, "updatedAt": "2025-07-16T22:37:54Z", "createdAt": "2025-05-31T17:45:42Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-24T16:34:34Z", "lastReleaseAt": "2025-07-16T22:37:54Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "958021178", "name": "phone-agent", "description": "This implementation creates a Cloudflare Worker that receives emails via Resend, parses calendar invites, and schedules a Bland.ai agent to join conference calls. The worker handles the entire process automatically, from receiving the email to scheduling the AI agent with specific instructions", "url": "https://github.com/ruvnet/phone-agent", "language": "TypeScript", "stars": 11, "forks": 3, "updatedAt": "2025-07-16T21:52:55Z", "createdAt": "2025-03-31T14:11:11Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-04T16:11:29Z", "lastReleaseAt": "2025-07-16T21:52:55Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "982299299", "name": "ruv-code", "description": "a fork of a fork", "url": "https://github.com/ruvnet/ruv-code", "language": "TypeScript", "stars": 7, "forks": 2, "updatedAt": "2025-07-16T21:49:43Z", "createdAt": "2025-05-12T17:09:26Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-14T15:23:53Z", "lastReleaseAt": "2025-07-16T21:49:43Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "933871971", "name": "hello_world_agent", "description": "A simple demonstration agent using the ReACT methodology for analyzing and executing tasks.", "url": "https://github.com/ruvnet/hello_world_agent", "language": "TypeScript", "stars": 97, "forks": 28, "updatedAt": "2025-07-16T21:39:18Z", "createdAt": "2025-02-16T21:43:45Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-03-08T23:19:24Z", "lastReleaseAt": "2025-07-16T21:39:18Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "978888660", "name": "sparc-ide", "description": "A custom Ai driven IDE", "url": "https://github.com/ruvnet/sparc-ide", "language": "Shell", "stars": 14, "forks": 8, "updatedAt": "2025-07-16T21:30:18Z", "createdAt": "2025-05-06T16:58:26Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-06T22:14:06Z", "lastReleaseAt": "2025-07-16T21:30:18Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "989009933", "name": "FACT", "description": "FACT – Fast Augmented Context Tools: FACT is a lean retrieval pattern that skips vector search. We cache every static token inside Claude Sonnet‑4 and fetch live facts only through authenticated tools hosted on Arcade.dev. The result is deterministic answers, fresh data, and sub‑100 ms latency.", "url": "https://github.com/ruvnet/FACT", "language": "Python", "stars": 72, "forks": 15, "updatedAt": "2025-07-16T12:14:10Z", "createdAt": "2025-05-23T12:19:07Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-03T14:37:48Z", "lastReleaseAt": "2025-07-16T12:14:10Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "616756123", "name": "AiToml", "description": "AI-TOML Workflow Specification (aiTWS), a comprehensive and flexible specification for defining arbitrary Ai centric workflows.", "url": "https://github.com/ruvnet/AiToml", "language": "Rust", "stars": 59, "forks": 11, "updatedAt": "2025-07-16T02:50:13Z", "createdAt": "2023-03-21T02:31:55Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-03-21T05:20:18Z", "lastReleaseAt": "2025-07-16T02:50:13Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "997737944", "name": "wifi-densepose", "description": "Production-ready implementation of InvisPose - a revolutionary WiFi-based dense human pose estimation system that enables real-time full-body tracking through walls using commodity mesh routers ", "url": "https://github.com/ruvnet/wifi-densepose", "language": "Python", "stars": 38, "forks": 13, "updatedAt": "2025-07-15T04:35:02Z", "createdAt": "2025-06-07T04:32:30Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-09T17:13:36Z", "lastReleaseAt": "2025-07-15T04:35:02Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "726953186", "name": "guardrail", "description": "GuardRail: Advanced tool for data analysis and AI content generation using OpenAI GPT models. Features sentiment analysis, content classification, trend analysis, and tailored GPT model usage. Ideal for content moderation, customer support, and market research.", "url": "https://github.com/ruvnet/guardrail", "language": "Python", "stars": 132, "forks": 22, "updatedAt": "2025-07-14T17:11:57Z", "createdAt": "2023-12-03T21:33:39Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-12-12T21:20:27Z", "lastReleaseAt": "2025-07-14T17:11:57Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "625978582", "name": "ruvnet", "description": "ruvnet.", "url": "https://github.com/ruvnet/ruvnet", "language": "", "stars": 75, "forks": 8, "updatedAt": "2025-07-14T04:01:16Z", "createdAt": "2023-04-10T14:34:35Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-09T16:48:37Z", "lastReleaseAt": "2025-07-14T04:01:16Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "924786226", "name": "nova", "description": "NOVA is a knowledge distillation system that extracts domain-specific knowledge and trains compact models while maintaining high performance.", "url": "https://github.com/ruvnet/nova", "language": "Python", "stars": 18, "forks": 11, "updatedAt": "2025-07-14T00:25:15Z", "createdAt": "2025-01-30T16:45:20Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-31T02:54:40Z", "lastReleaseAt": "2025-07-14T00:25:15Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "894546491", "name": "federated-mcp", "description": "This implementation follows the official MCP specification, including proper message framing, transport layer implementation, and complete protocol lifecycle management. It provides a foundation for building federated MCP systems that can scale across multiple servers while maintaining security and standardization requirements.", "url": "https://github.com/ruvnet/federated-mcp", "language": "TypeScript", "stars": 49, "forks": 10, "updatedAt": "2025-07-14T00:24:22Z", "createdAt": "2024-11-26T14:45:53Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-11-26T22:11:23Z", "lastReleaseAt": "2025-07-14T00:24:22Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "795528441", "name": "rUv-Enterprise-AI-Guide", "description": "The rUv Enterprise AI Guide is a comprehensive resource designed to assist Chief Information Officers (CIOs) and technology leaders in navigating the complexities of AI integration within large enterprises.", "url": "https://github.com/ruvnet/rUv-Enterprise-AI-Guide", "language": "", "stars": 58, "forks": 11, "updatedAt": "2025-07-12T22:50:53Z", "createdAt": "2024-05-03T13:29:14Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-05-03T18:54:22Z", "lastReleaseAt": "2025-07-12T22:50:53Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "809063199", "name": "agentic-reports", "description": "", "url": "https://github.com/ruvnet/agentic-reports", "language": "Python", "stars": 37, "forks": 7, "updatedAt": "2025-07-12T22:50:10Z", "createdAt": "2024-06-01T15:26:15Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-06-03T19:37:35Z", "lastReleaseAt": "2025-07-12T22:50:10Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "987841928", "name": "GenAI-Superstream", "description": "Agentic Engineering for Data Analysis ", "url": "https://github.com/ruvnet/GenAI-Superstream", "language": "Python", "stars": 40, "forks": 14, "updatedAt": "2025-07-12T19:54:51Z", "createdAt": "2025-05-21T17:03:59Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-22T20:33:47Z", "lastReleaseAt": "2025-07-12T19:54:51Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1000843936", "name": "claude-test", "description": "test", "url": "https://github.com/ruvnet/claude-test", "language": "Shell", "stars": 11, "forks": 15, "updatedAt": "2025-07-12T12:37:41Z", "createdAt": "2025-06-12T12:08:16Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-24T21:06:29Z", "lastReleaseAt": "2025-07-12T12:37:41Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "621509821", "name": "paris", "description": "PARIS (Perpetual Adaptive Regenerative Intelligence System) is a conceptual model for building and managing effective AI and Language Model (LLM) systems that emphasizes the importance of perpetual feedback loops.", "url": "https://github.com/ruvnet/paris", "language": "Python", "stars": 24, "forks": 6, "updatedAt": "2025-07-12T12:35:43Z", "createdAt": "2023-03-30T20:09:16Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-03-30T20:47:53Z", "lastReleaseAt": "2025-07-12T12:35:43Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "621991494", "name": "VIVIAN", "description": "VIVIAN: Vector Index Virtual Infrastructure for Autonomous Networks", "url": "https://github.com/ruvnet/VIVIAN", "language": "Rust", "stars": 36, "forks": 8, "updatedAt": "2025-07-12T12:35:22Z", "createdAt": "2023-03-31T20:52:56Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-04-02T05:46:28Z", "lastReleaseAt": "2025-07-12T12:35:22Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "938364389", "name": "open-claude-code", "description": "MIPROv3 is an advanced prompt optimization framework for DSPy, enhancing efficiency, adaptability, and convergence using parallel execution, Bayesian optimization, and evolutionary strategies to optimize RAG, multi-agent collaboration, and instruction tuning.", "url": "https://github.com/ruvnet/open-claude-code", "language": "JavaScript", "stars": 14, "forks": 4, "updatedAt": "2025-07-12T10:51:18Z", "createdAt": "2025-02-24T20:52:35Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-02-24T23:55:21Z", "lastReleaseAt": "2025-07-12T10:51:18Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "976155469", "name": "vibecast", "description": "Weekly Vibecast Live coding sessions with rUv. Check branches for each week. ", "url": "https://github.com/ruvnet/vibecast", "language": "", "stars": 15, "forks": 5, "updatedAt": "2025-07-12T02:49:36Z", "createdAt": "2025-05-01T15:44:46Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-07-12T01:53:12Z", "lastReleaseAt": "2025-07-12T02:49:36Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1002704385", "name": "QuDAG", "description": "QuDAG Protocol (Quantum-Resistant DAG-Based Anonymous Communication System) - Claude Code implementation of a Test-Driven Development Implementation Plan for QuDAG Protocol with Claude Code", "url": "https://github.com/ruvnet/QuDAG", "language": "Rust", "stars": 90, "forks": 32, "updatedAt": "2025-07-11T19:08:37Z", "createdAt": "2025-06-16T02:29:03Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-24T03:06:24Z", "lastReleaseAt": "2025-07-11T19:08:37Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "867836348", "name": "agentic-search", "description": "An Agentic Github Copilot Extension", "url": "https://github.com/ruvnet/agentic-search", "language": "JavaScript", "stars": 28, "forks": 14, "updatedAt": "2025-07-11T03:13:34Z", "createdAt": "2024-10-04T20:23:13Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-20T19:11:52Z", "lastReleaseAt": "2025-07-11T03:13:34Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "972827380", "name": "vsc-remote-mcp", "description": "", "url": "https://github.com/ruvnet/vsc-remote-mcp", "language": "JavaScript", "stars": 10, "forks": 6, "updatedAt": "2025-07-09T23:08:28Z", "createdAt": "2025-04-25T18:21:50Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-27T15:50:10Z", "lastReleaseAt": "2025-07-09T23:08:28Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "635967510", "name": "Prompt-Engine", "description": "Prompt Engine is a powerful and flexible template designed to facilitate the creation and customization of interactive prompts.", "url": "https://github.com/ruvnet/Prompt-Engine", "language": "", "stars": 74, "forks": 15, "updatedAt": "2025-07-09T21:13:15Z", "createdAt": "2023-05-03T21:02:44Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-05-03T23:18:12Z", "lastReleaseAt": "2025-07-09T21:13:15Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "812839960", "name": "agentic-devops", "description": "Agentic DevOps is a powerful, interactive Command-Line Interface (CLI) designed to streamline and automate the development, deployment, and management of applications on various Cloud Providers.", "url": "https://github.com/ruvnet/agentic-devops", "language": "Python", "stars": 25, "forks": 13, "updatedAt": "2025-07-09T06:08:20Z", "createdAt": "2024-06-10T01:44:54Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-06-20T20:53:42Z", "lastReleaseAt": "2025-07-09T06:08:20Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "874937493", "name": "swirl-search", "description": "SWIRL AI Connect: AI infrastructure software that powers your Search & Retrieval Augmented Generation (RAG) applications. Simplify and enhance your AI pipelines with seamless integration of large language models (LLMs) and data sources.", "url": "https://github.com/ruvnet/swirl-search", "language": "Python", "stars": 6, "forks": 0, "updatedAt": "2025-07-09T03:23:18Z", "createdAt": "2024-10-18T18:33:15Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-10-30T19:02:36Z", "lastReleaseAt": "2025-07-09T03:23:18Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "915335945", "name": "ai-browse", "description": "Agent-based automation, focusing on browser tasks. ", "url": "https://github.com/ruvnet/ai-browse", "language": "", "stars": 3, "forks": 0, "updatedAt": "2025-07-09T03:23:03Z", "createdAt": "2025-01-11T15:29:57Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-11T15:29:57Z", "lastReleaseAt": "2025-07-09T03:23:03Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "912254237", "name": "SynthLang", "description": "SynthLang is a hyper-efficient prompt language designed to optimize interactions with Large Language Models (LLMs) like GPT-4o by leveraging logographical scripts and symbolic constructs.", "url": "https://github.com/ruvnet/SynthLang", "language": "Python", "stars": 213, "forks": 51, "updatedAt": "2025-07-09T03:21:36Z", "createdAt": "2025-01-05T03:22:35Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-03-25T03:05:51Z", "lastReleaseAt": "2025-07-09T03:21:36Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "981400212", "name": "codespaces-jupyter", "description": "Explore machine learning and data science with Codespaces", "url": "https://github.com/ruvnet/codespaces-jupyter", "language": "", "stars": 1, "forks": 0, "updatedAt": "2025-07-09T03:21:33Z", "createdAt": "2025-05-11T02:43:01Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-03-26T14:44:55Z", "lastReleaseAt": "2025-07-09T03:21:33Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "984124806", "name": "open-agentics", "description": "An open-source, no-code agent building platform.", "url": "https://github.com/ruvnet/open-agentics", "language": "", "stars": 1, "forks": 1, "updatedAt": "2025-07-09T03:21:16Z", "createdAt": "2025-05-15T12:40:06Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-15T00:45:04Z", "lastReleaseAt": "2025-07-09T03:21:16Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "972126418", "name": "image-agent", "description": "", "url": "https://github.com/ruvnet/image-agent", "language": "JavaScript", "stars": 2, "forks": 0, "updatedAt": "2025-07-08T20:52:37Z", "createdAt": "2025-04-24T15:17:55Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-25T18:09:58Z", "lastReleaseAt": "2025-07-08T20:52:37Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "723067454", "name": "q-star", "description": "Q-Star Agent Code: A reinforcement learning-based framework for intelligent agents using Microsoft AutoGen. It leverages Q-Star, a Q-learning variant, for dynamic decision-making. Ideal for developing adaptive AI agents in diverse applications, from service bots to advanced simulations. Includes setup and customization guidelines.", "url": "https://github.com/ruvnet/q-star", "language": "Python", "stars": 87, "forks": 23, "updatedAt": "2025-07-08T20:03:39Z", "createdAt": "2023-11-24T15:48:22Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-01-31T15:43:27Z", "lastReleaseAt": "2025-07-08T20:03:39Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "906025473", "name": "genesis", "description": "Genesis is a groundbreaking physics platform designed for robotics and embodied AI applications that combines unprecedented simulation speeds with comprehensive features.", "url": "https://github.com/ruvnet/genesis", "language": "Python", "stars": 10, "forks": 2, "updatedAt": "2025-07-07T07:53:25Z", "createdAt": "2024-12-20T02:21:18Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-12-21T03:28:39Z", "lastReleaseAt": "2025-07-07T07:53:25Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "805379818", "name": "ai-video", "description": "a web application that captures media streams from various sources such as a webcam, desktop, or specific applications. It captures frames at intervals and uses AI to analyze and summarize the frames, providing insights using GPT-4.", "url": "https://github.com/ruvnet/ai-video", "language": "JavaScript", "stars": 33, "forks": 8, "updatedAt": "2025-07-06T23:45:54Z", "createdAt": "2024-05-24T12:57:12Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-05-24T14:05:55Z", "lastReleaseAt": "2025-07-06T23:45:54Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "902079934", "name": "reflective-engineer", "description": "", "url": "https://github.com/ruvnet/reflective-engineer", "language": "TypeScript", "stars": 50, "forks": 12, "updatedAt": "2025-07-06T20:41:03Z", "createdAt": "2024-12-11T21:40:06Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-12-19T17:46:59Z", "lastReleaseAt": "2025-07-06T20:41:03Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "1004611295", "name": "dreamfactory", "description": "DreamFactory API Generation Platform - API Wrapper for SQL Server, Snowflake, MySQL, and more!", "url": "https://github.com/ruvnet/dreamfactory", "language": "", "stars": 3, "forks": 1, "updatedAt": "2025-07-06T19:40:53Z", "createdAt": "2025-06-18T22:50:31Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-06-19T04:09:30Z", "lastReleaseAt": "2025-07-06T19:40:53Z", "isPrivate": false, "defaultBranch": "master"}}, {"id": "725751420", "name": "gpts", "description": "A collection of the GPTs created by rUv", "url": "https://github.com/ruvnet/gpts", "language": "", "stars": 279, "forks": 73, "updatedAt": "2025-07-03T21:40:08Z", "createdAt": "2023-11-30T19:53:36Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-07-18T20:35:05Z", "lastReleaseAt": "2025-07-03T21:40:08Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "805880888", "name": "pygentic", "description": "Pygentic is an innovative system designed to enhance the capabilities of AI assistants by providing a flexible and standardized API. ", "url": "https://github.com/ruvnet/pygentic", "language": "Python", "stars": 12, "forks": 5, "updatedAt": "2025-07-03T18:50:04Z", "createdAt": "2024-05-25T18:22:19Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-06-01T17:43:00Z", "lastReleaseAt": "2025-07-03T18:50:04Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "961418967", "name": "roomodes", "description": "Various Roo Mode Configurations", "url": "https://github.com/ruvnet/roomodes", "language": "", "stars": 11, "forks": 0, "updatedAt": "2025-07-03T07:08:27Z", "createdAt": "2025-04-06T13:37:57Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-06T13:37:58Z", "lastReleaseAt": "2025-07-03T07:08:27Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "845624399", "name": "voicebot", "description": "", "url": "https://github.com/ruvnet/voicebot", "language": "TypeScript", "stars": 84, "forks": 21, "updatedAt": "2025-07-01T22:12:47Z", "createdAt": "2024-08-21T15:52:48Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-31T17:16:49Z", "lastReleaseAt": "2025-07-01T22:12:47Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "*********", "name": "promptlang", "description": "A Prompt based programming language for prompts and AI interactions. Simple and human-readable syntax for easy integration with APIs and data.", "url": "https://github.com/ruvnet/promptlang", "language": "", "stars": 116, "forks": 12, "updatedAt": "2025-06-29T12:53:18Z", "createdAt": "2023-03-18T15:45:21Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-12-12T21:37:43Z", "lastReleaseAt": "2025-06-29T12:53:18Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "*********", "name": "ARCADIA", "description": "AI-powered game engine for dynamic, personalized experiences in evolving worlds. Ethical, accessible, inclusive.", "url": "https://github.com/ruvnet/ARCADIA", "language": "Rust", "stars": 17, "forks": 5, "updatedAt": "2025-06-27T13:46:37Z", "createdAt": "2023-04-10T19:16:08Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-04-10T19:34:31Z", "lastReleaseAt": "2025-06-27T13:46:37Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "*********", "name": "agentic-employment", "description": "The first platform designed to empower organizations by automating and enhancing their employment processes through advanced autonomous agents.", "url": "https://github.com/ruvnet/agentic-employment", "language": "Python", "stars": 41, "forks": 8, "updatedAt": "2025-06-27T10:24:52Z", "createdAt": "2024-05-13T21:30:09Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-06-26T23:03:47Z", "lastReleaseAt": "2025-06-27T10:24:52Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "913037731", "name": "midstream", "description": "", "url": "https://github.com/ruvnet/midstream", "language": "Rust", "stars": 4, "forks": 5, "updatedAt": "2025-06-25T17:33:46Z", "createdAt": "2025-01-06T22:50:15Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-07T04:54:44Z", "lastReleaseAt": "2025-06-25T17:33:46Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "919539952", "name": "auto-browser", "description": "A command-line tool for configurable web automation with AI-assisted template creation.", "url": "https://github.com/ruvnet/auto-browser", "language": "Python", "stars": 66, "forks": 16, "updatedAt": "2025-06-23T14:07:14Z", "createdAt": "2025-01-20T15:22:42Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-24T16:39:47Z", "lastReleaseAt": "2025-06-23T14:07:14Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "810916651", "name": "agentic-voice", "description": "Agentic Chat App is an advanced AI-powered chat application designed for seamless real-time communication and intelligent responses. Built with Next.js, OpenAI, and Exa API, it leverages cutting-edge technologies to enhance user interactions and provide contextual, relevant information dynamically.", "url": "https://github.com/ruvnet/agentic-voice", "language": "TypeScript", "stars": 81, "forks": 26, "updatedAt": "2025-06-23T03:06:01Z", "createdAt": "2024-06-05T15:38:01Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-07-15T15:26:01Z", "lastReleaseAt": "2025-06-23T03:06:01Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "801575814", "name": "aws-dev", "description": "AWS Dev enviroment", "url": "https://github.com/ruvnet/aws-dev", "language": "Python", "stars": 7, "forks": 1, "updatedAt": "2025-06-22T19:27:55Z", "createdAt": "2024-05-16T13:52:55Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-05-20T21:50:26Z", "lastReleaseAt": "2025-06-22T19:27:55Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "985967042", "name": "codex-one", "description": "", "url": "https://github.com/ruvnet/codex-one", "language": "Python", "stars": 21, "forks": 4, "updatedAt": "2025-06-18T23:23:20Z", "createdAt": "2025-05-18T22:15:47Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-19T23:51:16Z", "lastReleaseAt": "2025-06-18T23:23:20Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "913374812", "name": "inflight", "description": "", "url": "https://github.com/ruvnet/inflight", "language": "Python", "stars": 34, "forks": 9, "updatedAt": "2025-06-18T05:15:01Z", "createdAt": "2025-01-07T15:06:48Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-09T17:33:12Z", "lastReleaseAt": "2025-06-18T05:15:01Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "627245480", "name": "Surfer", "description": "ChatGPT Web Surfer Plugin", "url": "https://github.com/ruvnet/Surfer", "language": "Python", "stars": 81, "forks": 21, "updatedAt": "2025-06-17T20:39:51Z", "createdAt": "2023-04-13T04:32:07Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-04-23T17:25:09Z", "lastReleaseAt": "2025-06-17T20:39:51Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "859638565", "name": "infinity-ui", "description": "", "url": "https://github.com/ruvnet/infinity-ui", "language": "JavaScript", "stars": 4, "forks": 3, "updatedAt": "2025-06-17T20:39:35Z", "createdAt": "2024-09-19T02:48:30Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-09-19T02:55:56Z", "lastReleaseAt": "2025-06-17T20:39:35Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "904888428", "name": "omnipotent", "description": "", "url": "https://github.com/ruvnet/omnipotent", "language": "TypeScript", "stars": 14, "forks": 6, "updatedAt": "2025-06-16T06:27:55Z", "createdAt": "2024-12-17T18:36:31Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-03-04T17:43:44Z", "lastReleaseAt": "2025-06-16T06:27:55Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "806230014", "name": "codecraft", "description": "CodeCraft is a powerful command-line interface (CLI) tool that simplifies the installation and configuration of OpenDevin, an AI-driven coding assistant. With CodeCraft, you can easily set up and customize your OpenDevin environment, enabling you to leverage the power of AI to enhance your software development workflow.", "url": "https://github.com/ruvnet/codecraft", "language": "Shell", "stars": 7, "forks": 1, "updatedAt": "2025-06-15T08:22:59Z", "createdAt": "2024-05-26T18:23:46Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-05-26T20:10:41Z", "lastReleaseAt": "2025-06-15T08:22:59Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "631334034", "name": "chatgpt-openai-api-plugin", "description": "A powerful ChatGPT plugin that integrates with the OpenAI API, enabling creative text generation, conversational AI, model fine-tuning, and more. Enhance ChatGPT's capabilities with seamless access to OpenAI's language models.", "url": "https://github.com/ruvnet/chatgpt-openai-api-plugin", "language": "Python", "stars": 142, "forks": 25, "updatedAt": "2025-06-13T02:57:20Z", "createdAt": "2023-04-22T17:33:13Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-05-02T01:48:25Z", "lastReleaseAt": "2025-06-13T02:57:20Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "981399488", "name": "agentic-difusion", "description": "a comprehensive diffusion-based code refinement model", "url": "https://github.com/ruvnet/agentic-difusion", "language": "Python", "stars": 13, "forks": 6, "updatedAt": "2025-06-06T22:12:21Z", "createdAt": "2025-05-11T02:39:53Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-12T14:49:46Z", "lastReleaseAt": "2025-06-06T22:12:21Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "882495346", "name": "agentic-security", "description": "", "url": "https://github.com/ruvnet/agentic-security", "language": "Python", "stars": 11, "forks": 8, "updatedAt": "2025-06-03T16:46:26Z", "createdAt": "2024-11-02T23:31:08Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-11-08T13:19:56Z", "lastReleaseAt": "2025-06-03T16:46:26Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "861069130", "name": "Electo1", "description": "", "url": "https://github.com/ruvnet/Electo1", "language": "Python", "stars": 4, "forks": 3, "updatedAt": "2025-06-03T16:46:25Z", "createdAt": "2024-09-21T23:12:50Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-09-22T00:24:41Z", "lastReleaseAt": "2025-06-03T16:46:25Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "856434892", "name": "a<PERSON>l", "description": "", "url": "https://github.com/ruvnet/aihl", "language": "JavaScript", "stars": 15, "forks": 10, "updatedAt": "2025-06-03T16:46:24Z", "createdAt": "2024-09-12T15:13:12Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-11-13T19:07:59Z", "lastReleaseAt": "2025-06-03T16:46:24Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "857531313", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "url": "https://github.com/ruvnet/drupaljs", "language": "JavaScript", "stars": 34, "forks": 6, "updatedAt": "2025-06-03T16:46:24Z", "createdAt": "2024-09-14T22:23:24Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-09-16T20:36:57Z", "lastReleaseAt": "2025-06-03T16:46:24Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "857931457", "name": "agenticsjs", "description": "", "url": "https://github.com/ruvnet/agenticsjs", "language": "JavaScript", "stars": 17, "forks": 3, "updatedAt": "2025-06-03T16:46:24Z", "createdAt": "2024-09-16T01:07:17Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-09-25T05:10:15Z", "lastReleaseAt": "2025-06-03T16:46:24Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "823829047", "name": "agentXNG", "description": "", "url": "https://github.com/ruvnet/agentXNG", "language": "Python", "stars": 12, "forks": 5, "updatedAt": "2025-06-03T16:46:21Z", "createdAt": "2024-07-03T20:04:44Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-07-05T14:46:15Z", "lastReleaseAt": "2025-06-03T16:46:21Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "731670597", "name": "agentX", "description": "", "url": "https://github.com/ruvnet/agentX", "language": "JavaScript", "stars": 4, "forks": 2, "updatedAt": "2025-06-03T16:46:10Z", "createdAt": "2023-12-14T15:49:25Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-12-14T15:59:47Z", "lastReleaseAt": "2025-06-03T16:46:10Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "728401234", "name": "ruvbot", "description": "ruvBot is an innovative tool designed to assist users in navigating and understanding the professional journey and achievements of Re<PERSON><PERSON> Cohen. It utilizes advanced AI to answer questions and provide insights, making it a valuable resource for anyone interested in <PERSON><PERSON><PERSON>'s career.", "url": "https://github.com/ruvnet/ruvbot", "language": "TypeScript", "stars": 34, "forks": 12, "updatedAt": "2025-06-03T16:16:04Z", "createdAt": "2023-12-06T21:34:32Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-12-06T23:52:16Z", "lastReleaseAt": "2025-06-03T16:16:04Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "889077096", "name": "aido", "description": " AI-Driven Decentralized Organization (AIDO) using Supabase and LangChain.js. It includes all necessary files, configurations, tests, and deployment components to get started. Feel free to extend and customize the system based on your specific requirements.  ", "url": "https://github.com/ruvnet/aido", "language": "TypeScript", "stars": 20, "forks": 7, "updatedAt": "2025-06-03T13:11:27Z", "createdAt": "2024-11-15T15:09:15Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-11-15T19:24:58Z", "lastReleaseAt": "2025-06-03T13:11:27Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "915460172", "name": "AiCodeCalc", "description": "", "url": "https://github.com/ruvnet/AiCodeCalc", "language": "TypeScript", "stars": 13, "forks": 4, "updatedAt": "2025-06-03T07:56:56Z", "createdAt": "2025-01-11T22:34:03Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-01-12T16:46:18Z", "lastReleaseAt": "2025-06-03T07:56:56Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "855431037", "name": "llamastack", "description": "An open source UI for Meta LLama Stack Apps / Agents", "url": "https://github.com/ruvnet/llamastack", "language": "JavaScript", "stars": 36, "forks": 14, "updatedAt": "2025-06-01T19:28:14Z", "createdAt": "2024-09-10T21:19:57Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-09-10T22:07:55Z", "lastReleaseAt": "2025-06-01T19:28:14Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "848969420", "name": "supabase-authentication", "description": "This project showcases a comprehensive authentication system using Supabase as the backend, implemented with both Streamlit and FastAPI frontends. It demonstrates how to build secure, user-friendly authentication flows in two popular Python web frameworks.", "url": "https://github.com/ruvnet/supabase-authentication", "language": "Python", "stars": 10, "forks": 2, "updatedAt": "2025-05-22T21:10:15Z", "createdAt": "2024-08-28T18:28:54Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-08-30T16:13:10Z", "lastReleaseAt": "2025-05-22T21:10:15Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "972415370", "name": "Roo-Code-Docs", "description": "This is the documentation for Roo Code (prev. Roo Cline), an AI-powered autonomous coding agent that lives in your editor.", "url": "https://github.com/ruvnet/Roo-Code-Docs", "language": "", "stars": 0, "forks": 0, "updatedAt": "2025-05-14T02:41:31Z", "createdAt": "2025-04-25T03:35:25Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-25T03:55:09Z", "lastReleaseAt": "2025-05-14T02:41:31Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "812442828", "name": "micro-agent", "description": "An AI agent that writes code for you", "url": "https://github.com/ruvnet/micro-agent", "language": "", "stars": 12, "forks": 3, "updatedAt": "2025-05-13T20:06:26Z", "createdAt": "2024-06-08T22:52:54Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-06-08T23:12:35Z", "lastReleaseAt": "2025-05-13T20:06:26Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "875277219", "name": "agentic-scraper", "description": "", "url": "https://github.com/ruvnet/agentic-scraper", "language": "Python", "stars": 7, "forks": 1, "updatedAt": "2025-05-13T20:03:01Z", "createdAt": "2024-10-19T14:58:43Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-10-21T13:55:55Z", "lastReleaseAt": "2025-05-13T20:03:01Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "615841397", "name": "aiscreenplay", "description": "The Screenplay Generator is a web application that allows users to generate a TV or movie screenplay based on a scene template. ", "url": "https://github.com/ruvnet/aiscreenplay", "language": "Python", "stars": 9, "forks": 4, "updatedAt": "2025-05-11T05:18:24Z", "createdAt": "2023-03-18T20:42:40Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-03-18T20:44:27Z", "lastReleaseAt": "2025-05-11T05:18:24Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "868123090", "name": "agentic-preview", "description": "Agentic Preview is an asynchronous FastAPI backend service that allows users to deploy preview environments using Fly.io.", "url": "https://github.com/ruvnet/agentic-preview", "language": "JavaScript", "stars": 9, "forks": 8, "updatedAt": "2025-05-09T15:26:08Z", "createdAt": "2024-10-05T14:37:56Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-10-11T04:47:21Z", "lastReleaseAt": "2025-05-09T15:26:08Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "979049359", "name": "agentics-meetup", "description": "Sample Starting", "url": "https://github.com/ruvnet/agentics-meetup", "language": "", "stars": 0, "forks": 0, "updatedAt": "2025-05-06T23:21:53Z", "createdAt": "2025-05-06T23:21:50Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-05-06T23:21:50Z", "lastReleaseAt": "2025-05-06T23:21:53Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "618120669", "name": "chatgpt_plugin_python", "description": "A simple To-do ChatGPT Plugin using python and deployed on replit. ", "url": "https://github.com/ruvnet/chatgpt_plugin_python", "language": "Python", "stars": 87, "forks": 18, "updatedAt": "2025-04-25T07:13:37Z", "createdAt": "2023-03-23T19:49:20Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-03-23T20:10:05Z", "lastReleaseAt": "2025-04-25T07:13:37Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "805522096", "name": "supa-ruv", "description": "Everything you need to use Supabase + Ai", "url": "https://github.com/ruvnet/supa-ruv", "language": "Shell", "stars": 5, "forks": 1, "updatedAt": "2025-04-22T19:11:07Z", "createdAt": "2024-05-24T19:04:29Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-05-24T21:07:50Z", "lastReleaseAt": "2025-04-22T19:11:07Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "864539613", "name": "ruv-engineer", "description": "rUv-Engineer - let's you describe UI using your imagination, then see it rendered live.", "url": "https://github.com/ruvnet/ruv-engineer", "language": "TypeScript", "stars": 8, "forks": 3, "updatedAt": "2025-04-16T00:26:33Z", "createdAt": "2024-09-28T13:46:10Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-09-28T15:00:16Z", "lastReleaseAt": "2025-04-16T00:26:33Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "926227859", "name": "tariffic", "description": "", "url": "https://github.com/ruvnet/tariffic", "language": "TypeScript", "stars": 1, "forks": 0, "updatedAt": "2025-04-14T17:00:32Z", "createdAt": "2025-02-02T20:58:35Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-04-07T17:31:36Z", "lastReleaseAt": "2025-04-14T17:00:32Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "618254924", "name": "ChatGPT-Trading-Bot-for-KuCoin", "description": "A trading script for KuCoin that continuously places buy and sell orders based on market data and a predictive model by OpenAI's GPT-3.5", "url": "https://github.com/ruvnet/ChatGPT-Trading-Bot-for-KuCoin", "language": "Python", "stars": 20, "forks": 5, "updatedAt": "2025-04-12T22:15:50Z", "createdAt": "2023-03-24T04:22:27Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2023-03-24T04:22:48Z", "lastReleaseAt": "2025-04-12T22:15:50Z", "isPrivate": false, "defaultBranch": "ChatGPT-trading-bot"}}, {"id": "921222359", "name": "hacker-league", "description": "", "url": "https://github.com/ruvnet/hacker-league", "language": "Python", "stars": 32, "forks": 11, "updatedAt": "2025-04-03T11:43:26Z", "createdAt": "2025-01-23T15:12:59Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2025-02-06T17:43:37Z", "lastReleaseAt": "2025-04-03T11:43:26Z", "isPrivate": false, "defaultBranch": "main"}}, {"id": "809205250", "name": "markov-chains", "description": "", "url": "https://github.com/ruvnet/markov-chains", "language": "Python", "stars": 4, "forks": 1, "updatedAt": "2025-03-31T09:09:10Z", "createdAt": "2024-06-02T02:32:41Z", "authorId": "1752973380294", "authorName": "ruvnet", "category": "未分类", "metadata": {"lastCommitAt": "2024-06-02T03:21:22Z", "lastReleaseAt": "2025-03-31T09:09:10Z", "isPrivate": false, "defaultBranch": "main"}}]