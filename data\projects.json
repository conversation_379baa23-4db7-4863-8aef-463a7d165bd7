[{"id": "1753045194800", "name": "claude-flow", "fullName": "ruvnet/claude-flow", "owner": "ruvnet", "description": "Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.", "url": "https://github.com/ruvnet/claude-flow", "categoryId": "1752971521539", "isActive": true, "isWatching": true, "isFavorite": false, "createdAt": "2025-07-20T20:59:54.800Z", "updatedAt": "2025-07-20T21:34:21Z", "metadata": {"priority": 3, "isPrivate": false, "defaultBranch": "main", "stars": 2660, "forks": 399, "watchers": 2660, "lastCommitAt": "2025-07-20T17:51:08Z", "lastReleaseAt": "2025-07-20T21:34:21Z"}, "language": "TypeScript", "stars": 2660, "forks": 399}, {"id": "1753045752035", "name": "<PERSON><PERSON>", "fullName": "Hale<PERSON><PERSON>se/Claudia<PERSON>", "owner": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "url": "https://github.com/Haleclipse/<PERSON>", "categoryId": "1752971521539", "isActive": true, "isWatching": true, "isFavorite": false, "createdAt": "2025-07-20T21:09:12.035Z", "updatedAt": "2025-07-20T17:50:15Z", "metadata": {"priority": 3, "isPrivate": false, "defaultBranch": "master", "stars": 81, "forks": 9, "watchers": 81, "lastCommitAt": "2025-07-17T20:47:52Z", "lastReleaseAt": "2025-07-20T17:50:15Z"}, "language": "TypeScript", "stars": 81, "forks": 9}, {"id": "1753046591005", "name": "claude-task-master", "fullName": "eyal<PERSON><PERSON><PERSON>/claude-task-master", "owner": "eyaltoledano", "description": "An AI-powered task-management system you can drop into Cursor, Lovable, Windsurf, Roo, and others.", "url": "https://github.com/eyaltoledano/claude-task-master", "categoryId": "1752971521539", "isActive": true, "isWatching": true, "isFavorite": false, "createdAt": "2025-07-20T21:23:11.005Z", "updatedAt": "2025-07-20T22:15:35Z", "metadata": {"priority": 3, "stars": 19080, "forks": 1875, "watchers": 19080, "lastCommitAt": "2025-07-19T21:58:10Z", "lastReleaseAt": "2025-07-20T22:15:35Z", "isPrivate": false, "defaultBranch": "main"}, "language": "JavaScript", "stars": 19080, "forks": 1875}]