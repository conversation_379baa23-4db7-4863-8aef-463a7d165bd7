{"general": {"language": "zh", "theme": "dark", "startWithSystem": false, "minimizeToTray": true, "closeToTray": false, "autoUpdate": true, "telemetry": false, "crashReporting": true}, "github": {"token": "*********************************************************************************************", "apiUrl": "https://api.github.com", "timeout": 15, "retryAttempts": 2, "rateLimit": {"enabled": true, "requestsPerHour": 4500}}, "ui": {"theme": {"mode": "dark", "primaryColor": "#1890ff", "accentColor": "#52c41a", "borderRadius": 6}, "layout": {"sidebarWidth": 240, "contentPadding": 24, "compactMode": false, "showStatusBar": true, "showToolbar": true}, "typography": {"fontSize": "medium", "fontFamily": "system-ui", "lineHeight": 1.5}, "animation": {"enabled": true, "duration": 200, "easing": "ease-in-out"}, "window": {"width": 1200, "height": 800, "maximized": false, "alwaysOnTop": false, "opacity": 1}}, "notifications": {"enabled": true, "types": {"newRelease": true, "newRepository": true, "starMilestone": true, "updateAvailable": true, "syncComplete": false, "syncError": true}, "delivery": {"desktop": true, "sound": true, "badge": true}, "schedule": {"enabled": false, "startTime": "09:00", "endTime": "18:00", "weekdays": [true, true, true, true, true, false, false]}}, "sync": {"enabled": true, "interval": 60, "autoSync": true, "syncOnStartup": true, "batchSize": 50, "concurrent": 3, "timeout": 30, "retryAttempts": 3, "retryDelay": 5000, "filters": {"categories": [], "authors": [], "projects": [], "excludeArchived": true, "excludePrivate": false, "excludeForks": false, "minStars": 0, "maxAge": 365}}, "backup": {"enabled": false, "interval": 24, "maxBackups": 7, "location": "", "compression": true, "encryption": false, "includeSettings": true, "includeCache": false, "includeLogs": false, "autoCleanup": true}, "advanced": {"logging": {"level": "info", "maxFiles": 5, "maxSize": "10MB", "format": "json", "console": true, "file": true}, "performance": {"enableMetrics": false, "sampleRate": 0.1, "maxMemoryUsage": 512, "gcThreshold": 80, "enableProfiling": false}, "security": {"enableEncryption": false, "encryptionAlgorithm": "aes-256-gcm", "sessionTimeout": 60, "maxLoginAttempts": 5, "lockoutDuration": 15, "enableAuditLog": false}, "experimental": {"enableBetaFeatures": false, "enableDebugMode": false, "enableDevTools": false, "features": []}, "database": {"engine": "json", "location": "./data", "maxSize": 100, "vacuumInterval": 7, "enableWAL": false, "enableForeignKeys": true}, "network": {"userAgent": "GitHub-Monitor/1.0.0", "connectTimeout": 10, "readTimeout": 30, "maxRedirects": 5, "enableKeepAlive": true, "enableCompression": true}}}