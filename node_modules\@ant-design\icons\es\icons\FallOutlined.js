import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FallOutlinedSvg from "@ant-design/icons-svg/es/asn/FallOutlined";
import AntdIcon from "../components/AntdIcon";
var FallOutlined = function FallOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FallOutlinedSvg
  }));
};

/**![fall](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNS45IDgwNGwtMjQtMTk5LjJjLS44LTYuNi04LjktOS40LTEzLjYtNC43TDgyOSA2NTkuNSA1NTcuNyAzODguM2MtNi4zLTYuMi0xNi40LTYuMi0yMi42IDBMNDMzLjMgNDkwIDE1Ni42IDIxMy4zYTguMDMgOC4wMyAwIDAwLTExLjMgMGwtNDUgNDUuMmE4LjAzIDguMDMgMCAwMDAgMTEuM0w0MjIgNTkxLjdjNi4yIDYuMyAxNi40IDYuMyAyMi42IDBMNTQ2LjQgNDkwbDIyNi4xIDIyNi01OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDA0LjcgMTMuNmwxOTkuMiAyNGM1LjEuNyA5LjUtMy43IDguOC04Ljl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FallOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FallOutlined';
}
export default RefIcon;