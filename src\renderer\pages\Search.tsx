/**
 * 搜索页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Input,
  Select,
  Space,
  List,
  Avatar,
  Tag,
  Typography,
  Empty,
  Spin,
  Button,
  Tabs,
} from 'antd';
import {
  SearchOutlined,
  FolderOutlined,
  UserOutlined,
  ProjectOutlined,
  G<PERSON><PERSON>Outlined,
  StarOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import { selectAuthors } from '@/renderer/store/slices/authorSlice';
import { selectProjects } from '@/renderer/store/slices/projectSlice';
import {
  selectQuery,
  selectSearchResults,
  selectIsSearching,
  setQuery,
  setResults,
  setSearching,
  addToHistory,
} from '@/renderer/store/slices/searchSlice';

const { Title, Text } = Typography;
const { Search: AntSearch } = Input;

const Search: React.FC = () => {
  const dispatch = useAppDispatch();
  const query = useAppSelector(selectQuery);
  const searchResults = useAppSelector(selectSearchResults);
  const isSearching = useAppSelector(selectIsSearching);
  const categories = useAppSelector(selectCategories);
  const authors = useAppSelector(selectAuthors);
  const projects = useAppSelector(selectProjects);

  const [searchType, setSearchType] = useState<'all' | 'categories' | 'authors' | 'projects'>('all');
  const [filteredResults, setFilteredResults] = useState<any>({
    categories: [],
    authors: [],
    projects: [],
  });

  useEffect(() => {
    if (query.trim()) {
      performSearch(query, searchType);
    } else {
      setFilteredResults({ categories: [], authors: [], projects: [] });
    }
  }, [query, searchType, categories, authors, projects]);

  const performSearch = (searchQuery: string, type: string) => {
    dispatch(setSearching(true));
    
    setTimeout(() => {
      const lowerQuery = searchQuery.toLowerCase();
      
      const searchCategories = type === 'all' || type === 'categories' 
        ? categories.filter(category =>
            category.name.toLowerCase().includes(lowerQuery) ||
            (category.description && category.description.toLowerCase().includes(lowerQuery))
          )
        : [];

      const searchAuthors = type === 'all' || type === 'authors'
        ? authors.filter(author =>
            author.username.toLowerCase().includes(lowerQuery) ||
            (author.displayName && author.displayName.toLowerCase().includes(lowerQuery)) ||
            (author.bio && author.bio.toLowerCase().includes(lowerQuery))
          )
        : [];

      const searchProjects = type === 'all' || type === 'projects'
        ? projects.filter(project =>
            project.name.toLowerCase().includes(lowerQuery) ||
            project.fullName.toLowerCase().includes(lowerQuery) ||
            (project.description && project.description.toLowerCase().includes(lowerQuery))
          )
        : [];

      const results = {
        categories: searchCategories,
        authors: searchAuthors,
        projects: searchProjects,
      };

      setFilteredResults(results);
      dispatch(setResults([...searchCategories, ...searchAuthors, ...searchProjects]));
      dispatch(setSearching(false));
    }, 500);
  };

  const handleSearch = (value: string) => {
    dispatch(setQuery(value));
    if (value.trim()) {
      dispatch(addToHistory(value));
    }
  };

  const renderCategoryItem = (category: any) => (
    <List.Item
      actions={[
        <Button type="link" icon={<FolderOutlined />}>
          查看
        </Button>
      ]}
    >
      <List.Item.Meta
        avatar={<Avatar icon={<FolderOutlined />} style={{ backgroundColor: category.color }} />}
        title={category.name}
        description={category.description || '暂无描述'}
      />
      <div>
        <Tag color={category.color}>分类</Tag>
        <Text type="secondary">权重: {category.weight}</Text>
      </div>
    </List.Item>
  );

  const renderAuthorItem = (author: any) => (
    <List.Item
      actions={[
        <Button type="link" icon={<GithubOutlined />}>
          GitHub
        </Button>,
        <Button type="link" icon={<UserOutlined />}>
          查看
        </Button>
      ]}
    >
      <List.Item.Meta
        avatar={<Avatar src={author.avatar} icon={<UserOutlined />} />}
        title={author.displayName || author.username}
        description={`@${author.username} ${author.bio || ''}`}
      />
      <div>
        <Tag color="blue">作者</Tag>
        {author.isWatching && <Tag color="green">监控中</Tag>}
        <Text type="secondary">
          仓库: {author.metadata?.repositoryCount || 0}
        </Text>
      </div>
    </List.Item>
  );

  const renderProjectItem = (project: any) => (
    <List.Item
      actions={[
        <Button type="link" icon={<GithubOutlined />}>
          GitHub
        </Button>,
        <Button type="link" icon={<ProjectOutlined />}>
          查看
        </Button>
      ]}
    >
      <List.Item.Meta
        avatar={<Avatar icon={<ProjectOutlined />} />}
        title={project.name}
        description={project.description || '暂无描述'}
      />
      <div>
        <Tag color="purple">项目</Tag>
        {project.isFavorite && <Tag color="red">收藏</Tag>}
        {project.isWatching && <Tag color="green">监控中</Tag>}
        <Space>
          <Text type="secondary">
            <StarOutlined /> {project.metadata?.stars || 0}
          </Text>
          <Text type="secondary">
            语言: {project.language || '未知'}
          </Text>
        </Space>
      </div>
    </List.Item>
  );

  const tabItems = [
    {
      key: 'all',
      label: `全部 (${filteredResults.categories.length + filteredResults.authors.length + filteredResults.projects.length})`,
      children: (
        <div>
          {filteredResults.categories.length > 0 && (
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>分类 ({filteredResults.categories.length})</Title>
              <List
                dataSource={filteredResults.categories}
                renderItem={renderCategoryItem}
              />
            </div>
          )}
          
          {filteredResults.authors.length > 0 && (
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>作者 ({filteredResults.authors.length})</Title>
              <List
                dataSource={filteredResults.authors}
                renderItem={renderAuthorItem}
              />
            </div>
          )}
          
          {filteredResults.projects.length > 0 && (
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>项目 ({filteredResults.projects.length})</Title>
              <List
                dataSource={filteredResults.projects}
                renderItem={renderProjectItem}
              />
            </div>
          )}
          
          {!query && (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="请输入搜索关键词"
            />
          )}
          
          {query && filteredResults.categories.length === 0 && 
           filteredResults.authors.length === 0 && 
           filteredResults.projects.length === 0 && (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="未找到相关结果"
            />
          )}
        </div>
      ),
    },
    {
      key: 'categories',
      label: `分类 (${filteredResults.categories.length})`,
      children: (
        <List
          dataSource={filteredResults.categories}
          renderItem={renderCategoryItem}
          locale={{ emptyText: '未找到相关分类' }}
        />
      ),
    },
    {
      key: 'authors',
      label: `作者 (${filteredResults.authors.length})`,
      children: (
        <List
          dataSource={filteredResults.authors}
          renderItem={renderAuthorItem}
          locale={{ emptyText: '未找到相关作者' }}
        />
      ),
    },
    {
      key: 'projects',
      label: `项目 (${filteredResults.projects.length})`,
      children: (
        <List
          dataSource={filteredResults.projects}
          renderItem={renderProjectItem}
          locale={{ emptyText: '未找到相关项目' }}
        />
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={3} style={{ margin: 0, marginBottom: '16px' }}>
              搜索
            </Title>
            <Space.Compact style={{ width: '100%' }}>
              <Select
                value={searchType}
                onChange={setSearchType}
                style={{ width: 120 }}
              >
                <Select.Option value="all">全部</Select.Option>
                <Select.Option value="categories">分类</Select.Option>
                <Select.Option value="authors">作者</Select.Option>
                <Select.Option value="projects">项目</Select.Option>
              </Select>
              <AntSearch
                placeholder="搜索分类、作者或项目..."
                value={query}
                onChange={(e) => dispatch(setQuery(e.target.value))}
                onSearch={handleSearch}
                style={{ flex: 1 }}
                enterButton={<SearchOutlined />}
                size="large"
              />
            </Space.Compact>
          </div>

          <Spin spinning={isSearching}>
            <Tabs
              activeKey={searchType}
              onChange={(key) => setSearchType(key as any)}
              items={tabItems}
            />
          </Spin>
        </Space>
      </Card>
    </div>
  );
};

export default Search;
