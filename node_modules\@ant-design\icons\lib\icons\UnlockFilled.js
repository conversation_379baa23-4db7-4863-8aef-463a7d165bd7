"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _UnlockFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/UnlockFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var UnlockFilled = function UnlockFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _UnlockFilled.default
  }));
};

/**![unlock](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA0NjRIMzMyVjI0MGMwLTMwLjkgMjUuMS01NiA1Ni01NmgyNDhjMzAuOSAwIDU2IDI1LjEgNTYgNTZ2NjhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNjhjMC03MC43LTU3LjMtMTI4LTEyOC0xMjhIMzg4Yy03MC43IDAtMTI4IDU3LjMtMTI4IDEyOHYyMjRoLTY4Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYzODRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjQ5NmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTQwIDcwMXY1M2MwIDQuNC0zLjYgOC04IDhoLTQwYy00LjQgMC04LTMuNi04LTh2LTUzYTQ4LjAxIDQ4LjAxIDAgMTE1NiAweiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(UnlockFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UnlockFilled';
}
var _default = exports.default = RefIcon;