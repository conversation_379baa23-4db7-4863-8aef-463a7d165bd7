/**
 * 窗口管理模块
 * 负责创建和管理应用窗口
 */

import { BrowserWindow, screen } from 'electron';
import * as path from 'path';

// 开发环境标识
const isDev = process.env.NODE_ENV === 'development';

// 窗口实例
let mainWindow: BrowserWindow | null = null;

/**
 * 获取窗口默认配置
 */
function getWindowConfig() {
  const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;
  
  return {
    width: Math.min(1200, screenWidth - 100),
    height: Math.min(800, screenHeight - 100),
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true,
    },
    show: false,
    titleBarStyle: 'default' as const,
    icon: path.join(__dirname, '../../public/icon.png'),
    backgroundColor: '#1f1f1f',
  };
}

/**
 * 创建主窗口
 */
export function createMainWindow(): BrowserWindow {
  if (mainWindow) {
    return mainWindow;
  }

  mainWindow = new BrowserWindow(getWindowConfig());

  // 加载应用
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    // 开发环境下打开开发者工具
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
      
      // 居中显示
      const { width, height } = screen.getPrimaryDisplay().workAreaSize;
      const windowBounds = mainWindow.getBounds();
      const x = Math.round((width - windowBounds.width) / 2);
      const y = Math.round((height - windowBounds.height) / 2);
      mainWindow.setPosition(x, y);
    }
  });

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 阻止新窗口打开
  mainWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });

  return mainWindow;
}

/**
 * 获取主窗口实例
 */
export function getMainWindow(): BrowserWindow | null {
  return mainWindow;
}

/**
 * 关闭主窗口
 */
export function closeMainWindow(): void {
  if (mainWindow) {
    mainWindow.close();
  }
}

/**
 * 最小化主窗口
 */
export function minimizeMainWindow(): void {
  if (mainWindow) {
    mainWindow.minimize();
  }
}

/**
 * 最大化/还原主窗口
 */
export function toggleMaximizeMainWindow(): void {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
}

/**
 * 检查窗口是否存在
 */
export function hasMainWindow(): boolean {
  return mainWindow !== null && !mainWindow.isDestroyed();
}
