/**
 * 内容区域组件
 * 负责根据当前视图显示对应的页面内容
 */

import React from 'react';
import Home from '../../pages/Home';
import Categories from '../../pages/Categories';
import Authors from '../../pages/Authors';
import Projects from '../../pages/Projects';
import Search from '../../pages/Search';
import Statistics from '../../pages/Statistics';
import Settings from '../../pages/Settings';

interface ContentProps {
  currentView: string;
  children?: React.ReactNode;
}

const Content: React.FC<ContentProps> = ({ currentView, children }) => {
  const getViewContent = () => {
    switch (currentView) {
      case 'home':
        return <Home />;
      case 'search':
        return <Search />;
      case 'categories':
        return <Categories />;
      case 'authors':
        return <Authors />;
      case 'projects':
        return <Projects />;
      case 'statistics':
        return <Statistics />;
      case 'settings':
        return <Settings />;
      default:
        return <Home />;
    }
  };

  return (
    <div className="content-area">
      {children || getViewContent()}
    </div>
  );
};

export default Content;
