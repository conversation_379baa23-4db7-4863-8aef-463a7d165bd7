/**
 * 内容区域组件
 * 负责根据当前视图显示对应的页面内容
 */

import React from 'react';
import { Card, Typography } from 'antd';
import Home from '../../pages/Home';
import Categories from '../../pages/Categories';
import Authors from '../../pages/Authors';
import Projects from '../../pages/Projects';
import Search from '../../pages/Search';
import Statistics from '../../pages/Statistics';
import Settings from '../../pages/Settings';

const { Title } = Typography;

interface ContentProps {
  currentView: string;
  children?: React.ReactNode;
}

const Content: React.FC<ContentProps> = ({ currentView, children }) => {
  console.log('Content rendering with currentView:', currentView);

  const getViewContent = () => {
    console.log('Getting view content for:', currentView);

    switch (currentView) {
      case 'home':
        return <Home />;
      case 'search':
        return <Search />;
      case 'categories':
        return <Categories />;
      case 'authors':
        return <Authors />;
      case 'projects':
        return <Projects />;
      case 'statistics':
        return <Statistics />;
      case 'settings':
        return <Settings />;
      default:
        console.log('Default case, returning Home');
        return <Home />;
    }
  };

  return (
    <div className="content-area">
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: '#1890ff',
        color: 'white',
        padding: '4px 8px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 1000
      }}>
        当前视图: {currentView}
      </div>
      {children || getViewContent()}
    </div>
  );
};

export default Content;
