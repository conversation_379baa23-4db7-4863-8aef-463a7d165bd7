/**
 * 作者项目数据管理工具
 * 为每个作者维护单独的JSON文件，包含已知项目列表和已读时间戳
 */

export interface AuthorProjectData {
  authorId: string;
  authorName: string;
  knownProjects: string[]; // 已知项目ID列表
  readTimestamps: Record<string, string>; // 项目ID -> 已读时间戳
  lastUpdated: string;
}

export interface ProjectItem {
  id: number;
  name: string;
  full_name: string;
  html_url: string;
  description: string;
  stargazers_count: number;
  watchers_count: number;
  updated_at: string;
  created_at: string;
  pushed_at?: string;
  language: string;
  private: boolean;
}

/**
 * 获取作者项目数据文件路径
 */
function getAuthorProjectsFilePath(authorId: string): string {
  return `data/authors/${authorId}-projects.json`;
}

/**
 * 加载作者项目数据
 */
export async function loadAuthorProjectData(authorId: string, authorName: string): Promise<AuthorProjectData> {
  try {
    if (window.electronAPI) {
      const filePath = getAuthorProjectsFilePath(authorId);
      const response = await window.electronAPI.fs.readFile(filePath);
      
      if (response.success && response.data) {
        const data = JSON.parse(response.data);
        return {
          authorId,
          authorName,
          knownProjects: data.knownProjects || [],
          readTimestamps: data.readTimestamps || {},
          lastUpdated: data.lastUpdated || new Date().toISOString()
        };
      }
    }
  } catch (error) {
    console.log(`No existing data for author ${authorId}, creating new data structure`);
  }

  // 如果文件不存在或读取失败，返回默认数据结构
  return {
    authorId,
    authorName,
    knownProjects: [],
    readTimestamps: {},
    lastUpdated: new Date().toISOString()
  };
}

/**
 * 保存作者项目数据
 */
export async function saveAuthorProjectData(data: AuthorProjectData): Promise<boolean> {
  try {
    if (window.electronAPI) {
      const filePath = getAuthorProjectsFilePath(data.authorId);
      
      // 确保目录存在
      await window.electronAPI.fs.ensureDir('data/authors');
      
      // 更新最后修改时间
      const updatedData = {
        ...data,
        lastUpdated: new Date().toISOString()
      };
      
      const response = await window.electronAPI.fs.writeFile(filePath, JSON.stringify(updatedData, null, 2));
      return response.success;
    }
    return false;
  } catch (error) {
    console.error('Failed to save author project data:', error);
    return false;
  }
}

/**
 * 检测新项目（GitHub项目列表 - 本地已知项目列表）
 */
export function detectNewProjects(githubProjects: ProjectItem[], knownProjects: string[]): ProjectItem[] {
  return githubProjects.filter(project => 
    !knownProjects.includes(project.id.toString())
  );
}

/**
 * 检测需要查看的更新项目（金色按钮项目）
 */
export function detectUpdatedProjects(
  githubProjects: ProjectItem[], 
  knownProjects: string[], 
  readTimestamps: Record<string, string>
): ProjectItem[] {
  return githubProjects.filter(project => {
    const projectId = project.id.toString();
    
    // 只检查已知项目
    if (!knownProjects.includes(projectId)) {
      return false;
    }
    
    // 检查是否有已读时间戳
    const readTimestamp = readTimestamps[projectId];
    if (!readTimestamp) {
      // 没有已读时间戳，需要查看
      return true;
    }
    
    // 比较项目更新时间和已读时间
    const readTime = new Date(readTimestamp);
    const projectUpdatedAt = new Date(project.updated_at || project.pushed_at || project.created_at);
    
    // 项目更新时间 > 已读时间 = 需要查看（金色按钮）
    return projectUpdatedAt > readTime;
  });
}

/**
 * 标记项目为已读
 */
export async function markProjectAsRead(
  authorId: string, 
  authorName: string, 
  projectId: string
): Promise<boolean> {
  try {
    // 加载当前数据
    const data = await loadAuthorProjectData(authorId, authorName);
    
    // 添加到已知项目列表（如果不存在）
    if (!data.knownProjects.includes(projectId)) {
      data.knownProjects.push(projectId);
    }
    
    // 更新已读时间戳
    data.readTimestamps[projectId] = new Date().toISOString();
    
    // 保存数据
    return await saveAuthorProjectData(data);
  } catch (error) {
    console.error('Failed to mark project as read:', error);
    return false;
  }
}

/**
 * 批量标记项目为已读
 */
export async function markMultipleProjectsAsRead(
  authorId: string, 
  authorName: string, 
  projectIds: string[]
): Promise<boolean> {
  try {
    // 加载当前数据
    const data = await loadAuthorProjectData(authorId, authorName);
    
    const currentTime = new Date().toISOString();
    
    // 批量处理
    projectIds.forEach(projectId => {
      // 添加到已知项目列表（如果不存在）
      if (!data.knownProjects.includes(projectId)) {
        data.knownProjects.push(projectId);
      }
      
      // 更新已读时间戳
      data.readTimestamps[projectId] = currentTime;
    });
    
    // 保存数据
    return await saveAuthorProjectData(data);
  } catch (error) {
    console.error('Failed to mark multiple projects as read:', error);
    return false;
  }
}

/**
 * 检查项目是否需要查看（用于按钮颜色判断）
 */
export function checkProjectNeedsReview(
  project: ProjectItem, 
  knownProjects: string[], 
  readTimestamps: Record<string, string>
): boolean {
  const projectId = project.id.toString();
  
  // 如果是新项目（不在已知项目列表中），需要查看
  if (!knownProjects.includes(projectId)) {
    return true;
  }
  
  // 检查已读时间戳
  const readTimestamp = readTimestamps[projectId];
  if (!readTimestamp) {
    // 没有已读时间戳，需要查看
    return true;
  }
  
  // 比较项目更新时间和已读时间
  const readTime = new Date(readTimestamp);
  const projectUpdatedAt = new Date(project.updated_at || project.pushed_at || project.created_at);
  
  // 项目更新时间 > 已读时间 = 需要查看（金色按钮）
  return projectUpdatedAt > readTime;
}

/**
 * 同步GitHub项目到本地已知项目列表（不更新已读状态）
 */
export async function syncGithubProjectsToKnown(
  authorId: string, 
  authorName: string, 
  githubProjects: ProjectItem[]
): Promise<boolean> {
  try {
    // 加载当前数据
    const data = await loadAuthorProjectData(authorId, authorName);
    
    // 获取所有GitHub项目ID
    const githubProjectIds = githubProjects.map(p => p.id.toString());
    
    // 合并到已知项目列表（去重）
    const allKnownProjects = [...new Set([...data.knownProjects, ...githubProjectIds])];
    
    // 只更新已知项目列表，不修改已读时间戳
    data.knownProjects = allKnownProjects;
    
    // 保存数据
    return await saveAuthorProjectData(data);
  } catch (error) {
    console.error('Failed to sync GitHub projects to known projects:', error);
    return false;
  }
}
