"use strict";
/**
 * 系统操作IPC处理器
 * 负责处理系统相关的IPC通信
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerSystemHandlers = registerSystemHandlers;
const electron_1 = require("electron");
const index_1 = require("./index");
const window_1 = require("../window");
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
/**
 * 注册系统操作IPC处理器
 */
function registerSystemHandlers() {
    // 打开外部链接
    electron_1.ipcMain.handle('system:openExternal', async (_, url) => {
        try {
            if (!url) {
                throw new Error('URL is required');
            }
            // 验证URL格式
            try {
                new URL(url);
            }
            catch {
                throw new Error('Invalid URL format');
            }
            await electron_1.shell.openExternal(url);
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('system:openExternal', error);
        }
    });
    // 显示消息框
    electron_1.ipcMain.handle('system:showMessageBox', async (_, options) => {
        try {
            const mainWindow = (0, window_1.getMainWindow)();
            if (!mainWindow) {
                throw new Error('Main window not available');
            }
            const result = await electron_1.dialog.showMessageBox(mainWindow, {
                type: options.type || 'info',
                title: options.title || '提示',
                message: options.message || '',
                detail: options.detail || '',
                buttons: options.buttons || ['确定'],
                defaultId: options.defaultId || 0,
                cancelId: options.cancelId,
                ...options,
            });
            return (0, index_1.handleIPCSuccess)(result);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('system:showMessageBox', error);
        }
    });
    // 显示打开文件对话框
    electron_1.ipcMain.handle('system:showOpenDialog', async (_, options) => {
        try {
            const mainWindow = (0, window_1.getMainWindow)();
            if (!mainWindow) {
                throw new Error('Main window not available');
            }
            const result = await electron_1.dialog.showOpenDialog(mainWindow, {
                title: options.title || '选择文件',
                defaultPath: options.defaultPath,
                buttonLabel: options.buttonLabel || '打开',
                filters: options.filters || [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] },
                ],
                properties: options.properties || ['openFile'],
                ...options,
            });
            return (0, index_1.handleIPCSuccess)(result);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('system:showOpenDialog', error);
        }
    });
    // 显示保存文件对话框
    electron_1.ipcMain.handle('system:showSaveDialog', async (_, options) => {
        try {
            const mainWindow = (0, window_1.getMainWindow)();
            if (!mainWindow) {
                throw new Error('Main window not available');
            }
            const result = await electron_1.dialog.showSaveDialog(mainWindow, {
                title: options.title || '保存文件',
                defaultPath: options.defaultPath,
                buttonLabel: options.buttonLabel || '保存',
                filters: options.filters || [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] },
                ],
                ...options,
            });
            return (0, index_1.handleIPCSuccess)(result);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('system:showSaveDialog', error);
        }
    });
    // 获取系统信息
    electron_1.ipcMain.handle('system:getSystemInfo', async () => {
        try {
            const os = require('os');
            const systemInfo = {
                platform: process.platform,
                arch: process.arch,
                version: process.version,
                osType: os.type(),
                osRelease: os.release(),
                totalMemory: os.totalmem(),
                freeMemory: os.freemem(),
                cpus: os.cpus().length,
            };
            return (0, index_1.handleIPCSuccess)(systemInfo);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('system:getSystemInfo', error);
        }
    });
    // 显示项目在文件管理器中
    electron_1.ipcMain.handle('system:showItemInFolder', async (_, fullPath) => {
        try {
            if (!fullPath) {
                throw new Error('File path is required');
            }
            electron_1.shell.showItemInFolder(fullPath);
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('system:showItemInFolder', error);
        }
    });
    // 文件系统操作 - 读取文件
    electron_1.ipcMain.handle('fs:readFile', async (_, filePath) => {
        try {
            if (!filePath) {
                throw new Error('File path is required');
            }
            const fullPath = path.resolve(filePath);
            const data = await fs.readFile(fullPath, 'utf8');
            return (0, index_1.handleIPCSuccess)(data);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('fs:readFile', error);
        }
    });
    // 文件系统操作 - 写入文件
    electron_1.ipcMain.handle('fs:writeFile', async (_, filePath, data) => {
        try {
            if (!filePath) {
                throw new Error('File path is required');
            }
            const fullPath = path.resolve(filePath);
            await fs.writeFile(fullPath, data, 'utf8');
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('fs:writeFile', error);
        }
    });
    // 文件系统操作 - 确保目录存在
    electron_1.ipcMain.handle('fs:ensureDir', async (_, dirPath) => {
        try {
            if (!dirPath) {
                throw new Error('Directory path is required');
            }
            const fullPath = path.resolve(dirPath);
            await fs.ensureDir(fullPath);
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('fs:ensureDir', error);
        }
    });
    // 文件系统操作 - 检查文件是否存在
    electron_1.ipcMain.handle('fs:pathExists', async (_, filePath) => {
        try {
            if (!filePath) {
                throw new Error('File path is required');
            }
            const fullPath = path.resolve(filePath);
            const exists = await fs.pathExists(fullPath);
            return (0, index_1.handleIPCSuccess)(exists);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('fs:pathExists', error);
        }
    });
    console.log('✓ System IPC handlers registered');
}
