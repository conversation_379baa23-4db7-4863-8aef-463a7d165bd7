/**
 * Preload脚本
 * 在渲染进程中暴露安全的API接口
 */

import { contextBridge, ipcRenderer } from 'electron';

// 定义暴露给渲染进程的API接口
export interface ElectronAPI {
  // 数据操作
  data: {
    getConfig: () => Promise<any>;
    setConfig: (config: any) => Promise<void>;
    getCategories: () => Promise<any[]>;
    setCategories: (categories: any[]) => Promise<void>;
    getAuthors: <AUTHORS>
    setAuthors: <AUTHORS>
    getProjects: () => Promise<any[]>;
    setProjects: (projects: any[]) => Promise<void>;
  };
  
  // GitHub API操作
  github: {
    checkRateLimit: () => Promise<any>;
    getUser: (username: string) => Promise<any>;
    getRepository: (owner: string, repo: string) => Promise<any>;
    getUserRepositories: (username: string) => Promise<any[]>;
  };
  
  // 系统操作
  system: {
    openExternal: (url: string) => Promise<void>;
    showMessageBox: (options: any) => Promise<any>;
    showOpenDialog: (options: any) => Promise<any>;
    showSaveDialog: (options: any) => Promise<any>;
  };
  
  // 应用操作
  app: {
    getVersion: () => Promise<string>;
    quit: () => void;
    minimize: () => void;
    maximize: () => void;
    close: () => void;
  };
}

// 实现API接口
const electronAPI: ElectronAPI = {
  data: {
    getConfig: () => ipcRenderer.invoke('data:getConfig'),
    setConfig: (config) => ipcRenderer.invoke('data:setConfig', config),
    getCategories: () => ipcRenderer.invoke('data:getCategories'),
    setCategories: (categories) => ipcRenderer.invoke('data:setCategories', categories),
    getAuthors: <AUTHORS>
    setAuthors: <AUTHORS>
    getProjects: () => ipcRenderer.invoke('data:getProjects'),
    setProjects: (projects) => ipcRenderer.invoke('data:setProjects', projects),
  },
  
  github: {
    checkRateLimit: () => ipcRenderer.invoke('github:checkRateLimit'),
    getUser: (username) => ipcRenderer.invoke('github:getUser', username),
    getRepository: (owner, repo) => ipcRenderer.invoke('github:getRepository', owner, repo),
    getUserRepositories: (username) => ipcRenderer.invoke('github:getUserRepositories', username),
  },
  
  system: {
    openExternal: (url) => ipcRenderer.invoke('system:openExternal', url),
    showMessageBox: (options) => ipcRenderer.invoke('system:showMessageBox', options),
    showOpenDialog: (options) => ipcRenderer.invoke('system:showOpenDialog', options),
    showSaveDialog: (options) => ipcRenderer.invoke('system:showSaveDialog', options),
  },
  
  app: {
    getVersion: () => ipcRenderer.invoke('app:getVersion'),
    quit: () => ipcRenderer.send('app:quit'),
    minimize: () => ipcRenderer.send('app:minimize'),
    maximize: () => ipcRenderer.send('app:maximize'),
    close: () => ipcRenderer.send('app:close'),
  },
};

// 将API暴露给渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明，供渲染进程使用
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
