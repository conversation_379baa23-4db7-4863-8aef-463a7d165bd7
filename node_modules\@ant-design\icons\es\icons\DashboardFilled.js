import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DashboardFilledSvg from "@ant-design/icons-svg/es/asn/DashboardFilled";
import AntdIcon from "../components/AntdIcon";
var DashboardFilled = function DashboardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DashboardFilledSvg
  }));
};

/**![dashboard](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNC44IDM4NS42YTQ0Ni43IDQ0Ni43IDAgMDAtOTYtMTQyLjQgNDQ2LjcgNDQ2LjcgMCAwMC0xNDIuNC05NkM2MzEuMSAxMjMuOCA1NzIuNSAxMTIgNTEyIDExMnMtMTE5LjEgMTEuOC0xNzQuNCAzNS4yYTQ0Ni43IDQ0Ni43IDAgMDAtMTQyLjQgOTYgNDQ2LjcgNDQ2LjcgMCAwMC05NiAxNDIuNEM3NS44IDQ0MC45IDY0IDQ5OS41IDY0IDU2MGMwIDEzMi43IDU4LjMgMjU3LjcgMTU5LjkgMzQzLjFsMS43IDEuNGM1LjggNC44IDEzLjEgNy41IDIwLjYgNy41aDUzMS43YzcuNSAwIDE0LjgtMi43IDIwLjYtNy41bDEuNy0xLjRDOTAxLjcgODE3LjcgOTYwIDY5Mi43IDk2MCA1NjBjMC02MC41LTExLjktMTE5LjEtMzUuMi0xNzQuNHpNNDgyIDIzMmMwLTQuNCAzLjYtOCA4LThoNDRjNC40IDAgOCAzLjYgOCA4djgwYzAgNC40LTMuNiA4LTggOGgtNDRjLTQuNCAwLTgtMy42LTgtOHYtODB6TTI3MCA1ODJjMCA0LjQtMy42IDgtOCA4aC04MGMtNC40IDAtOC0zLjYtOC04di00NGMwLTQuNCAzLjYtOCA4LThoODBjNC40IDAgOCAzLjYgOCA4djQ0em05MC43LTIwNC41bC0zMS4xIDMxLjFhOC4wMyA4LjAzIDAgMDEtMTEuMyAwTDI2MS43IDM1MmE4LjAzIDguMDMgMCAwMTAtMTEuM2wzMS4xLTMxLjFjMy4xLTMuMSA4LjItMy4xIDExLjMgMGw1Ni42IDU2LjZjMy4xIDMuMSAzLjEgOC4yIDAgMTEuM3ptMjkxLjEgODMuNmwtODQuNSA4NC41YzUgMTguNy4yIDM5LjQtMTQuNSA1NC4xYTU1Ljk1IDU1Ljk1IDAgMDEtNzkuMiAwIDU1Ljk1IDU1Ljk1IDAgMDEwLTc5LjIgNTUuODcgNTUuODcgMCAwMTU0LjEtMTQuNWw4NC41LTg0LjVjMy4xLTMuMSA4LjItMy4xIDExLjMgMGwyOC4zIDI4LjNjMy4xIDMuMSAzLjEgOC4xIDAgMTEuM3ptNDMtNTIuNGwtMzEuMS0zMS4xYTguMDMgOC4wMyAwIDAxMC0xMS4zbDU2LjYtNTYuNmMzLjEtMy4xIDguMi0zLjEgMTEuMyAwbDMxLjEgMzEuMWMzLjEgMy4xIDMuMSA4LjIgMCAxMS4zbC01Ni42IDU2LjZhOC4wMyA4LjAzIDAgMDEtMTEuMyAwek04NDYgNTgyYzAgNC40LTMuNiA4LTggOGgtODBjLTQuNCAwLTgtMy42LTgtOHYtNDRjMC00LjQgMy42LTggOC04aDgwYzQuNCAwIDggMy42IDggOHY0NHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DashboardFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DashboardFilled';
}
export default RefIcon;