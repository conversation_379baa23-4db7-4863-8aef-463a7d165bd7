/**
 * 侧边栏组件
 * 负责应用的导航菜单
 */

import React, { useState } from 'react';
import { Menu, Typography, Button, Space, Divider } from 'antd';
import {
  HomeOutlined,
  FolderOutlined,
  UserOutlined,
  ProjectOutlined,
  BarChartOutlined,
  SettingOutlined,
  SearchOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/renderer/store';
import { setCurrentView, selectCurrentView, showNotification } from '@/renderer/store/slices/appSlice';

const { Title } = Typography;

const Sidebar: React.FC = () => {
  const dispatch = useAppDispatch();
  const currentView = useAppSelector(selectCurrentView);
  const [isUpdating, setIsUpdating] = useState(false);

  console.log('Sidebar rendering with currentView:', currentView);
  console.log('Dispatch function:', typeof dispatch);

  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: 'search',
      icon: <SearchOutlined />,
      label: '搜索',
    },
    {
      key: 'categories',
      icon: <FolderOutlined />,
      label: '分类管理',
    },
    {
      key: 'authors',
      icon: <UserOutlined />,
      label: '作者管理',
    },
    {
      key: 'projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
    },
    {
      key: 'statistics',
      icon: <BarChartOutlined />,
      label: '统计分析',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    console.log('Menu clicked:', key, 'Current view:', currentView);
    dispatch(setCurrentView(key));
  };

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      console.log('开始更新检查...');

      // 调用主进程的更新检查API
      if (window.electronAPI) {
        // 这里可以调用实际的更新检查API
        // const result = await window.electronAPI.github.checkRateLimit();
        console.log('正在检查GitHub API状态...');
      }

      // 模拟更新过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('更新检查完成');

      // 显示成功消息
      dispatch(showNotification({
        type: 'success',
        title: '更新检查完成',
        message: '所有数据已更新到最新状态',
      }));
    } catch (error) {
      console.error('更新失败:', error);
      dispatch(showNotification({
        type: 'error',
        title: '更新失败',
        message: '更新检查过程中出现错误，请稍后重试',
      }));
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header" style={{ padding: '16px', textAlign: 'center' }}>
        <Title level={4} style={{ color: 'white', margin: 0 }}>
          GitHub Monitor
        </Title>
        <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
          当前视图: {currentView}
        </div>
      </div>
      
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[currentView]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ border: 'none' }}
      />

      <div style={{ padding: '16px' }}>
        <Divider style={{ borderColor: '#303030', margin: '16px 0' }} />
        <Button
          type="primary"
          block
          icon={<SyncOutlined spin={isUpdating} />}
          loading={isUpdating}
          onClick={handleUpdate}
          style={{ marginBottom: '8px' }}
        >
          {isUpdating ? '更新中...' : '一键更新'}
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;
