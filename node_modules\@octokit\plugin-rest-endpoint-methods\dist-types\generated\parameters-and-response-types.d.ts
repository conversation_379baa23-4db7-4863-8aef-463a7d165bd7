import type { Endpoints, RequestParameters } from "@octokit/types";
export type RestEndpointMethodTypes = {
    actions: {
        addCustomLabelsToSelfHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["POST /orgs/{org}/actions/runners/{runner_id}/labels"]["response"];
        };
        addCustomLabelsToSelfHostedRunnerForRepo: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["response"];
        };
        addRepoAccessToSelfHostedRunnerGroupInOrg: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories/{repository_id}"]["response"];
        };
        addSelectedRepoToOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        addSelectedRepoToOrgVariable: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"]["response"];
        };
        approveWorkflowRun: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve"]["response"];
        };
        cancelWorkflowRun: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel"]["response"];
        };
        createEnvironmentVariable: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/environments/{environment_name}/variables"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/environments/{environment_name}/variables"]["response"];
        };
        createHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/actions/hosted-runners"]["parameters"];
            response: Endpoints["POST /orgs/{org}/actions/hosted-runners"]["response"];
        };
        createOrUpdateEnvironmentSecret: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"]["response"];
        };
        createOrUpdateOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/secrets/{secret_name}"]["response"];
        };
        createOrUpdateRepoSecret: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}"]["response"];
        };
        createOrgVariable: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/actions/variables"]["parameters"];
            response: Endpoints["POST /orgs/{org}/actions/variables"]["response"];
        };
        createRegistrationTokenForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/actions/runners/registration-token"]["parameters"];
            response: Endpoints["POST /orgs/{org}/actions/runners/registration-token"]["response"];
        };
        createRegistrationTokenForRepo: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runners/registration-token"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runners/registration-token"]["response"];
        };
        createRemoveTokenForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/actions/runners/remove-token"]["parameters"];
            response: Endpoints["POST /orgs/{org}/actions/runners/remove-token"]["response"];
        };
        createRemoveTokenForRepo: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runners/remove-token"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runners/remove-token"]["response"];
        };
        createRepoVariable: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/variables"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/variables"]["response"];
        };
        createWorkflowDispatch: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches"]["response"];
        };
        deleteActionsCacheById: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}"]["response"];
        };
        deleteActionsCacheByKey: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}"]["response"];
        };
        deleteArtifact: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"]["response"];
        };
        deleteEnvironmentSecret: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"]["response"];
        };
        deleteEnvironmentVariable: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"]["response"];
        };
        deleteHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/hosted-runners/{hosted_runner_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/hosted-runners/{hosted_runner_id}"]["response"];
        };
        deleteOrgSecret: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/secrets/{secret_name}"]["response"];
        };
        deleteOrgVariable: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/variables/{name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/variables/{name}"]["response"];
        };
        deleteRepoSecret: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}"]["response"];
        };
        deleteRepoVariable: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/variables/{name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/variables/{name}"]["response"];
        };
        deleteSelfHostedRunnerFromOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/runners/{runner_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/runners/{runner_id}"]["response"];
        };
        deleteSelfHostedRunnerFromRepo: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}"]["response"];
        };
        deleteWorkflowRun: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}"]["response"];
        };
        deleteWorkflowRunLogs: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs"]["response"];
        };
        disableSelectedRepositoryGithubActionsOrganization: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}"]["response"];
        };
        disableWorkflow: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable"]["response"];
        };
        downloadArtifact: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}"]["response"];
        };
        downloadJobLogsForWorkflowRun: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs"]["response"];
        };
        downloadWorkflowRunAttemptLogs: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs"]["response"];
        };
        downloadWorkflowRunLogs: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs"]["response"];
        };
        enableSelectedRepositoryGithubActionsOrganization: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/permissions/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/permissions/repositories/{repository_id}"]["response"];
        };
        enableWorkflow: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable"]["response"];
        };
        forceCancelWorkflowRun: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/force-cancel"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/force-cancel"]["response"];
        };
        generateRunnerJitconfigForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/actions/runners/generate-jitconfig"]["parameters"];
            response: Endpoints["POST /orgs/{org}/actions/runners/generate-jitconfig"]["response"];
        };
        generateRunnerJitconfigForRepo: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runners/generate-jitconfig"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runners/generate-jitconfig"]["response"];
        };
        getActionsCacheList: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/caches"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/caches"]["response"];
        };
        getActionsCacheUsage: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/cache/usage"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/cache/usage"]["response"];
        };
        getActionsCacheUsageByRepoForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/cache/usage-by-repository"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/cache/usage-by-repository"]["response"];
        };
        getActionsCacheUsageForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/cache/usage"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/cache/usage"]["response"];
        };
        getAllowedActionsOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/permissions/selected-actions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/permissions/selected-actions"]["response"];
        };
        getAllowedActionsRepository: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/permissions/selected-actions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/permissions/selected-actions"]["response"];
        };
        getArtifact: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"]["response"];
        };
        getCustomOidcSubClaimForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/oidc/customization/sub"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/oidc/customization/sub"]["response"];
        };
        getEnvironmentPublicKey: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/public-key"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/public-key"]["response"];
        };
        getEnvironmentSecret: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"]["response"];
        };
        getEnvironmentVariable: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"]["response"];
        };
        getGithubActionsDefaultWorkflowPermissionsOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/permissions/workflow"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/permissions/workflow"]["response"];
        };
        getGithubActionsDefaultWorkflowPermissionsRepository: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/permissions/workflow"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/permissions/workflow"]["response"];
        };
        getGithubActionsPermissionsOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/permissions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/permissions"]["response"];
        };
        getGithubActionsPermissionsRepository: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/permissions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/permissions"]["response"];
        };
        getHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/hosted-runners/{hosted_runner_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/hosted-runners/{hosted_runner_id}"]["response"];
        };
        getHostedRunnersGithubOwnedImagesForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/hosted-runners/images/github-owned"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/hosted-runners/images/github-owned"]["response"];
        };
        getHostedRunnersLimitsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/hosted-runners/limits"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/hosted-runners/limits"]["response"];
        };
        getHostedRunnersMachineSpecsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/hosted-runners/machine-sizes"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/hosted-runners/machine-sizes"]["response"];
        };
        getHostedRunnersPartnerImagesForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/hosted-runners/images/partner"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/hosted-runners/images/partner"]["response"];
        };
        getHostedRunnersPlatformsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/hosted-runners/platforms"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/hosted-runners/platforms"]["response"];
        };
        getJobForWorkflowRun: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/jobs/{job_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/jobs/{job_id}"]["response"];
        };
        getOrgPublicKey: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/secrets/public-key"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/secrets/public-key"]["response"];
        };
        getOrgSecret: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/secrets/{secret_name}"]["response"];
        };
        getOrgVariable: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/variables/{name}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/variables/{name}"]["response"];
        };
        getPendingDeploymentsForRun: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"]["response"];
        };
        getRepoPermissions: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/permissions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/permissions"]["response"];
        };
        getRepoPublicKey: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/secrets/public-key"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/secrets/public-key"]["response"];
        };
        getRepoSecret: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/secrets/{secret_name}"]["response"];
        };
        getRepoVariable: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/variables/{name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/variables/{name}"]["response"];
        };
        getReviewsForRun: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals"]["response"];
        };
        getSelfHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/runners/{runner_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/runners/{runner_id}"]["response"];
        };
        getSelfHostedRunnerForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runners/{runner_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runners/{runner_id}"]["response"];
        };
        getWorkflow: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}"]["response"];
        };
        getWorkflowAccessToRepository: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/permissions/access"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/permissions/access"]["response"];
        };
        getWorkflowRun: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}"]["response"];
        };
        getWorkflowRunAttempt: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}"]["response"];
        };
        getWorkflowRunUsage: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing"]["response"];
        };
        getWorkflowUsage: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing"]["response"];
        };
        listArtifactsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/artifacts"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/artifacts"]["response"];
        };
        listEnvironmentSecrets: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets"]["response"];
        };
        listEnvironmentVariables: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/variables"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/variables"]["response"];
        };
        listGithubHostedRunnersInGroupForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners"]["response"];
        };
        listHostedRunnersForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/hosted-runners"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/hosted-runners"]["response"];
        };
        listJobsForWorkflowRun: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"]["response"];
        };
        listJobsForWorkflowRunAttempt: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"]["response"];
        };
        listLabelsForSelfHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/runners/{runner_id}/labels"]["response"];
        };
        listLabelsForSelfHostedRunnerForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["response"];
        };
        listOrgSecrets: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/secrets"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/secrets"]["response"];
        };
        listOrgVariables: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/variables"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/variables"]["response"];
        };
        listRepoOrganizationSecrets: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/organization-secrets"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/organization-secrets"]["response"];
        };
        listRepoOrganizationVariables: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/organization-variables"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/organization-variables"]["response"];
        };
        listRepoSecrets: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/secrets"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/secrets"]["response"];
        };
        listRepoVariables: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/variables"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/variables"]["response"];
        };
        listRepoWorkflows: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/workflows"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/workflows"]["response"];
        };
        listRunnerApplicationsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/runners/downloads"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/runners/downloads"]["response"];
        };
        listRunnerApplicationsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runners/downloads"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runners/downloads"]["response"];
        };
        listSelectedReposForOrgSecret: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"]["response"];
        };
        listSelectedReposForOrgVariable: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/variables/{name}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/variables/{name}/repositories"]["response"];
        };
        listSelectedRepositoriesEnabledGithubActionsOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/permissions/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/permissions/repositories"]["response"];
        };
        listSelfHostedRunnersForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/runners"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/runners"]["response"];
        };
        listSelfHostedRunnersForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runners"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runners"]["response"];
        };
        listWorkflowRunArtifacts: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"]["response"];
        };
        listWorkflowRuns: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"]["response"];
        };
        listWorkflowRunsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/actions/runs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/actions/runs"]["response"];
        };
        reRunJobForWorkflowRun: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun"]["response"];
        };
        reRunWorkflow: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun"]["response"];
        };
        reRunWorkflowFailedJobs: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs"]["response"];
        };
        removeAllCustomLabelsFromSelfHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/runners/{runner_id}/labels"]["response"];
        };
        removeAllCustomLabelsFromSelfHostedRunnerForRepo: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["response"];
        };
        removeCustomLabelFromSelfHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}"]["response"];
        };
        removeCustomLabelFromSelfHostedRunnerForRepo: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}"]["response"];
        };
        removeSelectedRepoFromOrgSecret: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        removeSelectedRepoFromOrgVariable: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"]["response"];
        };
        reviewCustomGatesForRun: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/deployment_protection_rule"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/deployment_protection_rule"]["response"];
        };
        reviewPendingDeploymentsForRun: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"]["response"];
        };
        setAllowedActionsOrganization: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/permissions/selected-actions"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/permissions/selected-actions"]["response"];
        };
        setAllowedActionsRepository: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/permissions/selected-actions"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/permissions/selected-actions"]["response"];
        };
        setCustomLabelsForSelfHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/runners/{runner_id}/labels"]["response"];
        };
        setCustomLabelsForSelfHostedRunnerForRepo: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"]["response"];
        };
        setCustomOidcSubClaimForRepo: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/oidc/customization/sub"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/oidc/customization/sub"]["response"];
        };
        setGithubActionsDefaultWorkflowPermissionsOrganization: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/permissions/workflow"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/permissions/workflow"]["response"];
        };
        setGithubActionsDefaultWorkflowPermissionsRepository: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/permissions/workflow"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/permissions/workflow"]["response"];
        };
        setGithubActionsPermissionsOrganization: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/permissions"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/permissions"]["response"];
        };
        setGithubActionsPermissionsRepository: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/permissions"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/permissions"]["response"];
        };
        setSelectedReposForOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories"]["response"];
        };
        setSelectedReposForOrgVariable: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/variables/{name}/repositories"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/variables/{name}/repositories"]["response"];
        };
        setSelectedRepositoriesEnabledGithubActionsOrganization: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/permissions/repositories"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/permissions/repositories"]["response"];
        };
        setWorkflowAccessToRepository: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/actions/permissions/access"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/actions/permissions/access"]["response"];
        };
        updateEnvironmentVariable: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"]["response"];
        };
        updateHostedRunnerForOrg: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/actions/hosted-runners/{hosted_runner_id}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/actions/hosted-runners/{hosted_runner_id}"]["response"];
        };
        updateOrgVariable: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/actions/variables/{name}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/actions/variables/{name}"]["response"];
        };
        updateRepoVariable: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/actions/variables/{name}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/actions/variables/{name}"]["response"];
        };
    };
    activity: {
        checkRepoIsStarredByAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/starred/{owner}/{repo}"]["parameters"];
            response: Endpoints["GET /user/starred/{owner}/{repo}"]["response"];
        };
        deleteRepoSubscription: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/subscription"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/subscription"]["response"];
        };
        deleteThreadSubscription: {
            parameters: RequestParameters & Endpoints["DELETE /notifications/threads/{thread_id}/subscription"]["parameters"];
            response: Endpoints["DELETE /notifications/threads/{thread_id}/subscription"]["response"];
        };
        getFeeds: {
            parameters: RequestParameters & Endpoints["GET /feeds"]["parameters"];
            response: Endpoints["GET /feeds"]["response"];
        };
        getRepoSubscription: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/subscription"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/subscription"]["response"];
        };
        getThread: {
            parameters: RequestParameters & Endpoints["GET /notifications/threads/{thread_id}"]["parameters"];
            response: Endpoints["GET /notifications/threads/{thread_id}"]["response"];
        };
        getThreadSubscriptionForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /notifications/threads/{thread_id}/subscription"]["parameters"];
            response: Endpoints["GET /notifications/threads/{thread_id}/subscription"]["response"];
        };
        listEventsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/events"]["parameters"];
            response: Endpoints["GET /users/{username}/events"]["response"];
        };
        listNotificationsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /notifications"]["parameters"];
            response: Endpoints["GET /notifications"]["response"];
        };
        listOrgEventsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/events/orgs/{org}"]["parameters"];
            response: Endpoints["GET /users/{username}/events/orgs/{org}"]["response"];
        };
        listPublicEvents: {
            parameters: RequestParameters & Endpoints["GET /events"]["parameters"];
            response: Endpoints["GET /events"]["response"];
        };
        listPublicEventsForRepoNetwork: {
            parameters: RequestParameters & Endpoints["GET /networks/{owner}/{repo}/events"]["parameters"];
            response: Endpoints["GET /networks/{owner}/{repo}/events"]["response"];
        };
        listPublicEventsForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/events/public"]["parameters"];
            response: Endpoints["GET /users/{username}/events/public"]["response"];
        };
        listPublicOrgEvents: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/events"]["parameters"];
            response: Endpoints["GET /orgs/{org}/events"]["response"];
        };
        listReceivedEventsForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/received_events"]["parameters"];
            response: Endpoints["GET /users/{username}/received_events"]["response"];
        };
        listReceivedPublicEventsForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/received_events/public"]["parameters"];
            response: Endpoints["GET /users/{username}/received_events/public"]["response"];
        };
        listRepoEvents: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/events"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/events"]["response"];
        };
        listRepoNotificationsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/notifications"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/notifications"]["response"];
        };
        listReposStarredByAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/starred"]["parameters"];
            response: Endpoints["GET /user/starred"]["response"];
        };
        listReposStarredByUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/starred"]["parameters"];
            response: Endpoints["GET /users/{username}/starred"]["response"];
        };
        listReposWatchedByUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/subscriptions"]["parameters"];
            response: Endpoints["GET /users/{username}/subscriptions"]["response"];
        };
        listStargazersForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/stargazers"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/stargazers"]["response"];
        };
        listWatchedReposForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/subscriptions"]["parameters"];
            response: Endpoints["GET /user/subscriptions"]["response"];
        };
        listWatchersForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/subscribers"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/subscribers"]["response"];
        };
        markNotificationsAsRead: {
            parameters: RequestParameters & Endpoints["PUT /notifications"]["parameters"];
            response: Endpoints["PUT /notifications"]["response"];
        };
        markRepoNotificationsAsRead: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/notifications"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/notifications"]["response"];
        };
        markThreadAsDone: {
            parameters: RequestParameters & Endpoints["DELETE /notifications/threads/{thread_id}"]["parameters"];
            response: Endpoints["DELETE /notifications/threads/{thread_id}"]["response"];
        };
        markThreadAsRead: {
            parameters: RequestParameters & Endpoints["PATCH /notifications/threads/{thread_id}"]["parameters"];
            response: Endpoints["PATCH /notifications/threads/{thread_id}"]["response"];
        };
        setRepoSubscription: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/subscription"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/subscription"]["response"];
        };
        setThreadSubscription: {
            parameters: RequestParameters & Endpoints["PUT /notifications/threads/{thread_id}/subscription"]["parameters"];
            response: Endpoints["PUT /notifications/threads/{thread_id}/subscription"]["response"];
        };
        starRepoForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PUT /user/starred/{owner}/{repo}"]["parameters"];
            response: Endpoints["PUT /user/starred/{owner}/{repo}"]["response"];
        };
        unstarRepoForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/starred/{owner}/{repo}"]["parameters"];
            response: Endpoints["DELETE /user/starred/{owner}/{repo}"]["response"];
        };
    };
    apps: {
        addRepoToInstallation: {
            parameters: RequestParameters & Endpoints["PUT /user/installations/{installation_id}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /user/installations/{installation_id}/repositories/{repository_id}"]["response"];
        };
        addRepoToInstallationForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PUT /user/installations/{installation_id}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /user/installations/{installation_id}/repositories/{repository_id}"]["response"];
        };
        checkToken: {
            parameters: RequestParameters & Endpoints["POST /applications/{client_id}/token"]["parameters"];
            response: Endpoints["POST /applications/{client_id}/token"]["response"];
        };
        createFromManifest: {
            parameters: RequestParameters & Endpoints["POST /app-manifests/{code}/conversions"]["parameters"];
            response: Endpoints["POST /app-manifests/{code}/conversions"]["response"];
        };
        createInstallationAccessToken: {
            parameters: RequestParameters & Endpoints["POST /app/installations/{installation_id}/access_tokens"]["parameters"];
            response: Endpoints["POST /app/installations/{installation_id}/access_tokens"]["response"];
        };
        deleteAuthorization: {
            parameters: RequestParameters & Endpoints["DELETE /applications/{client_id}/grant"]["parameters"];
            response: Endpoints["DELETE /applications/{client_id}/grant"]["response"];
        };
        deleteInstallation: {
            parameters: RequestParameters & Endpoints["DELETE /app/installations/{installation_id}"]["parameters"];
            response: Endpoints["DELETE /app/installations/{installation_id}"]["response"];
        };
        deleteToken: {
            parameters: RequestParameters & Endpoints["DELETE /applications/{client_id}/token"]["parameters"];
            response: Endpoints["DELETE /applications/{client_id}/token"]["response"];
        };
        getAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /app"]["parameters"];
            response: Endpoints["GET /app"]["response"];
        };
        getBySlug: {
            parameters: RequestParameters & Endpoints["GET /apps/{app_slug}"]["parameters"];
            response: Endpoints["GET /apps/{app_slug}"]["response"];
        };
        getInstallation: {
            parameters: RequestParameters & Endpoints["GET /app/installations/{installation_id}"]["parameters"];
            response: Endpoints["GET /app/installations/{installation_id}"]["response"];
        };
        getOrgInstallation: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/installation"]["parameters"];
            response: Endpoints["GET /orgs/{org}/installation"]["response"];
        };
        getRepoInstallation: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/installation"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/installation"]["response"];
        };
        getSubscriptionPlanForAccount: {
            parameters: RequestParameters & Endpoints["GET /marketplace_listing/accounts/{account_id}"]["parameters"];
            response: Endpoints["GET /marketplace_listing/accounts/{account_id}"]["response"];
        };
        getSubscriptionPlanForAccountStubbed: {
            parameters: RequestParameters & Endpoints["GET /marketplace_listing/stubbed/accounts/{account_id}"]["parameters"];
            response: Endpoints["GET /marketplace_listing/stubbed/accounts/{account_id}"]["response"];
        };
        getUserInstallation: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/installation"]["parameters"];
            response: Endpoints["GET /users/{username}/installation"]["response"];
        };
        getWebhookConfigForApp: {
            parameters: RequestParameters & Endpoints["GET /app/hook/config"]["parameters"];
            response: Endpoints["GET /app/hook/config"]["response"];
        };
        getWebhookDelivery: {
            parameters: RequestParameters & Endpoints["GET /app/hook/deliveries/{delivery_id}"]["parameters"];
            response: Endpoints["GET /app/hook/deliveries/{delivery_id}"]["response"];
        };
        listAccountsForPlan: {
            parameters: RequestParameters & Endpoints["GET /marketplace_listing/plans/{plan_id}/accounts"]["parameters"];
            response: Endpoints["GET /marketplace_listing/plans/{plan_id}/accounts"]["response"];
        };
        listAccountsForPlanStubbed: {
            parameters: RequestParameters & Endpoints["GET /marketplace_listing/stubbed/plans/{plan_id}/accounts"]["parameters"];
            response: Endpoints["GET /marketplace_listing/stubbed/plans/{plan_id}/accounts"]["response"];
        };
        listInstallationReposForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/installations/{installation_id}/repositories"]["parameters"];
            response: Endpoints["GET /user/installations/{installation_id}/repositories"]["response"];
        };
        listInstallationRequestsForAuthenticatedApp: {
            parameters: RequestParameters & Endpoints["GET /app/installation-requests"]["parameters"];
            response: Endpoints["GET /app/installation-requests"]["response"];
        };
        listInstallations: {
            parameters: RequestParameters & Endpoints["GET /app/installations"]["parameters"];
            response: Endpoints["GET /app/installations"]["response"];
        };
        listInstallationsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/installations"]["parameters"];
            response: Endpoints["GET /user/installations"]["response"];
        };
        listPlans: {
            parameters: RequestParameters & Endpoints["GET /marketplace_listing/plans"]["parameters"];
            response: Endpoints["GET /marketplace_listing/plans"]["response"];
        };
        listPlansStubbed: {
            parameters: RequestParameters & Endpoints["GET /marketplace_listing/stubbed/plans"]["parameters"];
            response: Endpoints["GET /marketplace_listing/stubbed/plans"]["response"];
        };
        listReposAccessibleToInstallation: {
            parameters: RequestParameters & Endpoints["GET /installation/repositories"]["parameters"];
            response: Endpoints["GET /installation/repositories"]["response"];
        };
        listSubscriptionsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/marketplace_purchases"]["parameters"];
            response: Endpoints["GET /user/marketplace_purchases"]["response"];
        };
        listSubscriptionsForAuthenticatedUserStubbed: {
            parameters: RequestParameters & Endpoints["GET /user/marketplace_purchases/stubbed"]["parameters"];
            response: Endpoints["GET /user/marketplace_purchases/stubbed"]["response"];
        };
        listWebhookDeliveries: {
            parameters: RequestParameters & Endpoints["GET /app/hook/deliveries"]["parameters"];
            response: Endpoints["GET /app/hook/deliveries"]["response"];
        };
        redeliverWebhookDelivery: {
            parameters: RequestParameters & Endpoints["POST /app/hook/deliveries/{delivery_id}/attempts"]["parameters"];
            response: Endpoints["POST /app/hook/deliveries/{delivery_id}/attempts"]["response"];
        };
        removeRepoFromInstallation: {
            parameters: RequestParameters & Endpoints["DELETE /user/installations/{installation_id}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /user/installations/{installation_id}/repositories/{repository_id}"]["response"];
        };
        removeRepoFromInstallationForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/installations/{installation_id}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /user/installations/{installation_id}/repositories/{repository_id}"]["response"];
        };
        resetToken: {
            parameters: RequestParameters & Endpoints["PATCH /applications/{client_id}/token"]["parameters"];
            response: Endpoints["PATCH /applications/{client_id}/token"]["response"];
        };
        revokeInstallationAccessToken: {
            parameters: RequestParameters & Endpoints["DELETE /installation/token"]["parameters"];
            response: Endpoints["DELETE /installation/token"]["response"];
        };
        scopeToken: {
            parameters: RequestParameters & Endpoints["POST /applications/{client_id}/token/scoped"]["parameters"];
            response: Endpoints["POST /applications/{client_id}/token/scoped"]["response"];
        };
        suspendInstallation: {
            parameters: RequestParameters & Endpoints["PUT /app/installations/{installation_id}/suspended"]["parameters"];
            response: Endpoints["PUT /app/installations/{installation_id}/suspended"]["response"];
        };
        unsuspendInstallation: {
            parameters: RequestParameters & Endpoints["DELETE /app/installations/{installation_id}/suspended"]["parameters"];
            response: Endpoints["DELETE /app/installations/{installation_id}/suspended"]["response"];
        };
        updateWebhookConfigForApp: {
            parameters: RequestParameters & Endpoints["PATCH /app/hook/config"]["parameters"];
            response: Endpoints["PATCH /app/hook/config"]["response"];
        };
    };
    billing: {
        getGithubActionsBillingOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/settings/billing/actions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/settings/billing/actions"]["response"];
        };
        getGithubActionsBillingUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/settings/billing/actions"]["parameters"];
            response: Endpoints["GET /users/{username}/settings/billing/actions"]["response"];
        };
        getGithubBillingUsageReportOrg: {
            parameters: RequestParameters & Endpoints["GET /organizations/{org}/settings/billing/usage"]["parameters"];
            response: Endpoints["GET /organizations/{org}/settings/billing/usage"]["response"];
        };
        getGithubBillingUsageReportUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/settings/billing/usage"]["parameters"];
            response: Endpoints["GET /users/{username}/settings/billing/usage"]["response"];
        };
        getGithubPackagesBillingOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/settings/billing/packages"]["parameters"];
            response: Endpoints["GET /orgs/{org}/settings/billing/packages"]["response"];
        };
        getGithubPackagesBillingUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/settings/billing/packages"]["parameters"];
            response: Endpoints["GET /users/{username}/settings/billing/packages"]["response"];
        };
        getSharedStorageBillingOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/settings/billing/shared-storage"]["parameters"];
            response: Endpoints["GET /orgs/{org}/settings/billing/shared-storage"]["response"];
        };
        getSharedStorageBillingUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/settings/billing/shared-storage"]["parameters"];
            response: Endpoints["GET /users/{username}/settings/billing/shared-storage"]["response"];
        };
    };
    campaigns: {
        createCampaign: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/campaigns"]["parameters"];
            response: Endpoints["POST /orgs/{org}/campaigns"]["response"];
        };
        deleteCampaign: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/campaigns/{campaign_number}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/campaigns/{campaign_number}"]["response"];
        };
        getCampaignSummary: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/campaigns/{campaign_number}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/campaigns/{campaign_number}"]["response"];
        };
        listOrgCampaigns: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/campaigns"]["parameters"];
            response: Endpoints["GET /orgs/{org}/campaigns"]["response"];
        };
        updateCampaign: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/campaigns/{campaign_number}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/campaigns/{campaign_number}"]["response"];
        };
    };
    checks: {
        create: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/check-runs"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/check-runs"]["response"];
        };
        createSuite: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/check-suites"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/check-suites"]["response"];
        };
        get: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/check-runs/{check_run_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/check-runs/{check_run_id}"]["response"];
        };
        getSuite: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}"]["response"];
        };
        listAnnotations: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations"]["response"];
        };
        listForRef: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"]["response"];
        };
        listForSuite: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"]["response"];
        };
        listSuitesForRef: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"]["response"];
        };
        rerequestRun: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest"]["response"];
        };
        rerequestSuite: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest"]["response"];
        };
        setSuitesPreferences: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/check-suites/preferences"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/check-suites/preferences"]["response"];
        };
        update: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}"]["response"];
        };
    };
    codeScanning: {
        commitAutofix: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix/commits"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix/commits"]["response"];
        };
        createAutofix: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"]["response"];
        };
        createVariantAnalysis: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses"]["response"];
        };
        deleteAnalysis: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}"]["response"];
        };
        deleteCodeqlDatabase: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"]["response"];
        };
        getAlert: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}"]["response"];
        };
        getAnalysis: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}"]["response"];
        };
        getAutofix: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"]["response"];
        };
        getCodeqlDatabase: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"]["response"];
        };
        getDefaultSetup: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/default-setup"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/default-setup"]["response"];
        };
        getSarif: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}"]["response"];
        };
        getVariantAnalysis: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}"]["response"];
        };
        getVariantAnalysisRepoTask: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}/repos/{repo_owner}/{repo_name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}/repos/{repo_owner}/{repo_name}"]["response"];
        };
        listAlertInstances: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"]["response"];
        };
        listAlertsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/code-scanning/alerts"]["parameters"];
            response: Endpoints["GET /orgs/{org}/code-scanning/alerts"]["response"];
        };
        listAlertsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts"]["response"];
        };
        listAlertsInstances: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"]["response"];
        };
        listCodeqlDatabases: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/databases"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/codeql/databases"]["response"];
        };
        listRecentAnalyses: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-scanning/analyses"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/analyses"]["response"];
        };
        updateAlert: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}"]["response"];
        };
        updateDefaultSetup: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/code-scanning/default-setup"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/code-scanning/default-setup"]["response"];
        };
        uploadSarif: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/code-scanning/sarifs"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/code-scanning/sarifs"]["response"];
        };
    };
    codeSecurity: {
        attachConfiguration: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/code-security/configurations/{configuration_id}/attach"]["parameters"];
            response: Endpoints["POST /orgs/{org}/code-security/configurations/{configuration_id}/attach"]["response"];
        };
        attachEnterpriseConfiguration: {
            parameters: RequestParameters & Endpoints["POST /enterprises/{enterprise}/code-security/configurations/{configuration_id}/attach"]["parameters"];
            response: Endpoints["POST /enterprises/{enterprise}/code-security/configurations/{configuration_id}/attach"]["response"];
        };
        createConfiguration: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/code-security/configurations"]["parameters"];
            response: Endpoints["POST /orgs/{org}/code-security/configurations"]["response"];
        };
        createConfigurationForEnterprise: {
            parameters: RequestParameters & Endpoints["POST /enterprises/{enterprise}/code-security/configurations"]["parameters"];
            response: Endpoints["POST /enterprises/{enterprise}/code-security/configurations"]["response"];
        };
        deleteConfiguration: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/code-security/configurations/{configuration_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/code-security/configurations/{configuration_id}"]["response"];
        };
        deleteConfigurationForEnterprise: {
            parameters: RequestParameters & Endpoints["DELETE /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]["parameters"];
            response: Endpoints["DELETE /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]["response"];
        };
        detachConfiguration: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/code-security/configurations/detach"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/code-security/configurations/detach"]["response"];
        };
        getConfiguration: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/code-security/configurations/{configuration_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/code-security/configurations/{configuration_id}"]["response"];
        };
        getConfigurationForRepository: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/code-security-configuration"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/code-security-configuration"]["response"];
        };
        getConfigurationsForEnterprise: {
            parameters: RequestParameters & Endpoints["GET /enterprises/{enterprise}/code-security/configurations"]["parameters"];
            response: Endpoints["GET /enterprises/{enterprise}/code-security/configurations"]["response"];
        };
        getConfigurationsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/code-security/configurations"]["parameters"];
            response: Endpoints["GET /orgs/{org}/code-security/configurations"]["response"];
        };
        getDefaultConfigurations: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/code-security/configurations/defaults"]["parameters"];
            response: Endpoints["GET /orgs/{org}/code-security/configurations/defaults"]["response"];
        };
        getDefaultConfigurationsForEnterprise: {
            parameters: RequestParameters & Endpoints["GET /enterprises/{enterprise}/code-security/configurations/defaults"]["parameters"];
            response: Endpoints["GET /enterprises/{enterprise}/code-security/configurations/defaults"]["response"];
        };
        getRepositoriesForConfiguration: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories"]["response"];
        };
        getRepositoriesForEnterpriseConfiguration: {
            parameters: RequestParameters & Endpoints["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories"]["parameters"];
            response: Endpoints["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories"]["response"];
        };
        getSingleConfigurationForEnterprise: {
            parameters: RequestParameters & Endpoints["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]["parameters"];
            response: Endpoints["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]["response"];
        };
        setConfigurationAsDefault: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/code-security/configurations/{configuration_id}/defaults"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/code-security/configurations/{configuration_id}/defaults"]["response"];
        };
        setConfigurationAsDefaultForEnterprise: {
            parameters: RequestParameters & Endpoints["PUT /enterprises/{enterprise}/code-security/configurations/{configuration_id}/defaults"]["parameters"];
            response: Endpoints["PUT /enterprises/{enterprise}/code-security/configurations/{configuration_id}/defaults"]["response"];
        };
        updateConfiguration: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/code-security/configurations/{configuration_id}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/code-security/configurations/{configuration_id}"]["response"];
        };
        updateEnterpriseConfiguration: {
            parameters: RequestParameters & Endpoints["PATCH /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]["parameters"];
            response: Endpoints["PATCH /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]["response"];
        };
    };
    codesOfConduct: {
        getAllCodesOfConduct: {
            parameters: RequestParameters & Endpoints["GET /codes_of_conduct"]["parameters"];
            response: Endpoints["GET /codes_of_conduct"]["response"];
        };
        getConductCode: {
            parameters: RequestParameters & Endpoints["GET /codes_of_conduct/{key}"]["parameters"];
            response: Endpoints["GET /codes_of_conduct/{key}"]["response"];
        };
    };
    codespaces: {
        addRepositoryForSecretForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        addSelectedRepoToOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        checkPermissionsForDevcontainer: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces/permissions_check"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces/permissions_check"]["response"];
        };
        codespaceMachinesForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces/{codespace_name}/machines"]["parameters"];
            response: Endpoints["GET /user/codespaces/{codespace_name}/machines"]["response"];
        };
        createForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/codespaces"]["parameters"];
            response: Endpoints["POST /user/codespaces"]["response"];
        };
        createOrUpdateOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/codespaces/secrets/{secret_name}"]["response"];
        };
        createOrUpdateRepoSecret: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"]["response"];
        };
        createOrUpdateSecretForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PUT /user/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /user/codespaces/secrets/{secret_name}"]["response"];
        };
        createWithPrForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces"]["response"];
        };
        createWithRepoForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/codespaces"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/codespaces"]["response"];
        };
        deleteForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/codespaces/{codespace_name}"]["parameters"];
            response: Endpoints["DELETE /user/codespaces/{codespace_name}"]["response"];
        };
        deleteFromOrganization: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}"]["response"];
        };
        deleteOrgSecret: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/codespaces/secrets/{secret_name}"]["response"];
        };
        deleteRepoSecret: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"]["response"];
        };
        deleteSecretForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /user/codespaces/secrets/{secret_name}"]["response"];
        };
        exportForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/codespaces/{codespace_name}/exports"]["parameters"];
            response: Endpoints["POST /user/codespaces/{codespace_name}/exports"]["response"];
        };
        getCodespacesForUserInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/members/{username}/codespaces"]["parameters"];
            response: Endpoints["GET /orgs/{org}/members/{username}/codespaces"]["response"];
        };
        getExportDetailsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces/{codespace_name}/exports/{export_id}"]["parameters"];
            response: Endpoints["GET /user/codespaces/{codespace_name}/exports/{export_id}"]["response"];
        };
        getForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces/{codespace_name}"]["parameters"];
            response: Endpoints["GET /user/codespaces/{codespace_name}"]["response"];
        };
        getOrgPublicKey: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/codespaces/secrets/public-key"]["parameters"];
            response: Endpoints["GET /orgs/{org}/codespaces/secrets/public-key"]["response"];
        };
        getOrgSecret: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/codespaces/secrets/{secret_name}"]["response"];
        };
        getPublicKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces/secrets/public-key"]["parameters"];
            response: Endpoints["GET /user/codespaces/secrets/public-key"]["response"];
        };
        getRepoPublicKey: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets/public-key"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets/public-key"]["response"];
        };
        getRepoSecret: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"]["response"];
        };
        getSecretForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /user/codespaces/secrets/{secret_name}"]["response"];
        };
        listDevcontainersInRepositoryForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces/devcontainers"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces/devcontainers"]["response"];
        };
        listForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces"]["parameters"];
            response: Endpoints["GET /user/codespaces"]["response"];
        };
        listInOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/codespaces"]["parameters"];
            response: Endpoints["GET /orgs/{org}/codespaces"]["response"];
        };
        listInRepositoryForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces"]["response"];
        };
        listOrgSecrets: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/codespaces/secrets"]["parameters"];
            response: Endpoints["GET /orgs/{org}/codespaces/secrets"]["response"];
        };
        listRepoSecrets: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets"]["response"];
        };
        listRepositoriesForSecretForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["GET /user/codespaces/secrets/{secret_name}/repositories"]["response"];
        };
        listSecretsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/codespaces/secrets"]["parameters"];
            response: Endpoints["GET /user/codespaces/secrets"]["response"];
        };
        listSelectedReposForOrgSecret: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories"]["response"];
        };
        preFlightWithRepoForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces/new"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces/new"]["response"];
        };
        publishForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/codespaces/{codespace_name}/publish"]["parameters"];
            response: Endpoints["POST /user/codespaces/{codespace_name}/publish"]["response"];
        };
        removeRepositoryForSecretForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        removeSelectedRepoFromOrgSecret: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        repoMachinesForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codespaces/machines"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codespaces/machines"]["response"];
        };
        setRepositoriesForSecretForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PUT /user/codespaces/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["PUT /user/codespaces/secrets/{secret_name}/repositories"]["response"];
        };
        setSelectedReposForOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories"]["response"];
        };
        startForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/codespaces/{codespace_name}/start"]["parameters"];
            response: Endpoints["POST /user/codespaces/{codespace_name}/start"]["response"];
        };
        stopForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/codespaces/{codespace_name}/stop"]["parameters"];
            response: Endpoints["POST /user/codespaces/{codespace_name}/stop"]["response"];
        };
        stopInOrganization: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop"]["parameters"];
            response: Endpoints["POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop"]["response"];
        };
        updateForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PATCH /user/codespaces/{codespace_name}"]["parameters"];
            response: Endpoints["PATCH /user/codespaces/{codespace_name}"]["response"];
        };
    };
    copilot: {
        addCopilotSeatsForTeams: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/copilot/billing/selected_teams"]["parameters"];
            response: Endpoints["POST /orgs/{org}/copilot/billing/selected_teams"]["response"];
        };
        addCopilotSeatsForUsers: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/copilot/billing/selected_users"]["parameters"];
            response: Endpoints["POST /orgs/{org}/copilot/billing/selected_users"]["response"];
        };
        cancelCopilotSeatAssignmentForTeams: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/copilot/billing/selected_teams"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/copilot/billing/selected_teams"]["response"];
        };
        cancelCopilotSeatAssignmentForUsers: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/copilot/billing/selected_users"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/copilot/billing/selected_users"]["response"];
        };
        copilotMetricsForOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/copilot/metrics"]["parameters"];
            response: Endpoints["GET /orgs/{org}/copilot/metrics"]["response"];
        };
        copilotMetricsForTeam: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/team/{team_slug}/copilot/metrics"]["parameters"];
            response: Endpoints["GET /orgs/{org}/team/{team_slug}/copilot/metrics"]["response"];
        };
        getCopilotOrganizationDetails: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/copilot/billing"]["parameters"];
            response: Endpoints["GET /orgs/{org}/copilot/billing"]["response"];
        };
        getCopilotSeatDetailsForUser: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/members/{username}/copilot"]["parameters"];
            response: Endpoints["GET /orgs/{org}/members/{username}/copilot"]["response"];
        };
        listCopilotSeats: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/copilot/billing/seats"]["parameters"];
            response: Endpoints["GET /orgs/{org}/copilot/billing/seats"]["response"];
        };
    };
    credentials: {
        revoke: {
            parameters: RequestParameters & Endpoints["POST /credentials/revoke"]["parameters"];
            response: Endpoints["POST /credentials/revoke"]["response"];
        };
    };
    dependabot: {
        addSelectedRepoToOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        createOrUpdateOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/dependabot/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/dependabot/secrets/{secret_name}"]["response"];
        };
        createOrUpdateRepoSecret: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"]["response"];
        };
        deleteOrgSecret: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/dependabot/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/dependabot/secrets/{secret_name}"]["response"];
        };
        deleteRepoSecret: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"]["response"];
        };
        getAlert: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"]["response"];
        };
        getOrgPublicKey: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/dependabot/secrets/public-key"]["parameters"];
            response: Endpoints["GET /orgs/{org}/dependabot/secrets/public-key"]["response"];
        };
        getOrgSecret: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/dependabot/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/dependabot/secrets/{secret_name}"]["response"];
        };
        getRepoPublicKey: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets/public-key"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets/public-key"]["response"];
        };
        getRepoSecret: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"]["response"];
        };
        listAlertsForEnterprise: {
            parameters: RequestParameters & Endpoints["GET /enterprises/{enterprise}/dependabot/alerts"]["parameters"];
            response: Endpoints["GET /enterprises/{enterprise}/dependabot/alerts"]["response"];
        };
        listAlertsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/dependabot/alerts"]["parameters"];
            response: Endpoints["GET /orgs/{org}/dependabot/alerts"]["response"];
        };
        listAlertsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/dependabot/alerts"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/dependabot/alerts"]["response"];
        };
        listOrgSecrets: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/dependabot/secrets"]["parameters"];
            response: Endpoints["GET /orgs/{org}/dependabot/secrets"]["response"];
        };
        listRepoSecrets: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets"]["response"];
        };
        listSelectedReposForOrgSecret: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"]["response"];
        };
        removeSelectedRepoFromOrgSecret: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"]["response"];
        };
        setSelectedReposForOrgSecret: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories"]["response"];
        };
        updateAlert: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"]["response"];
        };
    };
    dependencyGraph: {
        createRepositorySnapshot: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/dependency-graph/snapshots"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/dependency-graph/snapshots"]["response"];
        };
        diffRange: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}"]["response"];
        };
        exportSbom: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/dependency-graph/sbom"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/dependency-graph/sbom"]["response"];
        };
    };
    emojis: {
        get: {
            parameters: RequestParameters & Endpoints["GET /emojis"]["parameters"];
            response: Endpoints["GET /emojis"]["response"];
        };
    };
    gists: {
        checkIsStarred: {
            parameters: RequestParameters & Endpoints["GET /gists/{gist_id}/star"]["parameters"];
            response: Endpoints["GET /gists/{gist_id}/star"]["response"];
        };
        create: {
            parameters: RequestParameters & Endpoints["POST /gists"]["parameters"];
            response: Endpoints["POST /gists"]["response"];
        };
        createComment: {
            parameters: RequestParameters & Endpoints["POST /gists/{gist_id}/comments"]["parameters"];
            response: Endpoints["POST /gists/{gist_id}/comments"]["response"];
        };
        delete: {
            parameters: RequestParameters & Endpoints["DELETE /gists/{gist_id}"]["parameters"];
            response: Endpoints["DELETE /gists/{gist_id}"]["response"];
        };
        deleteComment: {
            parameters: RequestParameters & Endpoints["DELETE /gists/{gist_id}/comments/{comment_id}"]["parameters"];
            response: Endpoints["DELETE /gists/{gist_id}/comments/{comment_id}"]["response"];
        };
        fork: {
            parameters: RequestParameters & Endpoints["POST /gists/{gist_id}/forks"]["parameters"];
            response: Endpoints["POST /gists/{gist_id}/forks"]["response"];
        };
        get: {
            parameters: RequestParameters & Endpoints["GET /gists/{gist_id}"]["parameters"];
            response: Endpoints["GET /gists/{gist_id}"]["response"];
        };
        getComment: {
            parameters: RequestParameters & Endpoints["GET /gists/{gist_id}/comments/{comment_id}"]["parameters"];
            response: Endpoints["GET /gists/{gist_id}/comments/{comment_id}"]["response"];
        };
        getRevision: {
            parameters: RequestParameters & Endpoints["GET /gists/{gist_id}/{sha}"]["parameters"];
            response: Endpoints["GET /gists/{gist_id}/{sha}"]["response"];
        };
        list: {
            parameters: RequestParameters & Endpoints["GET /gists"]["parameters"];
            response: Endpoints["GET /gists"]["response"];
        };
        listComments: {
            parameters: RequestParameters & Endpoints["GET /gists/{gist_id}/comments"]["parameters"];
            response: Endpoints["GET /gists/{gist_id}/comments"]["response"];
        };
        listCommits: {
            parameters: RequestParameters & Endpoints["GET /gists/{gist_id}/commits"]["parameters"];
            response: Endpoints["GET /gists/{gist_id}/commits"]["response"];
        };
        listForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/gists"]["parameters"];
            response: Endpoints["GET /users/{username}/gists"]["response"];
        };
        listForks: {
            parameters: RequestParameters & Endpoints["GET /gists/{gist_id}/forks"]["parameters"];
            response: Endpoints["GET /gists/{gist_id}/forks"]["response"];
        };
        listPublic: {
            parameters: RequestParameters & Endpoints["GET /gists/public"]["parameters"];
            response: Endpoints["GET /gists/public"]["response"];
        };
        listStarred: {
            parameters: RequestParameters & Endpoints["GET /gists/starred"]["parameters"];
            response: Endpoints["GET /gists/starred"]["response"];
        };
        star: {
            parameters: RequestParameters & Endpoints["PUT /gists/{gist_id}/star"]["parameters"];
            response: Endpoints["PUT /gists/{gist_id}/star"]["response"];
        };
        unstar: {
            parameters: RequestParameters & Endpoints["DELETE /gists/{gist_id}/star"]["parameters"];
            response: Endpoints["DELETE /gists/{gist_id}/star"]["response"];
        };
        update: {
            parameters: RequestParameters & Endpoints["PATCH /gists/{gist_id}"]["parameters"];
            response: Endpoints["PATCH /gists/{gist_id}"]["response"];
        };
        updateComment: {
            parameters: RequestParameters & Endpoints["PATCH /gists/{gist_id}/comments/{comment_id}"]["parameters"];
            response: Endpoints["PATCH /gists/{gist_id}/comments/{comment_id}"]["response"];
        };
    };
    git: {
        createBlob: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/git/blobs"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/git/blobs"]["response"];
        };
        createCommit: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/git/commits"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/git/commits"]["response"];
        };
        createRef: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/git/refs"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/git/refs"]["response"];
        };
        createTag: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/git/tags"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/git/tags"]["response"];
        };
        createTree: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/git/trees"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/git/trees"]["response"];
        };
        deleteRef: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/git/refs/{ref}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/git/refs/{ref}"]["response"];
        };
        getBlob: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/git/blobs/{file_sha}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/git/blobs/{file_sha}"]["response"];
        };
        getCommit: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/git/commits/{commit_sha}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/git/commits/{commit_sha}"]["response"];
        };
        getRef: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/git/ref/{ref}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/git/ref/{ref}"]["response"];
        };
        getTag: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/git/tags/{tag_sha}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/git/tags/{tag_sha}"]["response"];
        };
        getTree: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/git/trees/{tree_sha}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/git/trees/{tree_sha}"]["response"];
        };
        listMatchingRefs: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/git/matching-refs/{ref}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/git/matching-refs/{ref}"]["response"];
        };
        updateRef: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/git/refs/{ref}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/git/refs/{ref}"]["response"];
        };
    };
    gitignore: {
        getAllTemplates: {
            parameters: RequestParameters & Endpoints["GET /gitignore/templates"]["parameters"];
            response: Endpoints["GET /gitignore/templates"]["response"];
        };
        getTemplate: {
            parameters: RequestParameters & Endpoints["GET /gitignore/templates/{name}"]["parameters"];
            response: Endpoints["GET /gitignore/templates/{name}"]["response"];
        };
    };
    hostedCompute: {
        createNetworkConfigurationForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/settings/network-configurations"]["parameters"];
            response: Endpoints["POST /orgs/{org}/settings/network-configurations"]["response"];
        };
        deleteNetworkConfigurationFromOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/settings/network-configurations/{network_configuration_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/settings/network-configurations/{network_configuration_id}"]["response"];
        };
        getNetworkConfigurationForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/settings/network-configurations/{network_configuration_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/settings/network-configurations/{network_configuration_id}"]["response"];
        };
        getNetworkSettingsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/settings/network-settings/{network_settings_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/settings/network-settings/{network_settings_id}"]["response"];
        };
        listNetworkConfigurationsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/settings/network-configurations"]["parameters"];
            response: Endpoints["GET /orgs/{org}/settings/network-configurations"]["response"];
        };
        updateNetworkConfigurationForOrg: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/settings/network-configurations/{network_configuration_id}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/settings/network-configurations/{network_configuration_id}"]["response"];
        };
    };
    interactions: {
        getRestrictionsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/interaction-limits"]["parameters"];
            response: Endpoints["GET /user/interaction-limits"]["response"];
        };
        getRestrictionsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/interaction-limits"]["parameters"];
            response: Endpoints["GET /orgs/{org}/interaction-limits"]["response"];
        };
        getRestrictionsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/interaction-limits"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/interaction-limits"]["response"];
        };
        getRestrictionsForYourPublicRepos: {
            parameters: RequestParameters & Endpoints["GET /user/interaction-limits"]["parameters"];
            response: Endpoints["GET /user/interaction-limits"]["response"];
        };
        removeRestrictionsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/interaction-limits"]["parameters"];
            response: Endpoints["DELETE /user/interaction-limits"]["response"];
        };
        removeRestrictionsForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/interaction-limits"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/interaction-limits"]["response"];
        };
        removeRestrictionsForRepo: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/interaction-limits"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/interaction-limits"]["response"];
        };
        removeRestrictionsForYourPublicRepos: {
            parameters: RequestParameters & Endpoints["DELETE /user/interaction-limits"]["parameters"];
            response: Endpoints["DELETE /user/interaction-limits"]["response"];
        };
        setRestrictionsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PUT /user/interaction-limits"]["parameters"];
            response: Endpoints["PUT /user/interaction-limits"]["response"];
        };
        setRestrictionsForOrg: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/interaction-limits"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/interaction-limits"]["response"];
        };
        setRestrictionsForRepo: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/interaction-limits"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/interaction-limits"]["response"];
        };
        setRestrictionsForYourPublicRepos: {
            parameters: RequestParameters & Endpoints["PUT /user/interaction-limits"]["parameters"];
            response: Endpoints["PUT /user/interaction-limits"]["response"];
        };
    };
    issues: {
        addAssignees: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/assignees"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/assignees"]["response"];
        };
        addLabels: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/labels"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/labels"]["response"];
        };
        addSubIssue: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"]["response"];
        };
        checkUserCanBeAssigned: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/assignees/{assignee}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/assignees/{assignee}"]["response"];
        };
        checkUserCanBeAssignedToIssue: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/assignees/{assignee}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/assignees/{assignee}"]["response"];
        };
        create: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/issues"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/issues"]["response"];
        };
        createComment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/comments"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/comments"]["response"];
        };
        createLabel: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/labels"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/labels"]["response"];
        };
        createMilestone: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/milestones"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/milestones"]["response"];
        };
        deleteComment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}"]["response"];
        };
        deleteLabel: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/labels/{name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/labels/{name}"]["response"];
        };
        deleteMilestone: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/milestones/{milestone_number}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/milestones/{milestone_number}"]["response"];
        };
        get: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}"]["response"];
        };
        getComment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/comments/{comment_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/comments/{comment_id}"]["response"];
        };
        getEvent: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/events/{event_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/events/{event_id}"]["response"];
        };
        getLabel: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/labels/{name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/labels/{name}"]["response"];
        };
        getMilestone: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/milestones/{milestone_number}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/milestones/{milestone_number}"]["response"];
        };
        list: {
            parameters: RequestParameters & Endpoints["GET /issues"]["parameters"];
            response: Endpoints["GET /issues"]["response"];
        };
        listAssignees: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/assignees"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/assignees"]["response"];
        };
        listComments: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/comments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/comments"]["response"];
        };
        listCommentsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/comments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/comments"]["response"];
        };
        listEvents: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/events"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/events"]["response"];
        };
        listEventsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/events"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/events"]["response"];
        };
        listEventsForTimeline: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/timeline"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/timeline"]["response"];
        };
        listForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/issues"]["parameters"];
            response: Endpoints["GET /user/issues"]["response"];
        };
        listForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/issues"]["parameters"];
            response: Endpoints["GET /orgs/{org}/issues"]["response"];
        };
        listForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues"]["response"];
        };
        listLabelsForMilestone: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels"]["response"];
        };
        listLabelsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/labels"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/labels"]["response"];
        };
        listLabelsOnIssue: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/labels"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/labels"]["response"];
        };
        listMilestones: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/milestones"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/milestones"]["response"];
        };
        listSubIssues: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"]["response"];
        };
        lock: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/issues/{issue_number}/lock"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/issues/{issue_number}/lock"]["response"];
        };
        removeAllLabels: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels"]["response"];
        };
        removeAssignees: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees"]["response"];
        };
        removeLabel: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}"]["response"];
        };
        removeSubIssue: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/sub_issue"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/sub_issue"]["response"];
        };
        reprioritizeSubIssue: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/issues/{issue_number}/sub_issues/priority"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/issues/{issue_number}/sub_issues/priority"]["response"];
        };
        setLabels: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/issues/{issue_number}/labels"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/issues/{issue_number}/labels"]["response"];
        };
        unlock: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock"]["response"];
        };
        update: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/issues/{issue_number}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/issues/{issue_number}"]["response"];
        };
        updateComment: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}"]["response"];
        };
        updateLabel: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/labels/{name}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/labels/{name}"]["response"];
        };
        updateMilestone: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/milestones/{milestone_number}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/milestones/{milestone_number}"]["response"];
        };
    };
    licenses: {
        get: {
            parameters: RequestParameters & Endpoints["GET /licenses/{license}"]["parameters"];
            response: Endpoints["GET /licenses/{license}"]["response"];
        };
        getAllCommonlyUsed: {
            parameters: RequestParameters & Endpoints["GET /licenses"]["parameters"];
            response: Endpoints["GET /licenses"]["response"];
        };
        getForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/license"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/license"]["response"];
        };
    };
    markdown: {
        render: {
            parameters: RequestParameters & Endpoints["POST /markdown"]["parameters"];
            response: Endpoints["POST /markdown"]["response"];
        };
        renderRaw: {
            parameters: RequestParameters & Endpoints["POST /markdown/raw"]["parameters"];
            response: Endpoints["POST /markdown/raw"]["response"];
        };
    };
    meta: {
        get: {
            parameters: RequestParameters & Endpoints["GET /meta"]["parameters"];
            response: Endpoints["GET /meta"]["response"];
        };
        getAllVersions: {
            parameters: RequestParameters & Endpoints["GET /versions"]["parameters"];
            response: Endpoints["GET /versions"]["response"];
        };
        getOctocat: {
            parameters: RequestParameters & Endpoints["GET /octocat"]["parameters"];
            response: Endpoints["GET /octocat"]["response"];
        };
        getZen: {
            parameters: RequestParameters & Endpoints["GET /zen"]["parameters"];
            response: Endpoints["GET /zen"]["response"];
        };
        root: {
            parameters: RequestParameters & Endpoints["GET /"]["parameters"];
            response: Endpoints["GET /"]["response"];
        };
    };
    migrations: {
        deleteArchiveForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/migrations/{migration_id}/archive"]["parameters"];
            response: Endpoints["DELETE /user/migrations/{migration_id}/archive"]["response"];
        };
        deleteArchiveForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/migrations/{migration_id}/archive"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/migrations/{migration_id}/archive"]["response"];
        };
        downloadArchiveForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/migrations/{migration_id}/archive"]["parameters"];
            response: Endpoints["GET /orgs/{org}/migrations/{migration_id}/archive"]["response"];
        };
        getArchiveForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/migrations/{migration_id}/archive"]["parameters"];
            response: Endpoints["GET /user/migrations/{migration_id}/archive"]["response"];
        };
        getStatusForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/migrations/{migration_id}"]["parameters"];
            response: Endpoints["GET /user/migrations/{migration_id}"]["response"];
        };
        getStatusForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/migrations/{migration_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/migrations/{migration_id}"]["response"];
        };
        listForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/migrations"]["parameters"];
            response: Endpoints["GET /user/migrations"]["response"];
        };
        listForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/migrations"]["parameters"];
            response: Endpoints["GET /orgs/{org}/migrations"]["response"];
        };
        listReposForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/migrations/{migration_id}/repositories"]["parameters"];
            response: Endpoints["GET /user/migrations/{migration_id}/repositories"]["response"];
        };
        listReposForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/migrations/{migration_id}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/migrations/{migration_id}/repositories"]["response"];
        };
        listReposForUser: {
            parameters: RequestParameters & Endpoints["GET /user/migrations/{migration_id}/repositories"]["parameters"];
            response: Endpoints["GET /user/migrations/{migration_id}/repositories"]["response"];
        };
        startForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/migrations"]["parameters"];
            response: Endpoints["POST /user/migrations"]["response"];
        };
        startForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/migrations"]["parameters"];
            response: Endpoints["POST /orgs/{org}/migrations"]["response"];
        };
        unlockRepoForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock"]["parameters"];
            response: Endpoints["DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock"]["response"];
        };
        unlockRepoForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock"]["response"];
        };
    };
    oidc: {
        getOidcCustomSubTemplateForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/actions/oidc/customization/sub"]["parameters"];
            response: Endpoints["GET /orgs/{org}/actions/oidc/customization/sub"]["response"];
        };
        updateOidcCustomSubTemplateForOrg: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/actions/oidc/customization/sub"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/actions/oidc/customization/sub"]["response"];
        };
    };
    orgs: {
        addSecurityManagerTeam: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/security-managers/teams/{team_slug}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/security-managers/teams/{team_slug}"]["response"];
        };
        assignTeamToOrgRole: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"]["response"];
        };
        assignUserToOrgRole: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/organization-roles/users/{username}/{role_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/organization-roles/users/{username}/{role_id}"]["response"];
        };
        blockUser: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/blocks/{username}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/blocks/{username}"]["response"];
        };
        cancelInvitation: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/invitations/{invitation_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/invitations/{invitation_id}"]["response"];
        };
        checkBlockedUser: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/blocks/{username}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/blocks/{username}"]["response"];
        };
        checkMembershipForUser: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/members/{username}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/members/{username}"]["response"];
        };
        checkPublicMembershipForUser: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/public_members/{username}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/public_members/{username}"]["response"];
        };
        convertMemberToOutsideCollaborator: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/outside_collaborators/{username}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/outside_collaborators/{username}"]["response"];
        };
        createInvitation: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/invitations"]["parameters"];
            response: Endpoints["POST /orgs/{org}/invitations"]["response"];
        };
        createIssueType: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/issue-types"]["parameters"];
            response: Endpoints["POST /orgs/{org}/issue-types"]["response"];
        };
        createOrUpdateCustomProperties: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/properties/schema"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/properties/schema"]["response"];
        };
        createOrUpdateCustomPropertiesValuesForRepos: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/properties/values"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/properties/values"]["response"];
        };
        createOrUpdateCustomProperty: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/properties/schema/{custom_property_name}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/properties/schema/{custom_property_name}"]["response"];
        };
        createWebhook: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/hooks"]["parameters"];
            response: Endpoints["POST /orgs/{org}/hooks"]["response"];
        };
        delete: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}"]["response"];
        };
        deleteIssueType: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/issue-types/{issue_type_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/issue-types/{issue_type_id}"]["response"];
        };
        deleteWebhook: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/hooks/{hook_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/hooks/{hook_id}"]["response"];
        };
        enableOrDisableSecurityProductOnAllOrgRepos: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/{security_product}/{enablement}"]["parameters"];
            response: Endpoints["POST /orgs/{org}/{security_product}/{enablement}"]["response"];
        };
        get: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}"]["parameters"];
            response: Endpoints["GET /orgs/{org}"]["response"];
        };
        getAllCustomProperties: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/properties/schema"]["parameters"];
            response: Endpoints["GET /orgs/{org}/properties/schema"]["response"];
        };
        getCustomProperty: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/properties/schema/{custom_property_name}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/properties/schema/{custom_property_name}"]["response"];
        };
        getMembershipForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/memberships/orgs/{org}"]["parameters"];
            response: Endpoints["GET /user/memberships/orgs/{org}"]["response"];
        };
        getMembershipForUser: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/memberships/{username}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/memberships/{username}"]["response"];
        };
        getOrgRole: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/organization-roles/{role_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/organization-roles/{role_id}"]["response"];
        };
        getOrgRulesetHistory: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}/history"]["parameters"];
            response: Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}/history"]["response"];
        };
        getOrgRulesetVersion: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}/history/{version_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}/history/{version_id}"]["response"];
        };
        getWebhook: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/hooks/{hook_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/hooks/{hook_id}"]["response"];
        };
        getWebhookConfigForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/hooks/{hook_id}/config"]["parameters"];
            response: Endpoints["GET /orgs/{org}/hooks/{hook_id}/config"]["response"];
        };
        getWebhookDelivery: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}"]["response"];
        };
        list: {
            parameters: RequestParameters & Endpoints["GET /organizations"]["parameters"];
            response: Endpoints["GET /organizations"]["response"];
        };
        listAppInstallations: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/installations"]["parameters"];
            response: Endpoints["GET /orgs/{org}/installations"]["response"];
        };
        listAttestations: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/attestations/{subject_digest}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/attestations/{subject_digest}"]["response"];
        };
        listBlockedUsers: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/blocks"]["parameters"];
            response: Endpoints["GET /orgs/{org}/blocks"]["response"];
        };
        listCustomPropertiesValuesForRepos: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/properties/values"]["parameters"];
            response: Endpoints["GET /orgs/{org}/properties/values"]["response"];
        };
        listFailedInvitations: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/failed_invitations"]["parameters"];
            response: Endpoints["GET /orgs/{org}/failed_invitations"]["response"];
        };
        listForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/orgs"]["parameters"];
            response: Endpoints["GET /user/orgs"]["response"];
        };
        listForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/orgs"]["parameters"];
            response: Endpoints["GET /users/{username}/orgs"]["response"];
        };
        listInvitationTeams: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/invitations/{invitation_id}/teams"]["parameters"];
            response: Endpoints["GET /orgs/{org}/invitations/{invitation_id}/teams"]["response"];
        };
        listIssueTypes: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/issue-types"]["parameters"];
            response: Endpoints["GET /orgs/{org}/issue-types"]["response"];
        };
        listMembers: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/members"]["parameters"];
            response: Endpoints["GET /orgs/{org}/members"]["response"];
        };
        listMembershipsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/memberships/orgs"]["parameters"];
            response: Endpoints["GET /user/memberships/orgs"]["response"];
        };
        listOrgRoleTeams: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/organization-roles/{role_id}/teams"]["parameters"];
            response: Endpoints["GET /orgs/{org}/organization-roles/{role_id}/teams"]["response"];
        };
        listOrgRoleUsers: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/organization-roles/{role_id}/users"]["parameters"];
            response: Endpoints["GET /orgs/{org}/organization-roles/{role_id}/users"]["response"];
        };
        listOrgRoles: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/organization-roles"]["parameters"];
            response: Endpoints["GET /orgs/{org}/organization-roles"]["response"];
        };
        listOrganizationFineGrainedPermissions: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/organization-fine-grained-permissions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/organization-fine-grained-permissions"]["response"];
        };
        listOutsideCollaborators: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/outside_collaborators"]["parameters"];
            response: Endpoints["GET /orgs/{org}/outside_collaborators"]["response"];
        };
        listPatGrantRepositories: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories"]["response"];
        };
        listPatGrantRequestRepositories: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories"]["response"];
        };
        listPatGrantRequests: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/personal-access-token-requests"]["parameters"];
            response: Endpoints["GET /orgs/{org}/personal-access-token-requests"]["response"];
        };
        listPatGrants: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/personal-access-tokens"]["parameters"];
            response: Endpoints["GET /orgs/{org}/personal-access-tokens"]["response"];
        };
        listPendingInvitations: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/invitations"]["parameters"];
            response: Endpoints["GET /orgs/{org}/invitations"]["response"];
        };
        listPublicMembers: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/public_members"]["parameters"];
            response: Endpoints["GET /orgs/{org}/public_members"]["response"];
        };
        listSecurityManagerTeams: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/security-managers"]["parameters"];
            response: Endpoints["GET /orgs/{org}/security-managers"]["response"];
        };
        listWebhookDeliveries: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/hooks/{hook_id}/deliveries"]["parameters"];
            response: Endpoints["GET /orgs/{org}/hooks/{hook_id}/deliveries"]["response"];
        };
        listWebhooks: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/hooks"]["parameters"];
            response: Endpoints["GET /orgs/{org}/hooks"]["response"];
        };
        pingWebhook: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/hooks/{hook_id}/pings"]["parameters"];
            response: Endpoints["POST /orgs/{org}/hooks/{hook_id}/pings"]["response"];
        };
        redeliverWebhookDelivery: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"]["parameters"];
            response: Endpoints["POST /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"]["response"];
        };
        removeCustomProperty: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/properties/schema/{custom_property_name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/properties/schema/{custom_property_name}"]["response"];
        };
        removeMember: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/members/{username}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/members/{username}"]["response"];
        };
        removeMembershipForUser: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/memberships/{username}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/memberships/{username}"]["response"];
        };
        removeOutsideCollaborator: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/outside_collaborators/{username}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/outside_collaborators/{username}"]["response"];
        };
        removePublicMembershipForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/public_members/{username}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/public_members/{username}"]["response"];
        };
        removeSecurityManagerTeam: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/security-managers/teams/{team_slug}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/security-managers/teams/{team_slug}"]["response"];
        };
        reviewPatGrantRequest: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/personal-access-token-requests/{pat_request_id}"]["parameters"];
            response: Endpoints["POST /orgs/{org}/personal-access-token-requests/{pat_request_id}"]["response"];
        };
        reviewPatGrantRequestsInBulk: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/personal-access-token-requests"]["parameters"];
            response: Endpoints["POST /orgs/{org}/personal-access-token-requests"]["response"];
        };
        revokeAllOrgRolesTeam: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/organization-roles/teams/{team_slug}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/organization-roles/teams/{team_slug}"]["response"];
        };
        revokeAllOrgRolesUser: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/organization-roles/users/{username}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/organization-roles/users/{username}"]["response"];
        };
        revokeOrgRoleTeam: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"]["response"];
        };
        revokeOrgRoleUser: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/organization-roles/users/{username}/{role_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/organization-roles/users/{username}/{role_id}"]["response"];
        };
        setMembershipForUser: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/memberships/{username}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/memberships/{username}"]["response"];
        };
        setPublicMembershipForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/public_members/{username}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/public_members/{username}"]["response"];
        };
        unblockUser: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/blocks/{username}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/blocks/{username}"]["response"];
        };
        update: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}"]["response"];
        };
        updateIssueType: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/issue-types/{issue_type_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/issue-types/{issue_type_id}"]["response"];
        };
        updateMembershipForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PATCH /user/memberships/orgs/{org}"]["parameters"];
            response: Endpoints["PATCH /user/memberships/orgs/{org}"]["response"];
        };
        updatePatAccess: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/personal-access-tokens/{pat_id}"]["parameters"];
            response: Endpoints["POST /orgs/{org}/personal-access-tokens/{pat_id}"]["response"];
        };
        updatePatAccesses: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/personal-access-tokens"]["parameters"];
            response: Endpoints["POST /orgs/{org}/personal-access-tokens"]["response"];
        };
        updateWebhook: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/hooks/{hook_id}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/hooks/{hook_id}"]["response"];
        };
        updateWebhookConfigForOrg: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/hooks/{hook_id}/config"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/hooks/{hook_id}/config"]["response"];
        };
    };
    packages: {
        deletePackageForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/packages/{package_type}/{package_name}"]["parameters"];
            response: Endpoints["DELETE /user/packages/{package_type}/{package_name}"]["response"];
        };
        deletePackageForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/packages/{package_type}/{package_name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/packages/{package_type}/{package_name}"]["response"];
        };
        deletePackageForUser: {
            parameters: RequestParameters & Endpoints["DELETE /users/{username}/packages/{package_type}/{package_name}"]["parameters"];
            response: Endpoints["DELETE /users/{username}/packages/{package_type}/{package_name}"]["response"];
        };
        deletePackageVersionForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}"]["parameters"];
            response: Endpoints["DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}"]["response"];
        };
        deletePackageVersionForOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["response"];
        };
        deletePackageVersionForUser: {
            parameters: RequestParameters & Endpoints["DELETE /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["parameters"];
            response: Endpoints["DELETE /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["response"];
        };
        getAllPackageVersionsForAPackageOwnedByAnOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"]["response"];
        };
        getAllPackageVersionsForAPackageOwnedByTheAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/packages/{package_type}/{package_name}/versions"]["parameters"];
            response: Endpoints["GET /user/packages/{package_type}/{package_name}/versions"]["response"];
        };
        getAllPackageVersionsForPackageOwnedByAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/packages/{package_type}/{package_name}/versions"]["parameters"];
            response: Endpoints["GET /user/packages/{package_type}/{package_name}/versions"]["response"];
        };
        getAllPackageVersionsForPackageOwnedByOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"]["response"];
        };
        getAllPackageVersionsForPackageOwnedByUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/packages/{package_type}/{package_name}/versions"]["parameters"];
            response: Endpoints["GET /users/{username}/packages/{package_type}/{package_name}/versions"]["response"];
        };
        getPackageForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/packages/{package_type}/{package_name}"]["parameters"];
            response: Endpoints["GET /user/packages/{package_type}/{package_name}"]["response"];
        };
        getPackageForOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}"]["response"];
        };
        getPackageForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/packages/{package_type}/{package_name}"]["parameters"];
            response: Endpoints["GET /users/{username}/packages/{package_type}/{package_name}"]["response"];
        };
        getPackageVersionForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}"]["parameters"];
            response: Endpoints["GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}"]["response"];
        };
        getPackageVersionForOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["response"];
        };
        getPackageVersionForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["parameters"];
            response: Endpoints["GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"]["response"];
        };
        listDockerMigrationConflictingPackagesForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/docker/conflicts"]["parameters"];
            response: Endpoints["GET /user/docker/conflicts"]["response"];
        };
        listDockerMigrationConflictingPackagesForOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/docker/conflicts"]["parameters"];
            response: Endpoints["GET /orgs/{org}/docker/conflicts"]["response"];
        };
        listDockerMigrationConflictingPackagesForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/docker/conflicts"]["parameters"];
            response: Endpoints["GET /users/{username}/docker/conflicts"]["response"];
        };
        listPackagesForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/packages"]["parameters"];
            response: Endpoints["GET /user/packages"]["response"];
        };
        listPackagesForOrganization: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/packages"]["parameters"];
            response: Endpoints["GET /orgs/{org}/packages"]["response"];
        };
        listPackagesForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/packages"]["parameters"];
            response: Endpoints["GET /users/{username}/packages"]["response"];
        };
        restorePackageForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/packages/{package_type}/{package_name}/restore{?token}"]["parameters"];
            response: Endpoints["POST /user/packages/{package_type}/{package_name}/restore{?token}"]["response"];
        };
        restorePackageForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}"]["parameters"];
            response: Endpoints["POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}"]["response"];
        };
        restorePackageForUser: {
            parameters: RequestParameters & Endpoints["POST /users/{username}/packages/{package_type}/{package_name}/restore{?token}"]["parameters"];
            response: Endpoints["POST /users/{username}/packages/{package_type}/{package_name}/restore{?token}"]["response"];
        };
        restorePackageVersionForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]["parameters"];
            response: Endpoints["POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]["response"];
        };
        restorePackageVersionForOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]["parameters"];
            response: Endpoints["POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]["response"];
        };
        restorePackageVersionForUser: {
            parameters: RequestParameters & Endpoints["POST /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]["parameters"];
            response: Endpoints["POST /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]["response"];
        };
    };
    privateRegistries: {
        createOrgPrivateRegistry: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/private-registries"]["parameters"];
            response: Endpoints["POST /orgs/{org}/private-registries"]["response"];
        };
        deleteOrgPrivateRegistry: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/private-registries/{secret_name}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/private-registries/{secret_name}"]["response"];
        };
        getOrgPrivateRegistry: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/private-registries/{secret_name}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/private-registries/{secret_name}"]["response"];
        };
        getOrgPublicKey: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/private-registries/public-key"]["parameters"];
            response: Endpoints["GET /orgs/{org}/private-registries/public-key"]["response"];
        };
        listOrgPrivateRegistries: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/private-registries"]["parameters"];
            response: Endpoints["GET /orgs/{org}/private-registries"]["response"];
        };
        updateOrgPrivateRegistry: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/private-registries/{secret_name}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/private-registries/{secret_name}"]["response"];
        };
    };
    pulls: {
        checkIfMerged: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/merge"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/merge"]["response"];
        };
        create: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls"]["response"];
        };
        createReplyForReviewComment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies"]["response"];
        };
        createReview: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews"]["response"];
        };
        createReviewComment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments"]["response"];
        };
        deletePendingReview: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"]["response"];
        };
        deleteReviewComment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}"]["response"];
        };
        dismissReview: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals"]["response"];
        };
        get: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}"]["response"];
        };
        getReview: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"]["response"];
        };
        getReviewComment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}"]["response"];
        };
        list: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls"]["response"];
        };
        listCommentsForReview: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments"]["response"];
        };
        listCommits: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/commits"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/commits"]["response"];
        };
        listFiles: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/files"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/files"]["response"];
        };
        listRequestedReviewers: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"]["response"];
        };
        listReviewComments: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/comments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/comments"]["response"];
        };
        listReviewCommentsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/comments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/comments"]["response"];
        };
        listReviews: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews"]["response"];
        };
        merge: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge"]["response"];
        };
        removeRequestedReviewers: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"]["response"];
        };
        requestReviewers: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"]["response"];
        };
        submitReview: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events"]["response"];
        };
        update: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/pulls/{pull_number}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/pulls/{pull_number}"]["response"];
        };
        updateBranch: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch"]["response"];
        };
        updateReview: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"]["response"];
        };
        updateReviewComment: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}"]["response"];
        };
    };
    rateLimit: {
        get: {
            parameters: RequestParameters & Endpoints["GET /rate_limit"]["parameters"];
            response: Endpoints["GET /rate_limit"]["response"];
        };
    };
    reactions: {
        createForCommitComment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/comments/{comment_id}/reactions"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/comments/{comment_id}/reactions"]["response"];
        };
        createForIssue: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/reactions"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/issues/{issue_number}/reactions"]["response"];
        };
        createForIssueComment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"]["response"];
        };
        createForPullRequestReviewComment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"]["response"];
        };
        createForRelease: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/releases/{release_id}/reactions"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/releases/{release_id}/reactions"]["response"];
        };
        createForTeamDiscussionCommentInOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["parameters"];
            response: Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["response"];
        };
        createForTeamDiscussionInOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]["parameters"];
            response: Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]["response"];
        };
        deleteForCommitComment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}"]["response"];
        };
        deleteForIssue: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}"]["response"];
        };
        deleteForIssueComment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}"]["response"];
        };
        deleteForPullRequestComment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}"]["response"];
        };
        deleteForRelease: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/releases/{release_id}/reactions/{reaction_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/releases/{release_id}/reactions/{reaction_id}"]["response"];
        };
        deleteForTeamDiscussion: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}"]["response"];
        };
        deleteForTeamDiscussionComment: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}"]["response"];
        };
        listForCommitComment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/comments/{comment_id}/reactions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/comments/{comment_id}/reactions"]["response"];
        };
        listForIssue: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/reactions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/reactions"]["response"];
        };
        listForIssueComment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"]["response"];
        };
        listForPullRequestReviewComment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"]["response"];
        };
        listForRelease: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/reactions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/reactions"]["response"];
        };
        listForTeamDiscussionCommentInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["response"];
        };
        listForTeamDiscussionInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]["response"];
        };
    };
    repos: {
        acceptInvitation: {
            parameters: RequestParameters & Endpoints["PATCH /user/repository_invitations/{invitation_id}"]["parameters"];
            response: Endpoints["PATCH /user/repository_invitations/{invitation_id}"]["response"];
        };
        acceptInvitationForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PATCH /user/repository_invitations/{invitation_id}"]["parameters"];
            response: Endpoints["PATCH /user/repository_invitations/{invitation_id}"]["response"];
        };
        addAppAccessRestrictions: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["response"];
        };
        addCollaborator: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/collaborators/{username}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/collaborators/{username}"]["response"];
        };
        addStatusCheckContexts: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["response"];
        };
        addTeamAccessRestrictions: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["response"];
        };
        addUserAccessRestrictions: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["response"];
        };
        cancelPagesDeployment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}/cancel"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}/cancel"]["response"];
        };
        checkAutomatedSecurityFixes: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/automated-security-fixes"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/automated-security-fixes"]["response"];
        };
        checkCollaborator: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/collaborators/{username}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/collaborators/{username}"]["response"];
        };
        checkPrivateVulnerabilityReporting: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/private-vulnerability-reporting"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/private-vulnerability-reporting"]["response"];
        };
        checkVulnerabilityAlerts: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/vulnerability-alerts"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/vulnerability-alerts"]["response"];
        };
        codeownersErrors: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/codeowners/errors"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/codeowners/errors"]["response"];
        };
        compareCommits: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/compare/{base}...{head}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/compare/{base}...{head}"]["response"];
        };
        compareCommitsWithBasehead: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/compare/{basehead}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/compare/{basehead}"]["response"];
        };
        createAttestation: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/attestations"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/attestations"]["response"];
        };
        createAutolink: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/autolinks"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/autolinks"]["response"];
        };
        createCommitComment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/commits/{commit_sha}/comments"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/commits/{commit_sha}/comments"]["response"];
        };
        createCommitSignatureProtection: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"]["response"];
        };
        createCommitStatus: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/statuses/{sha}"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/statuses/{sha}"]["response"];
        };
        createDeployKey: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/keys"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/keys"]["response"];
        };
        createDeployment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/deployments"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/deployments"]["response"];
        };
        createDeploymentBranchPolicy: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"]["response"];
        };
        createDeploymentProtectionRule: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"]["response"];
        };
        createDeploymentStatus: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"]["response"];
        };
        createDispatchEvent: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/dispatches"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/dispatches"]["response"];
        };
        createForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/repos"]["parameters"];
            response: Endpoints["POST /user/repos"]["response"];
        };
        createFork: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/forks"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/forks"]["response"];
        };
        createInOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/repos"]["parameters"];
            response: Endpoints["POST /orgs/{org}/repos"]["response"];
        };
        createOrUpdateCustomPropertiesValues: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/properties/values"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/properties/values"]["response"];
        };
        createOrUpdateEnvironment: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/environments/{environment_name}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/environments/{environment_name}"]["response"];
        };
        createOrUpdateFileContents: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/contents/{path}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/contents/{path}"]["response"];
        };
        createOrgRuleset: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/rulesets"]["parameters"];
            response: Endpoints["POST /orgs/{org}/rulesets"]["response"];
        };
        createPagesDeployment: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pages/deployments"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pages/deployments"]["response"];
        };
        createPagesSite: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pages"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pages"]["response"];
        };
        createRelease: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/releases"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/releases"]["response"];
        };
        createRepoRuleset: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/rulesets"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/rulesets"]["response"];
        };
        createUsingTemplate: {
            parameters: RequestParameters & Endpoints["POST /repos/{template_owner}/{template_repo}/generate"]["parameters"];
            response: Endpoints["POST /repos/{template_owner}/{template_repo}/generate"]["response"];
        };
        createWebhook: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/hooks"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/hooks"]["response"];
        };
        declineInvitation: {
            parameters: RequestParameters & Endpoints["DELETE /user/repository_invitations/{invitation_id}"]["parameters"];
            response: Endpoints["DELETE /user/repository_invitations/{invitation_id}"]["response"];
        };
        declineInvitationForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/repository_invitations/{invitation_id}"]["parameters"];
            response: Endpoints["DELETE /user/repository_invitations/{invitation_id}"]["response"];
        };
        delete: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}"]["response"];
        };
        deleteAccessRestrictions: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"]["response"];
        };
        deleteAdminBranchProtection: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"]["response"];
        };
        deleteAnEnvironment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}"]["response"];
        };
        deleteAutolink: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/autolinks/{autolink_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/autolinks/{autolink_id}"]["response"];
        };
        deleteBranchProtection: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection"]["response"];
        };
        deleteCommitComment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/comments/{comment_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/comments/{comment_id}"]["response"];
        };
        deleteCommitSignatureProtection: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"]["response"];
        };
        deleteDeployKey: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/keys/{key_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/keys/{key_id}"]["response"];
        };
        deleteDeployment: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/deployments/{deployment_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/deployments/{deployment_id}"]["response"];
        };
        deleteDeploymentBranchPolicy: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"]["response"];
        };
        deleteFile: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/contents/{path}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/contents/{path}"]["response"];
        };
        deleteInvitation: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/invitations/{invitation_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/invitations/{invitation_id}"]["response"];
        };
        deleteOrgRuleset: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/rulesets/{ruleset_id}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/rulesets/{ruleset_id}"]["response"];
        };
        deletePagesSite: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/pages"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/pages"]["response"];
        };
        deletePullRequestReviewProtection: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"]["response"];
        };
        deleteRelease: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/releases/{release_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/releases/{release_id}"]["response"];
        };
        deleteReleaseAsset: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}"]["response"];
        };
        deleteRepoRuleset: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/rulesets/{ruleset_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/rulesets/{ruleset_id}"]["response"];
        };
        deleteWebhook: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/hooks/{hook_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/hooks/{hook_id}"]["response"];
        };
        disableAutomatedSecurityFixes: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/automated-security-fixes"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/automated-security-fixes"]["response"];
        };
        disableDeploymentProtectionRule: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"]["response"];
        };
        disablePrivateVulnerabilityReporting: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/private-vulnerability-reporting"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/private-vulnerability-reporting"]["response"];
        };
        disableVulnerabilityAlerts: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/vulnerability-alerts"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/vulnerability-alerts"]["response"];
        };
        downloadArchive: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/zipball/{ref}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/zipball/{ref}"]["response"];
        };
        downloadTarballArchive: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/tarball/{ref}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/tarball/{ref}"]["response"];
        };
        downloadZipballArchive: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/zipball/{ref}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/zipball/{ref}"]["response"];
        };
        enableAutomatedSecurityFixes: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/automated-security-fixes"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/automated-security-fixes"]["response"];
        };
        enablePrivateVulnerabilityReporting: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/private-vulnerability-reporting"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/private-vulnerability-reporting"]["response"];
        };
        enableVulnerabilityAlerts: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/vulnerability-alerts"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/vulnerability-alerts"]["response"];
        };
        generateReleaseNotes: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/releases/generate-notes"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/releases/generate-notes"]["response"];
        };
        get: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}"]["response"];
        };
        getAccessRestrictions: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"]["response"];
        };
        getAdminBranchProtection: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"]["response"];
        };
        getAllDeploymentProtectionRules: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"]["response"];
        };
        getAllEnvironments: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments"]["response"];
        };
        getAllStatusCheckContexts: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["response"];
        };
        getAllTopics: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/topics"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/topics"]["response"];
        };
        getAppsWithAccessToProtectedBranch: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["response"];
        };
        getAutolink: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/autolinks/{autolink_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/autolinks/{autolink_id}"]["response"];
        };
        getBranch: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}"]["response"];
        };
        getBranchProtection: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection"]["response"];
        };
        getBranchRules: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/rules/branches/{branch}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/rules/branches/{branch}"]["response"];
        };
        getClones: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/traffic/clones"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/traffic/clones"]["response"];
        };
        getCodeFrequencyStats: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/stats/code_frequency"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/stats/code_frequency"]["response"];
        };
        getCollaboratorPermissionLevel: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/collaborators/{username}/permission"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/collaborators/{username}/permission"]["response"];
        };
        getCombinedStatusForRef: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/status"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/status"]["response"];
        };
        getCommit: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{ref}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}"]["response"];
        };
        getCommitActivityStats: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/stats/commit_activity"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/stats/commit_activity"]["response"];
        };
        getCommitComment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/comments/{comment_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/comments/{comment_id}"]["response"];
        };
        getCommitSignatureProtection: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"]["response"];
        };
        getCommunityProfileMetrics: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/community/profile"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/community/profile"]["response"];
        };
        getContent: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/contents/{path}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/contents/{path}"]["response"];
        };
        getContributorsStats: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/stats/contributors"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/stats/contributors"]["response"];
        };
        getCustomDeploymentProtectionRule: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"]["response"];
        };
        getCustomPropertiesValues: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/properties/values"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/properties/values"]["response"];
        };
        getDeployKey: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/keys/{key_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/keys/{key_id}"]["response"];
        };
        getDeployment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}"]["response"];
        };
        getDeploymentBranchPolicy: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"]["response"];
        };
        getDeploymentStatus: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}"]["response"];
        };
        getEnvironment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}"]["response"];
        };
        getLatestPagesBuild: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pages/builds/latest"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pages/builds/latest"]["response"];
        };
        getLatestRelease: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/releases/latest"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/releases/latest"]["response"];
        };
        getOrgRuleSuite: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/rulesets/rule-suites/{rule_suite_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/rulesets/rule-suites/{rule_suite_id}"]["response"];
        };
        getOrgRuleSuites: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/rulesets/rule-suites"]["parameters"];
            response: Endpoints["GET /orgs/{org}/rulesets/rule-suites"]["response"];
        };
        getOrgRuleset: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}"]["response"];
        };
        getOrgRulesets: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/rulesets"]["parameters"];
            response: Endpoints["GET /orgs/{org}/rulesets"]["response"];
        };
        getPages: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pages"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pages"]["response"];
        };
        getPagesBuild: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pages/builds/{build_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pages/builds/{build_id}"]["response"];
        };
        getPagesDeployment: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}"]["response"];
        };
        getPagesHealthCheck: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pages/health"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pages/health"]["response"];
        };
        getParticipationStats: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/stats/participation"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/stats/participation"]["response"];
        };
        getPullRequestReviewProtection: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"]["response"];
        };
        getPunchCardStats: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/stats/punch_card"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/stats/punch_card"]["response"];
        };
        getReadme: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/readme"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/readme"]["response"];
        };
        getReadmeInDirectory: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/readme/{dir}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/readme/{dir}"]["response"];
        };
        getRelease: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}"]["response"];
        };
        getReleaseAsset: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/releases/assets/{asset_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/releases/assets/{asset_id}"]["response"];
        };
        getReleaseByTag: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/releases/tags/{tag}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/releases/tags/{tag}"]["response"];
        };
        getRepoRuleSuite: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/rulesets/rule-suites/{rule_suite_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/rulesets/rule-suites/{rule_suite_id}"]["response"];
        };
        getRepoRuleSuites: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/rulesets/rule-suites"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/rulesets/rule-suites"]["response"];
        };
        getRepoRuleset: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}"]["response"];
        };
        getRepoRulesetHistory: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history"]["response"];
        };
        getRepoRulesetVersion: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history/{version_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history/{version_id}"]["response"];
        };
        getRepoRulesets: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/rulesets"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/rulesets"]["response"];
        };
        getStatusChecksProtection: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["response"];
        };
        getTeamsWithAccessToProtectedBranch: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["response"];
        };
        getTopPaths: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/traffic/popular/paths"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/traffic/popular/paths"]["response"];
        };
        getTopReferrers: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/traffic/popular/referrers"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/traffic/popular/referrers"]["response"];
        };
        getUsersWithAccessToProtectedBranch: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["response"];
        };
        getViews: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/traffic/views"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/traffic/views"]["response"];
        };
        getWebhook: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}"]["response"];
        };
        getWebhookConfigForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/config"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/config"]["response"];
        };
        getWebhookDelivery: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}"]["response"];
        };
        listActivities: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/activity"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/activity"]["response"];
        };
        listAttestations: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/attestations/{subject_digest}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/attestations/{subject_digest}"]["response"];
        };
        listAutolinks: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/autolinks"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/autolinks"]["response"];
        };
        listBranches: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/branches"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/branches"]["response"];
        };
        listBranchesForHeadCommit: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head"]["response"];
        };
        listCollaborators: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/collaborators"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/collaborators"]["response"];
        };
        listCommentsForCommit: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/comments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/comments"]["response"];
        };
        listCommitCommentsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/comments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/comments"]["response"];
        };
        listCommitStatusesForRef: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/statuses"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/statuses"]["response"];
        };
        listCommits: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits"]["response"];
        };
        listContributors: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/contributors"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/contributors"]["response"];
        };
        listCustomDeploymentRuleIntegrations: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps"]["response"];
        };
        listDeployKeys: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/keys"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/keys"]["response"];
        };
        listDeploymentBranchPolicies: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"]["response"];
        };
        listDeploymentStatuses: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"]["response"];
        };
        listDeployments: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/deployments"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/deployments"]["response"];
        };
        listForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/repos"]["parameters"];
            response: Endpoints["GET /user/repos"]["response"];
        };
        listForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/repos"]["parameters"];
            response: Endpoints["GET /orgs/{org}/repos"]["response"];
        };
        listForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/repos"]["parameters"];
            response: Endpoints["GET /users/{username}/repos"]["response"];
        };
        listForks: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/forks"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/forks"]["response"];
        };
        listInvitations: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/invitations"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/invitations"]["response"];
        };
        listInvitationsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/repository_invitations"]["parameters"];
            response: Endpoints["GET /user/repository_invitations"]["response"];
        };
        listLanguages: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/languages"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/languages"]["response"];
        };
        listPagesBuilds: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/pages/builds"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/pages/builds"]["response"];
        };
        listPublic: {
            parameters: RequestParameters & Endpoints["GET /repositories"]["parameters"];
            response: Endpoints["GET /repositories"]["response"];
        };
        listPullRequestsAssociatedWithCommit: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls"]["response"];
        };
        listReleaseAssets: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/assets"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/assets"]["response"];
        };
        listReleases: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/releases"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/releases"]["response"];
        };
        listTags: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/tags"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/tags"]["response"];
        };
        listTeams: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/teams"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/teams"]["response"];
        };
        listWebhookDeliveries: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries"]["response"];
        };
        listWebhooks: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/hooks"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/hooks"]["response"];
        };
        merge: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/merges"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/merges"]["response"];
        };
        mergeUpstream: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/merge-upstream"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/merge-upstream"]["response"];
        };
        pingWebhook: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/hooks/{hook_id}/pings"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/hooks/{hook_id}/pings"]["response"];
        };
        redeliverWebhookDelivery: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"]["response"];
        };
        removeAppAccessRestrictions: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["response"];
        };
        removeCollaborator: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/collaborators/{username}"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/collaborators/{username}"]["response"];
        };
        removeStatusCheckContexts: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["response"];
        };
        removeStatusCheckProtection: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["response"];
        };
        removeTeamAccessRestrictions: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["response"];
        };
        removeUserAccessRestrictions: {
            parameters: RequestParameters & Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["parameters"];
            response: Endpoints["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["response"];
        };
        renameBranch: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/rename"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/rename"]["response"];
        };
        replaceAllTopics: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/topics"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/topics"]["response"];
        };
        requestPagesBuild: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/pages/builds"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/pages/builds"]["response"];
        };
        setAdminBranchProtection: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"]["response"];
        };
        setAppAccessRestrictions: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"]["response"];
        };
        setStatusCheckContexts: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"]["response"];
        };
        setTeamAccessRestrictions: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"]["response"];
        };
        setUserAccessRestrictions: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"]["response"];
        };
        testPushWebhook: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/hooks/{hook_id}/tests"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/hooks/{hook_id}/tests"]["response"];
        };
        transfer: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/transfer"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/transfer"]["response"];
        };
        update: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}"]["response"];
        };
        updateBranchProtection: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/branches/{branch}/protection"]["response"];
        };
        updateCommitComment: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/comments/{comment_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/comments/{comment_id}"]["response"];
        };
        updateDeploymentBranchPolicy: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"]["response"];
        };
        updateInformationAboutPagesSite: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/pages"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/pages"]["response"];
        };
        updateInvitation: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/invitations/{invitation_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/invitations/{invitation_id}"]["response"];
        };
        updateOrgRuleset: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/rulesets/{ruleset_id}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/rulesets/{ruleset_id}"]["response"];
        };
        updatePullRequestReviewProtection: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"]["response"];
        };
        updateRelease: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/releases/{release_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/releases/{release_id}"]["response"];
        };
        updateReleaseAsset: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}"]["response"];
        };
        updateRepoRuleset: {
            parameters: RequestParameters & Endpoints["PUT /repos/{owner}/{repo}/rulesets/{ruleset_id}"]["parameters"];
            response: Endpoints["PUT /repos/{owner}/{repo}/rulesets/{ruleset_id}"]["response"];
        };
        updateStatusCheckPotection: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["response"];
        };
        updateStatusCheckProtection: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"]["response"];
        };
        updateWebhook: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/hooks/{hook_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/hooks/{hook_id}"]["response"];
        };
        updateWebhookConfigForRepo: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config"]["response"];
        };
        uploadReleaseAsset: {
            parameters: RequestParameters & Endpoints["POST {origin}/repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}"]["parameters"];
            response: Endpoints["POST {origin}/repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}"]["response"];
        };
    };
    search: {
        code: {
            parameters: RequestParameters & Endpoints["GET /search/code"]["parameters"];
            response: Endpoints["GET /search/code"]["response"];
        };
        commits: {
            parameters: RequestParameters & Endpoints["GET /search/commits"]["parameters"];
            response: Endpoints["GET /search/commits"]["response"];
        };
        issuesAndPullRequests: {
            parameters: RequestParameters & Endpoints["GET /search/issues"]["parameters"];
            response: Endpoints["GET /search/issues"]["response"];
        };
        labels: {
            parameters: RequestParameters & Endpoints["GET /search/labels"]["parameters"];
            response: Endpoints["GET /search/labels"]["response"];
        };
        repos: {
            parameters: RequestParameters & Endpoints["GET /search/repositories"]["parameters"];
            response: Endpoints["GET /search/repositories"]["response"];
        };
        topics: {
            parameters: RequestParameters & Endpoints["GET /search/topics"]["parameters"];
            response: Endpoints["GET /search/topics"]["response"];
        };
        users: {
            parameters: RequestParameters & Endpoints["GET /search/users"]["parameters"];
            response: Endpoints["GET /search/users"]["response"];
        };
    };
    secretScanning: {
        createPushProtectionBypass: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/secret-scanning/push-protection-bypasses"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/secret-scanning/push-protection-bypasses"]["response"];
        };
        getAlert: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"]["response"];
        };
        getScanHistory: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/secret-scanning/scan-history"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/scan-history"]["response"];
        };
        listAlertsForEnterprise: {
            parameters: RequestParameters & Endpoints["GET /enterprises/{enterprise}/secret-scanning/alerts"]["parameters"];
            response: Endpoints["GET /enterprises/{enterprise}/secret-scanning/alerts"]["response"];
        };
        listAlertsForOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/secret-scanning/alerts"]["parameters"];
            response: Endpoints["GET /orgs/{org}/secret-scanning/alerts"]["response"];
        };
        listAlertsForRepo: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts"]["response"];
        };
        listLocationsForAlert: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations"]["response"];
        };
        updateAlert: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"]["response"];
        };
    };
    securityAdvisories: {
        createFork: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/forks"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/forks"]["response"];
        };
        createPrivateVulnerabilityReport: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/security-advisories/reports"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/security-advisories/reports"]["response"];
        };
        createRepositoryAdvisory: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/security-advisories"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/security-advisories"]["response"];
        };
        createRepositoryAdvisoryCveRequest: {
            parameters: RequestParameters & Endpoints["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/cve"]["parameters"];
            response: Endpoints["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/cve"]["response"];
        };
        getGlobalAdvisory: {
            parameters: RequestParameters & Endpoints["GET /advisories/{ghsa_id}"]["parameters"];
            response: Endpoints["GET /advisories/{ghsa_id}"]["response"];
        };
        getRepositoryAdvisory: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/security-advisories/{ghsa_id}"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/security-advisories/{ghsa_id}"]["response"];
        };
        listGlobalAdvisories: {
            parameters: RequestParameters & Endpoints["GET /advisories"]["parameters"];
            response: Endpoints["GET /advisories"]["response"];
        };
        listOrgRepositoryAdvisories: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/security-advisories"]["parameters"];
            response: Endpoints["GET /orgs/{org}/security-advisories"]["response"];
        };
        listRepositoryAdvisories: {
            parameters: RequestParameters & Endpoints["GET /repos/{owner}/{repo}/security-advisories"]["parameters"];
            response: Endpoints["GET /repos/{owner}/{repo}/security-advisories"]["response"];
        };
        updateRepositoryAdvisory: {
            parameters: RequestParameters & Endpoints["PATCH /repos/{owner}/{repo}/security-advisories/{ghsa_id}"]["parameters"];
            response: Endpoints["PATCH /repos/{owner}/{repo}/security-advisories/{ghsa_id}"]["response"];
        };
    };
    teams: {
        addOrUpdateMembershipForUserInOrg: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/teams/{team_slug}/memberships/{username}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/teams/{team_slug}/memberships/{username}"]["response"];
        };
        addOrUpdateRepoPermissionsInOrg: {
            parameters: RequestParameters & Endpoints["PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"]["parameters"];
            response: Endpoints["PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"]["response"];
        };
        checkPermissionsForRepoInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"]["response"];
        };
        create: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/teams"]["parameters"];
            response: Endpoints["POST /orgs/{org}/teams"]["response"];
        };
        createDiscussionCommentInOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"]["parameters"];
            response: Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"]["response"];
        };
        createDiscussionInOrg: {
            parameters: RequestParameters & Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions"]["parameters"];
            response: Endpoints["POST /orgs/{org}/teams/{team_slug}/discussions"]["response"];
        };
        deleteDiscussionCommentInOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"]["response"];
        };
        deleteDiscussionInOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"]["response"];
        };
        deleteInOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/teams/{team_slug}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/teams/{team_slug}"]["response"];
        };
        getByName: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}"]["response"];
        };
        getDiscussionCommentInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"]["response"];
        };
        getDiscussionInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"]["response"];
        };
        getMembershipForUserInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/memberships/{username}"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/memberships/{username}"]["response"];
        };
        list: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams"]["response"];
        };
        listChildInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/teams"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/teams"]["response"];
        };
        listDiscussionCommentsInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"]["response"];
        };
        listDiscussionsInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions"]["response"];
        };
        listForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/teams"]["parameters"];
            response: Endpoints["GET /user/teams"]["response"];
        };
        listMembersInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/members"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/members"]["response"];
        };
        listPendingInvitationsInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/invitations"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/invitations"]["response"];
        };
        listReposInOrg: {
            parameters: RequestParameters & Endpoints["GET /orgs/{org}/teams/{team_slug}/repos"]["parameters"];
            response: Endpoints["GET /orgs/{org}/teams/{team_slug}/repos"]["response"];
        };
        removeMembershipForUserInOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}"]["response"];
        };
        removeRepoInOrg: {
            parameters: RequestParameters & Endpoints["DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"]["parameters"];
            response: Endpoints["DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"]["response"];
        };
        updateDiscussionCommentInOrg: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"]["response"];
        };
        updateDiscussionInOrg: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"]["response"];
        };
        updateInOrg: {
            parameters: RequestParameters & Endpoints["PATCH /orgs/{org}/teams/{team_slug}"]["parameters"];
            response: Endpoints["PATCH /orgs/{org}/teams/{team_slug}"]["response"];
        };
    };
    users: {
        addEmailForAuthenticated: {
            parameters: RequestParameters & Endpoints["POST /user/emails"]["parameters"];
            response: Endpoints["POST /user/emails"]["response"];
        };
        addEmailForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/emails"]["parameters"];
            response: Endpoints["POST /user/emails"]["response"];
        };
        addSocialAccountForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/social_accounts"]["parameters"];
            response: Endpoints["POST /user/social_accounts"]["response"];
        };
        block: {
            parameters: RequestParameters & Endpoints["PUT /user/blocks/{username}"]["parameters"];
            response: Endpoints["PUT /user/blocks/{username}"]["response"];
        };
        checkBlocked: {
            parameters: RequestParameters & Endpoints["GET /user/blocks/{username}"]["parameters"];
            response: Endpoints["GET /user/blocks/{username}"]["response"];
        };
        checkFollowingForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/following/{target_user}"]["parameters"];
            response: Endpoints["GET /users/{username}/following/{target_user}"]["response"];
        };
        checkPersonIsFollowedByAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/following/{username}"]["parameters"];
            response: Endpoints["GET /user/following/{username}"]["response"];
        };
        createGpgKeyForAuthenticated: {
            parameters: RequestParameters & Endpoints["POST /user/gpg_keys"]["parameters"];
            response: Endpoints["POST /user/gpg_keys"]["response"];
        };
        createGpgKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/gpg_keys"]["parameters"];
            response: Endpoints["POST /user/gpg_keys"]["response"];
        };
        createPublicSshKeyForAuthenticated: {
            parameters: RequestParameters & Endpoints["POST /user/keys"]["parameters"];
            response: Endpoints["POST /user/keys"]["response"];
        };
        createPublicSshKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/keys"]["parameters"];
            response: Endpoints["POST /user/keys"]["response"];
        };
        createSshSigningKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["POST /user/ssh_signing_keys"]["parameters"];
            response: Endpoints["POST /user/ssh_signing_keys"]["response"];
        };
        deleteEmailForAuthenticated: {
            parameters: RequestParameters & Endpoints["DELETE /user/emails"]["parameters"];
            response: Endpoints["DELETE /user/emails"]["response"];
        };
        deleteEmailForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/emails"]["parameters"];
            response: Endpoints["DELETE /user/emails"]["response"];
        };
        deleteGpgKeyForAuthenticated: {
            parameters: RequestParameters & Endpoints["DELETE /user/gpg_keys/{gpg_key_id}"]["parameters"];
            response: Endpoints["DELETE /user/gpg_keys/{gpg_key_id}"]["response"];
        };
        deleteGpgKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/gpg_keys/{gpg_key_id}"]["parameters"];
            response: Endpoints["DELETE /user/gpg_keys/{gpg_key_id}"]["response"];
        };
        deletePublicSshKeyForAuthenticated: {
            parameters: RequestParameters & Endpoints["DELETE /user/keys/{key_id}"]["parameters"];
            response: Endpoints["DELETE /user/keys/{key_id}"]["response"];
        };
        deletePublicSshKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/keys/{key_id}"]["parameters"];
            response: Endpoints["DELETE /user/keys/{key_id}"]["response"];
        };
        deleteSocialAccountForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/social_accounts"]["parameters"];
            response: Endpoints["DELETE /user/social_accounts"]["response"];
        };
        deleteSshSigningKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["DELETE /user/ssh_signing_keys/{ssh_signing_key_id}"]["parameters"];
            response: Endpoints["DELETE /user/ssh_signing_keys/{ssh_signing_key_id}"]["response"];
        };
        follow: {
            parameters: RequestParameters & Endpoints["PUT /user/following/{username}"]["parameters"];
            response: Endpoints["PUT /user/following/{username}"]["response"];
        };
        getAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user"]["parameters"];
            response: Endpoints["GET /user"]["response"];
        };
        getById: {
            parameters: RequestParameters & Endpoints["GET /user/{account_id}"]["parameters"];
            response: Endpoints["GET /user/{account_id}"]["response"];
        };
        getByUsername: {
            parameters: RequestParameters & Endpoints["GET /users/{username}"]["parameters"];
            response: Endpoints["GET /users/{username}"]["response"];
        };
        getContextForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/hovercard"]["parameters"];
            response: Endpoints["GET /users/{username}/hovercard"]["response"];
        };
        getGpgKeyForAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/gpg_keys/{gpg_key_id}"]["parameters"];
            response: Endpoints["GET /user/gpg_keys/{gpg_key_id}"]["response"];
        };
        getGpgKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/gpg_keys/{gpg_key_id}"]["parameters"];
            response: Endpoints["GET /user/gpg_keys/{gpg_key_id}"]["response"];
        };
        getPublicSshKeyForAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/keys/{key_id}"]["parameters"];
            response: Endpoints["GET /user/keys/{key_id}"]["response"];
        };
        getPublicSshKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/keys/{key_id}"]["parameters"];
            response: Endpoints["GET /user/keys/{key_id}"]["response"];
        };
        getSshSigningKeyForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/ssh_signing_keys/{ssh_signing_key_id}"]["parameters"];
            response: Endpoints["GET /user/ssh_signing_keys/{ssh_signing_key_id}"]["response"];
        };
        list: {
            parameters: RequestParameters & Endpoints["GET /users"]["parameters"];
            response: Endpoints["GET /users"]["response"];
        };
        listAttestations: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/attestations/{subject_digest}"]["parameters"];
            response: Endpoints["GET /users/{username}/attestations/{subject_digest}"]["response"];
        };
        listBlockedByAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/blocks"]["parameters"];
            response: Endpoints["GET /user/blocks"]["response"];
        };
        listBlockedByAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/blocks"]["parameters"];
            response: Endpoints["GET /user/blocks"]["response"];
        };
        listEmailsForAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/emails"]["parameters"];
            response: Endpoints["GET /user/emails"]["response"];
        };
        listEmailsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/emails"]["parameters"];
            response: Endpoints["GET /user/emails"]["response"];
        };
        listFollowedByAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/following"]["parameters"];
            response: Endpoints["GET /user/following"]["response"];
        };
        listFollowedByAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/following"]["parameters"];
            response: Endpoints["GET /user/following"]["response"];
        };
        listFollowersForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/followers"]["parameters"];
            response: Endpoints["GET /user/followers"]["response"];
        };
        listFollowersForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/followers"]["parameters"];
            response: Endpoints["GET /users/{username}/followers"]["response"];
        };
        listFollowingForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/following"]["parameters"];
            response: Endpoints["GET /users/{username}/following"]["response"];
        };
        listGpgKeysForAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/gpg_keys"]["parameters"];
            response: Endpoints["GET /user/gpg_keys"]["response"];
        };
        listGpgKeysForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/gpg_keys"]["parameters"];
            response: Endpoints["GET /user/gpg_keys"]["response"];
        };
        listGpgKeysForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/gpg_keys"]["parameters"];
            response: Endpoints["GET /users/{username}/gpg_keys"]["response"];
        };
        listPublicEmailsForAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/public_emails"]["parameters"];
            response: Endpoints["GET /user/public_emails"]["response"];
        };
        listPublicEmailsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/public_emails"]["parameters"];
            response: Endpoints["GET /user/public_emails"]["response"];
        };
        listPublicKeysForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/keys"]["parameters"];
            response: Endpoints["GET /users/{username}/keys"]["response"];
        };
        listPublicSshKeysForAuthenticated: {
            parameters: RequestParameters & Endpoints["GET /user/keys"]["parameters"];
            response: Endpoints["GET /user/keys"]["response"];
        };
        listPublicSshKeysForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/keys"]["parameters"];
            response: Endpoints["GET /user/keys"]["response"];
        };
        listSocialAccountsForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/social_accounts"]["parameters"];
            response: Endpoints["GET /user/social_accounts"]["response"];
        };
        listSocialAccountsForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/social_accounts"]["parameters"];
            response: Endpoints["GET /users/{username}/social_accounts"]["response"];
        };
        listSshSigningKeysForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["GET /user/ssh_signing_keys"]["parameters"];
            response: Endpoints["GET /user/ssh_signing_keys"]["response"];
        };
        listSshSigningKeysForUser: {
            parameters: RequestParameters & Endpoints["GET /users/{username}/ssh_signing_keys"]["parameters"];
            response: Endpoints["GET /users/{username}/ssh_signing_keys"]["response"];
        };
        setPrimaryEmailVisibilityForAuthenticated: {
            parameters: RequestParameters & Endpoints["PATCH /user/email/visibility"]["parameters"];
            response: Endpoints["PATCH /user/email/visibility"]["response"];
        };
        setPrimaryEmailVisibilityForAuthenticatedUser: {
            parameters: RequestParameters & Endpoints["PATCH /user/email/visibility"]["parameters"];
            response: Endpoints["PATCH /user/email/visibility"]["response"];
        };
        unblock: {
            parameters: RequestParameters & Endpoints["DELETE /user/blocks/{username}"]["parameters"];
            response: Endpoints["DELETE /user/blocks/{username}"]["response"];
        };
        unfollow: {
            parameters: RequestParameters & Endpoints["DELETE /user/following/{username}"]["parameters"];
            response: Endpoints["DELETE /user/following/{username}"]["response"];
        };
        updateAuthenticated: {
            parameters: RequestParameters & Endpoints["PATCH /user"]["parameters"];
            response: Endpoints["PATCH /user"]["response"];
        };
    };
};
