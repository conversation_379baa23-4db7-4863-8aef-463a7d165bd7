{"app": {"title": "GitHub Monitor", "description": "GitHub Project Update Monitor Desktop Application"}, "menu": {"home": "Home", "statistics": "Statistics", "settings": "Settings"}, "category": {"title": "Category Management", "add": "Add Category", "edit": "Edit Category", "delete": "Delete Category", "name": "Category Name", "weight": "Weight"}, "author": {"title": "Author Management", "add": "Add Author", "edit": "Edit Author", "delete": "Delete Author", "username": "Username", "category": "Category"}, "project": {"title": "Project Management", "add": "Add Project", "edit": "Edit Project", "delete": "Delete Project", "name": "Project Name", "url": "Project URL", "category": "Category"}, "search": {"placeholder": "Search projects or authors...", "filter": "Filter", "sort": "Sort"}, "common": {"save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "success": "Operation successful", "error": "Operation failed"}}