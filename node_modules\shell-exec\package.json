{"name": "shell-exec", "version": "1.0.2", "description": "A tiny cross-platform promise based wrapper around child_process.spawn.", "license": "MIT", "repository": {"url": "https://github.com/tiaanduplessis/shell-exec", "type": "git"}, "homepage": "https://github.com/tiaanduplessis/shell-exec", "bugs": "https://github.com/tiaanduplessis/shell-exec", "main": "index.js", "files": ["index.js"], "scripts": {"test": "jest", "lint": "standard --fix index.js", "start": "yarn test", "pretest": "yarn lint", "precommit": "yarn test"}, "author": "<PERSON><PERSON><PERSON>", "devDependencies": {"husky": "^0.14.3", "jest": "^22.4.3", "standard": "^12.0.0"}}