{"version": 3, "sources": ["../dist-src/index.js", "../dist-src/defaults.js", "../dist-src/version.js", "../dist-src/fetch-wrapper.js", "../dist-src/is-plain-object.js", "../dist-src/with-defaults.js"], "sourcesContent": ["import { endpoint } from \"@octokit/endpoint\";\nimport defaults from \"./defaults.js\";\nimport withDefaults from \"./with-defaults.js\";\nconst request = withDefaults(endpoint, defaults);\nexport {\n  request\n};\n", "import { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version.js\";\nvar defaults_default = {\n  headers: {\n    \"user-agent\": `octokit-request.js/${VERSION} ${getUserAgent()}`\n  }\n};\nexport {\n  defaults_default as default\n};\n", "const VERSION = \"10.0.3\";\nexport {\n  VERSION\n};\n", "import { safeParse } from \"fast-content-type-parse\";\nimport { isPlainObject } from \"./is-plain-object.js\";\nimport { RequestError } from \"@octokit/request-error\";\nasync function fetchWrapper(requestOptions) {\n  const fetch = requestOptions.request?.fetch || globalThis.fetch;\n  if (!fetch) {\n    throw new Error(\n      \"fetch is not set. Please pass a fetch implementation as new Octokit({ request: { fetch }}). Learn more at https://github.com/octokit/octokit.js/#fetch-missing\"\n    );\n  }\n  const log = requestOptions.request?.log || console;\n  const parseSuccessResponseBody = requestOptions.request?.parseSuccessResponseBody !== false;\n  const body = isPlainObject(requestOptions.body) || Array.isArray(requestOptions.body) ? JSON.stringify(requestOptions.body) : requestOptions.body;\n  const requestHeaders = Object.fromEntries(\n    Object.entries(requestOptions.headers).map(([name, value]) => [\n      name,\n      String(value)\n    ])\n  );\n  let fetchResponse;\n  try {\n    fetchResponse = await fetch(requestOptions.url, {\n      method: requestOptions.method,\n      body,\n      redirect: requestOptions.request?.redirect,\n      headers: requestHeaders,\n      signal: requestOptions.request?.signal,\n      // duplex must be set if request.body is ReadableStream or Async Iterables.\n      // See https://fetch.spec.whatwg.org/#dom-requestinit-duplex.\n      ...requestOptions.body && { duplex: \"half\" }\n    });\n  } catch (error) {\n    let message = \"Unknown Error\";\n    if (error instanceof Error) {\n      if (error.name === \"AbortError\") {\n        error.status = 500;\n        throw error;\n      }\n      message = error.message;\n      if (error.name === \"TypeError\" && \"cause\" in error) {\n        if (error.cause instanceof Error) {\n          message = error.cause.message;\n        } else if (typeof error.cause === \"string\") {\n          message = error.cause;\n        }\n      }\n    }\n    const requestError = new RequestError(message, 500, {\n      request: requestOptions\n    });\n    requestError.cause = error;\n    throw requestError;\n  }\n  const status = fetchResponse.status;\n  const url = fetchResponse.url;\n  const responseHeaders = {};\n  for (const [key, value] of fetchResponse.headers) {\n    responseHeaders[key] = value;\n  }\n  const octokitResponse = {\n    url,\n    status,\n    headers: responseHeaders,\n    data: \"\"\n  };\n  if (\"deprecation\" in responseHeaders) {\n    const matches = responseHeaders.link && responseHeaders.link.match(/<([^<>]+)>; rel=\"deprecation\"/);\n    const deprecationLink = matches && matches.pop();\n    log.warn(\n      `[@octokit/request] \"${requestOptions.method} ${requestOptions.url}\" is deprecated. It is scheduled to be removed on ${responseHeaders.sunset}${deprecationLink ? `. See ${deprecationLink}` : \"\"}`\n    );\n  }\n  if (status === 204 || status === 205) {\n    return octokitResponse;\n  }\n  if (requestOptions.method === \"HEAD\") {\n    if (status < 400) {\n      return octokitResponse;\n    }\n    throw new RequestError(fetchResponse.statusText, status, {\n      response: octokitResponse,\n      request: requestOptions\n    });\n  }\n  if (status === 304) {\n    octokitResponse.data = await getResponseData(fetchResponse);\n    throw new RequestError(\"Not modified\", status, {\n      response: octokitResponse,\n      request: requestOptions\n    });\n  }\n  if (status >= 400) {\n    octokitResponse.data = await getResponseData(fetchResponse);\n    throw new RequestError(toErrorMessage(octokitResponse.data), status, {\n      response: octokitResponse,\n      request: requestOptions\n    });\n  }\n  octokitResponse.data = parseSuccessResponseBody ? await getResponseData(fetchResponse) : fetchResponse.body;\n  return octokitResponse;\n}\nasync function getResponseData(response) {\n  const contentType = response.headers.get(\"content-type\");\n  if (!contentType) {\n    return response.text().catch(() => \"\");\n  }\n  const mimetype = safeParse(contentType);\n  if (isJSONResponse(mimetype)) {\n    let text = \"\";\n    try {\n      text = await response.text();\n      return JSON.parse(text);\n    } catch (err) {\n      return text;\n    }\n  } else if (mimetype.type.startsWith(\"text/\") || mimetype.parameters.charset?.toLowerCase() === \"utf-8\") {\n    return response.text().catch(() => \"\");\n  } else {\n    return response.arrayBuffer().catch(() => new ArrayBuffer(0));\n  }\n}\nfunction isJSONResponse(mimetype) {\n  return mimetype.type === \"application/json\" || mimetype.type === \"application/scim+json\";\n}\nfunction toErrorMessage(data) {\n  if (typeof data === \"string\") {\n    return data;\n  }\n  if (data instanceof ArrayBuffer) {\n    return \"Unknown error\";\n  }\n  if (\"message\" in data) {\n    const suffix = \"documentation_url\" in data ? ` - ${data.documentation_url}` : \"\";\n    return Array.isArray(data.errors) ? `${data.message}: ${data.errors.map((v) => JSON.stringify(v)).join(\", \")}${suffix}` : `${data.message}${suffix}`;\n  }\n  return `Unknown error: ${JSON.stringify(data)}`;\n}\nexport {\n  fetchWrapper as default\n};\n", "function isPlainObject(value) {\n  if (typeof value !== \"object\" || value === null) return false;\n  if (Object.prototype.toString.call(value) !== \"[object Object]\") return false;\n  const proto = Object.getPrototypeOf(value);\n  if (proto === null) return true;\n  const Ctor = Object.prototype.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  return typeof Ctor === \"function\" && Ctor instanceof Ctor && Function.prototype.call(Ctor) === Function.prototype.call(value);\n}\nexport {\n  isPlainObject\n};\n", "import fetchWrapper from \"./fetch-wrapper.js\";\nfunction withDefaults(oldEndpoint, newDefaults) {\n  const endpoint = oldEndpoint.defaults(newDefaults);\n  const newApi = function(route, parameters) {\n    const endpointOptions = endpoint.merge(route, parameters);\n    if (!endpointOptions.request || !endpointOptions.request.hook) {\n      return fetchWrapper(endpoint.parse(endpointOptions));\n    }\n    const request = (route2, parameters2) => {\n      return fetchWrapper(\n        endpoint.parse(endpoint.merge(route2, parameters2))\n      );\n    };\n    Object.assign(request, {\n      endpoint,\n      defaults: withDefaults.bind(null, endpoint)\n    });\n    return endpointOptions.request.hook(request, endpointOptions);\n  };\n  return Object.assign(newApi, {\n    endpoint,\n    defaults: withDefaults.bind(null, endpoint)\n  });\n}\nexport {\n  withDefaults as default\n};\n"], "mappings": ";AAAA,SAAS,gBAAgB;;;ACAzB,SAAS,oBAAoB;;;ACA7B,IAAM,UAAU;;;ADEhB,IAAI,mBAAmB;AAAA,EACrB,SAAS;AAAA,IACP,cAAc,sBAAsB,OAAO,IAAI,aAAa,CAAC;AAAA,EAC/D;AACF;;;AENA,SAAS,iBAAiB;;;ACA1B,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AACxD,MAAI,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,kBAAmB,QAAO;AACxE,QAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,MAAI,UAAU,KAAM,QAAO;AAC3B,QAAM,OAAO,OAAO,UAAU,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AACjF,SAAO,OAAO,SAAS,cAAc,gBAAgB,QAAQ,SAAS,UAAU,KAAK,IAAI,MAAM,SAAS,UAAU,KAAK,KAAK;AAC9H;;;ADLA,SAAS,oBAAoB;AAC7B,eAAe,aAAa,gBAAgB;AAC1C,QAAM,QAAQ,eAAe,SAAS,SAAS,WAAW;AAC1D,MAAI,CAAC,OAAO;AACV,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,QAAM,MAAM,eAAe,SAAS,OAAO;AAC3C,QAAM,2BAA2B,eAAe,SAAS,6BAA6B;AACtF,QAAM,OAAO,cAAc,eAAe,IAAI,KAAK,MAAM,QAAQ,eAAe,IAAI,IAAI,KAAK,UAAU,eAAe,IAAI,IAAI,eAAe;AAC7I,QAAM,iBAAiB,OAAO;AAAA,IAC5B,OAAO,QAAQ,eAAe,OAAO,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AAAA,MAC5D;AAAA,MACA,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACA,MAAI;AACJ,MAAI;AACF,oBAAgB,MAAM,MAAM,eAAe,KAAK;AAAA,MAC9C,QAAQ,eAAe;AAAA,MACvB;AAAA,MACA,UAAU,eAAe,SAAS;AAAA,MAClC,SAAS;AAAA,MACT,QAAQ,eAAe,SAAS;AAAA;AAAA;AAAA,MAGhC,GAAG,eAAe,QAAQ,EAAE,QAAQ,OAAO;AAAA,IAC7C,CAAC;AAAA,EACH,SAAS,OAAO;AACd,QAAI,UAAU;AACd,QAAI,iBAAiB,OAAO;AAC1B,UAAI,MAAM,SAAS,cAAc;AAC/B,cAAM,SAAS;AACf,cAAM;AAAA,MACR;AACA,gBAAU,MAAM;AAChB,UAAI,MAAM,SAAS,eAAe,WAAW,OAAO;AAClD,YAAI,MAAM,iBAAiB,OAAO;AAChC,oBAAU,MAAM,MAAM;AAAA,QACxB,WAAW,OAAO,MAAM,UAAU,UAAU;AAC1C,oBAAU,MAAM;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,UAAM,eAAe,IAAI,aAAa,SAAS,KAAK;AAAA,MAClD,SAAS;AAAA,IACX,CAAC;AACD,iBAAa,QAAQ;AACrB,UAAM;AAAA,EACR;AACA,QAAM,SAAS,cAAc;AAC7B,QAAM,MAAM,cAAc;AAC1B,QAAM,kBAAkB,CAAC;AACzB,aAAW,CAAC,KAAK,KAAK,KAAK,cAAc,SAAS;AAChD,oBAAgB,GAAG,IAAI;AAAA,EACzB;AACA,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AACA,MAAI,iBAAiB,iBAAiB;AACpC,UAAM,UAAU,gBAAgB,QAAQ,gBAAgB,KAAK,MAAM,+BAA+B;AAClG,UAAM,kBAAkB,WAAW,QAAQ,IAAI;AAC/C,QAAI;AAAA,MACF,uBAAuB,eAAe,MAAM,IAAI,eAAe,GAAG,qDAAqD,gBAAgB,MAAM,GAAG,kBAAkB,SAAS,eAAe,KAAK,EAAE;AAAA,IACnM;AAAA,EACF;AACA,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,WAAO;AAAA,EACT;AACA,MAAI,eAAe,WAAW,QAAQ;AACpC,QAAI,SAAS,KAAK;AAChB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,aAAa,cAAc,YAAY,QAAQ;AAAA,MACvD,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,WAAW,KAAK;AAClB,oBAAgB,OAAO,MAAM,gBAAgB,aAAa;AAC1D,UAAM,IAAI,aAAa,gBAAgB,QAAQ;AAAA,MAC7C,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,UAAU,KAAK;AACjB,oBAAgB,OAAO,MAAM,gBAAgB,aAAa;AAC1D,UAAM,IAAI,aAAa,eAAe,gBAAgB,IAAI,GAAG,QAAQ;AAAA,MACnE,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,kBAAgB,OAAO,2BAA2B,MAAM,gBAAgB,aAAa,IAAI,cAAc;AACvG,SAAO;AACT;AACA,eAAe,gBAAgB,UAAU;AACvC,QAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,MAAI,CAAC,aAAa;AAChB,WAAO,SAAS,KAAK,EAAE,MAAM,MAAM,EAAE;AAAA,EACvC;AACA,QAAM,WAAW,UAAU,WAAW;AACtC,MAAI,eAAe,QAAQ,GAAG;AAC5B,QAAI,OAAO;AACX,QAAI;AACF,aAAO,MAAM,SAAS,KAAK;AAC3B,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB,SAAS,KAAK;AACZ,aAAO;AAAA,IACT;AAAA,EACF,WAAW,SAAS,KAAK,WAAW,OAAO,KAAK,SAAS,WAAW,SAAS,YAAY,MAAM,SAAS;AACtG,WAAO,SAAS,KAAK,EAAE,MAAM,MAAM,EAAE;AAAA,EACvC,OAAO;AACL,WAAO,SAAS,YAAY,EAAE,MAAM,MAAM,IAAI,YAAY,CAAC,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,eAAe,UAAU;AAChC,SAAO,SAAS,SAAS,sBAAsB,SAAS,SAAS;AACnE;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,aAAa;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,aAAa,MAAM;AACrB,UAAM,SAAS,uBAAuB,OAAO,MAAM,KAAK,iBAAiB,KAAK;AAC9E,WAAO,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG,KAAK,OAAO,KAAK,KAAK,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,OAAO,GAAG,MAAM;AAAA,EACpJ;AACA,SAAO,kBAAkB,KAAK,UAAU,IAAI,CAAC;AAC/C;;;AEvIA,SAAS,aAAa,aAAa,aAAa;AAC9C,QAAMA,YAAW,YAAY,SAAS,WAAW;AACjD,QAAM,SAAS,SAAS,OAAO,YAAY;AACzC,UAAM,kBAAkBA,UAAS,MAAM,OAAO,UAAU;AACxD,QAAI,CAAC,gBAAgB,WAAW,CAAC,gBAAgB,QAAQ,MAAM;AAC7D,aAAO,aAAaA,UAAS,MAAM,eAAe,CAAC;AAAA,IACrD;AACA,UAAMC,WAAU,CAAC,QAAQ,gBAAgB;AACvC,aAAO;AAAA,QACLD,UAAS,MAAMA,UAAS,MAAM,QAAQ,WAAW,CAAC;AAAA,MACpD;AAAA,IACF;AACA,WAAO,OAAOC,UAAS;AAAA,MACrB,UAAAD;AAAA,MACA,UAAU,aAAa,KAAK,MAAMA,SAAQ;AAAA,IAC5C,CAAC;AACD,WAAO,gBAAgB,QAAQ,KAAKC,UAAS,eAAe;AAAA,EAC9D;AACA,SAAO,OAAO,OAAO,QAAQ;AAAA,IAC3B,UAAAD;AAAA,IACA,UAAU,aAAa,KAAK,MAAMA,SAAQ;AAAA,EAC5C,CAAC;AACH;;;ALpBA,IAAM,UAAU,aAAa,UAAU,gBAAQ;", "names": ["endpoint", "request"]}