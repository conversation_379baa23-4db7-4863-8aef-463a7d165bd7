import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ShopFilledSvg from "@ant-design/icons-svg/es/asn/ShopFilled";
import AntdIcon from "../components/AntdIcon";
var ShopFilled = function ShopFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ShopFilledSvg
  }));
};

/**![shop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MiAyNzIuMVYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMTc0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMjguMWMtMTYuNyAxLTMwIDE0LjktMzAgMzEuOXYxMzEuN2ExNzcgMTc3IDAgMDAxNC40IDcwLjRjNC4zIDEwLjIgOS42IDE5LjggMTUuNiAyOC45djM0NWMwIDE3LjYgMTQuMyAzMiAzMiAzMmgyNzRWNzM2aDEyOHYxNzZoMjc0YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjUzNWExNzUgMTc1IDAgMDAxNS42LTI4LjljOS41LTIyLjMgMTQuNC00NiAxNC40LTcwLjRWMzA0YzAtMTctMTMuMy0zMC45LTMwLTMxLjl6bS03MiA1NjhINjQwVjcwNGMwLTE3LjctMTQuMy0zMi0zMi0zMkg0MTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjEzNi4xSDIxNFY1OTcuOWMyLjkgMS40IDUuOSAyLjggOSA0IDIyLjMgOS40IDQ2IDE0LjEgNzAuNCAxNC4xczQ4LTQuNyA3MC40LTE0LjFjMTMuOC01LjggMjYuOC0xMy4yIDM4LjctMjIuMS4yLS4xLjQtLjEuNiAwYTE4MC40IDE4MC40IDAgMDAzOC43IDIyLjFjMjIuMyA5LjQgNDYgMTQuMSA3MC40IDE0LjEgMjQuNCAwIDQ4LTQuNyA3MC40LTE0LjEgMTMuOC01LjggMjYuOC0xMy4yIDM4LjctMjIuMS4yLS4xLjQtLjEuNiAwYTE4MC40IDE4MC40IDAgMDAzOC43IDIyLjFjMjIuMyA5LjQgNDYgMTQuMSA3MC40IDE0LjEgMjQuNCAwIDQ4LTQuNyA3MC40LTE0LjEgMy0xLjMgNi0yLjYgOS00djI0Mi4yem0wLTU2OC4xSDIxNHYtODhoNTk2djg4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ShopFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShopFilled';
}
export default RefIcon;