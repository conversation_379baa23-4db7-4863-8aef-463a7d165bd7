{"name": "fast-content-type-parse", "version": "3.0.0", "description": "Parse HTTP Content-Type header according to RFC 7231", "main": "index.js", "type": "commonjs", "types": "./types/index.d.ts", "scripts": {"benchmark": "node benchmarks/simple.js && node benchmarks/simple-ows.js && node benchmarks/with-param.js && node benchmarks/with-param-quoted.js", "lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "c8 --100 node --test"}, "keywords": ["content-type", "rfc7231"], "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [{"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-content-type-parse.git"}, "bugs": {"url": "https://github.com/fastify/fast-content-type-parse/issues"}, "homepage": "https://github.com/fastify/fast-content-type-parse#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "benchmark": "^2.1.4", "busboy": "^1.6.0", "c8": "^10.1.3", "content-type": "^1.0.4", "eslint": "^9.17.0", "neostandard": "^0.12.0", "tsd": "^0.31.0"}, "pre-commit": ["lint", "test"]}