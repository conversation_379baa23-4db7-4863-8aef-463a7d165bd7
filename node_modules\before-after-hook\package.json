{"name": "before-after-hook", "type": "module", "version": "4.0.0", "description": "asynchronous before/error/after hooks for internal functionality", "exports": "./index.js", "types": "./index.d.ts", "files": ["index.js", "index.d.ts", "lib"], "scripts": {"test": "npm run test:code && npm run test:tsc && npm run test:tsd && npm run lint", "test:code": "c8 --100 --clean node --test", "test:deno": "deno test --no-check", "test:tsc": "tsc --allowJs --noEmit --esModuleInterop --skipLibCheck --lib es2020 index.js", "test:tsd": "tsd", "lint": "prettier --check \"*.{js,json,ts,md}\" \"test//*.{js,json,ts,md}\" \".github/**/*.yml\"", "lint:fix": "prettier --write \"*.{js,json,ts,md}\" \"test//*.{js,json,ts,md}\" \".github/**/*.yml\"", "postcoverage": "open-cli coverage/index.html"}, "repository": "github:gr2m/before-after-hook", "keywords": ["hook", "hooks", "api"], "author": "<PERSON>", "license": "Apache-2.0", "devDependencies": {"@types/node": "^22.15.17", "c8": "^10.1.3", "prettier": "^2.0.0", "tsd": "^0.24.1", "typescript": "^4.8.4"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "renovate": {"extends": ["github>gr2m/.github"]}}