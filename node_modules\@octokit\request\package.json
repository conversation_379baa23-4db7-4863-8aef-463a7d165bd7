{"name": "@octokit/request", "version": "10.0.3", "type": "module", "publishConfig": {"access": "public", "provenance": true}, "description": "Send parameterized requests to GitHub's APIs with sensible defaults in browsers and Node", "repository": "github:octokit/request.js", "keywords": ["octokit", "github", "api", "request"], "author": "<PERSON> (https://github.com/gr2m)", "license": "MIT", "dependencies": {"@octokit/endpoint": "^11.0.0", "@octokit/request-error": "^7.0.0", "@octokit/types": "^14.0.0", "fast-content-type-parse": "^3.0.0", "universal-user-agent": "^7.0.2"}, "devDependencies": {"@octokit/auth-app": "^8.0.0", "@octokit/tsconfig": "^4.0.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^3.0.0", "esbuild": "^0.25.0", "fetch-mock": "^12.0.0", "glob": "^11.0.0", "prettier": "3.5.3", "semantic-release-plugin-update-version-in-files": "^2.0.0", "typescript": "^5.0.0", "undici": "^7.0.0", "vitest": "^3.0.0"}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-bundle/index.js", "default": "./dist-bundle/index.js"}}, "sideEffects": false}