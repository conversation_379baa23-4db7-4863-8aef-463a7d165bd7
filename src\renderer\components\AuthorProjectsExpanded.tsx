/**
 * 作者项目展开组件
 */

import React, { useState, useEffect } from 'react';
import { Card, List, Button, Tag, Space, Typography, Divider, Collapse } from 'antd';
import { LinkOutlined, CheckOutlined, StarOutlined, EyeOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface ProjectItem {
  id: number;
  name: string;
  full_name: string;
  html_url: string;
  description: string;
  stargazers_count: number;
  watchers_count: number;
  updated_at: string;
  created_at: string;
  language: string;
  private: boolean;
}

interface AuthorProjectsExpandedProps {
  authorId: string;
  projects: ProjectItem[];
  onMarkAsRead: (projectId: string) => void;
}

const AuthorProjectsExpanded: React.FC<AuthorProjectsExpandedProps> = ({
  authorId,
  projects,
  onMarkAsRead,
}) => {
  const [readProjects, setReadProjects] = useState<Set<string>>(new Set());
  const [newProjects, setNewProjects] = useState<ProjectItem[]>([]);
  const [unreadUpdates, setUnreadUpdates] = useState<ProjectItem[]>([]);
  const [allProjects, setAllProjects] = useState<ProjectItem[]>([]);

  useEffect(() => {
    // 从localStorage加载已读状态
    const savedReadProjects = localStorage.getItem(`read-author-projects-${authorId}`);
    if (savedReadProjects) {
      setReadProjects(new Set(JSON.parse(savedReadProjects)));
    }
  }, [authorId]);

  useEffect(() => {
    // 分类项目
    categorizeProjects();

    // 加载作者的新项目和更新项目
    loadAuthorProjectUpdates();
  }, [projects, authorId, readProjects]);

  const loadAuthorProjectUpdates = async () => {
    try {
      if (window.electronAPI) {
        // 获取新项目
        const newProjectsResponse = await window.electronAPI.github.getAuthorNewProjects(authorId);
        if (newProjectsResponse.success) {
          const newProjectsData = newProjectsResponse.data || [];
          // 过滤掉已阅的项目
          const unreadNewProjects = newProjectsData.filter((project: ProjectItem) =>
            !readProjects.has(project.id.toString())
          );
          setNewProjects(unreadNewProjects);
        }

        // 获取更新项目
        const updatedProjectsResponse = await window.electronAPI.github.getAuthorUpdatedProjects(authorId);
        if (updatedProjectsResponse.success) {
          const updatedProjectsData = updatedProjectsResponse.data || [];
          // 过滤掉已阅的项目
          const unreadUpdatedProjects = updatedProjectsData.filter((project: ProjectItem) =>
            !readProjects.has(project.id.toString())
          );
          setUnreadUpdates(unreadUpdatedProjects);
        }
      }
    } catch (error) {
      console.error('Failed to load author project updates:', error);
    }
  };

  const categorizeProjects = () => {
    setAllProjects([...projects]);
  };

  const handleMarkAsRead = (project: ProjectItem) => {
    const newReadProjects = new Set(readProjects);
    newReadProjects.add(project.id.toString());
    setReadProjects(newReadProjects);

    // 保存到localStorage
    localStorage.setItem(`read-author-projects-${authorId}`, JSON.stringify([...newReadProjects]));

    // 重新分类项目 - 从新项目和未读更新中移除已阅项目
    setNewProjects(prev => prev.filter(p => p.id !== project.id));
    setUnreadUpdates(prev => prev.filter(p => p.id !== project.id));

    onMarkAsRead(project.id.toString());
  };

  // 一键已阅所有项目
  const handleMarkAllAsRead = () => {
    const allProjectIds = [...newProjects, ...unreadUpdates, ...allProjects].map(p => p.id.toString());
    const newReadProjects = new Set([...readProjects, ...allProjectIds]);
    setReadProjects(newReadProjects);

    // 保存到localStorage
    localStorage.setItem(`read-author-projects-${authorId}`, JSON.stringify([...newReadProjects]));

    // 清空新项目和未读更新列表
    setNewProjects([]);
    setUnreadUpdates([]);

    // 通知所有项目已标记为已读
    allProjectIds.forEach(id => onMarkAsRead(id));
  };

  const handleOpenProject = (project: ProjectItem) => {
    // 标记为已读
    handleMarkAsRead(project);

    // 使用系统默认浏览器打开
    if (window.electronAPI) {
      window.electronAPI.system.openExternal(project.html_url);
    } else {
      window.open(project.html_url, '_blank');
    }
  };

  const renderProjectItem = (project: ProjectItem, showReadButton: boolean = true) => {
    const isRead = readProjects.has(project.id.toString());

    return (
      <List.Item
        key={project.id}
        actions={[
          <Button
            type="link"
            icon={<LinkOutlined />}
            onClick={() => handleOpenProject(project)}
            size="small"
          >
            打开
          </Button>,
          showReadButton && (
            <Button
              type={isRead ? "default" : "primary"}
              icon={<CheckOutlined />}
              onClick={() => handleMarkAsRead(project)}
              size="small"
              style={{
                backgroundColor: isRead ? '#d9d9d9' : '#faad14',
                borderColor: isRead ? '#d9d9d9' : '#faad14',
                color: isRead ? '#666' : '#fff'
              }}
            >
              {isRead ? '已阅' : '已阅'}
            </Button>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <Space>
              <Text strong>{project.name}</Text>
              {project.private && <Tag color="red">私有</Tag>}
              {project.language && <Tag color="blue">{project.language}</Tag>}
            </Space>
          }
          description={
            <div>
              <Text type="secondary">{project.description || '暂无描述'}</Text>
              <div style={{ marginTop: '8px' }}>
                <Space>
                  <Space size="small">
                    <StarOutlined />
                    <Text type="secondary">{project.stargazers_count}</Text>
                  </Space>
                  <Space size="small">
                    <EyeOutlined />
                    <Text type="secondary">{project.watchers_count}</Text>
                  </Space>
                  <Text type="secondary">
                    更新于: {new Date(project.updated_at).toLocaleString()}
                  </Text>
                </Space>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <Title level={4} style={{ margin: 0 }}>作者项目详情</Title>
        <Button
          type="primary"
          icon={<CheckOutlined />}
          onClick={handleMarkAllAsRead}
          size="small"
          style={{
            backgroundColor: '#52c41a',
            borderColor: '#52c41a',
          }}
        >
          一键已阅所有
        </Button>
      </div>

      <Collapse
        defaultActiveKey={['1', '2', '3']}
        style={{ backgroundColor: 'transparent' }}
      >
        {/* 作者创建的新项目 */}
        <Panel
          header={
            <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
              🆕 作者创建的新项目 ({newProjects.length})
            </span>
          }
          key="1"
        >
          {newProjects.length > 0 ? (
            <List
              size="small"
              dataSource={newProjects}
              renderItem={(project) => renderProjectItem(project, true)}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              {allProjects.length > 0 ? '所有新项目都已查看' : '暂无新项目'}
            </div>
          )}
        </Panel>

        {/* 作者更新未查看 */}
        <Panel
          header={
            <span style={{ color: '#faad14', fontWeight: 'bold' }}>
              🔔 作者更新未查看 ({unreadUpdates.length})
            </span>
          }
          key="2"
        >
          {unreadUpdates.length > 0 ? (
            <List
              size="small"
              dataSource={unreadUpdates}
              renderItem={(project) => renderProjectItem(project, true)}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              {allProjects.length > 0 ? '所有更新都已查看' : '暂无未查看的更新'}
            </div>
          )}
        </Panel>

        {/* 作者所有项目 */}
        <Panel
          header={
            <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
              📁 作者所有项目 ({allProjects.length})
            </span>
          }
          key="3"
        >
          {allProjects.length > 0 ? (
            <List
              size="small"
              dataSource={allProjects}
              renderItem={(project) => renderProjectItem(project, true)}
              pagination={{
                pageSize: 10,
                size: 'small',
                showSizeChanger: false,
              }}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              暂无项目
            </div>
          )}
        </Panel>
      </Collapse>
    </div>
  );
};

export default AuthorProjectsExpanded;
