/**
 * 作者项目展开组件
 */

import React, { useState, useEffect } from 'react';
import { Card, List, Button, Tag, Space, Typography, Divider, Collapse, Select, Spin, Alert } from 'antd';
import { LinkOutlined, CheckOutlined, StarOutlined, EyeOutlined, DownOutlined, UpOutlined, TranslationOutlined, ReloadOutlined, SettingOutlined } from '@ant-design/icons';
import { translationService, SUPPORTED_LANGUAGES } from '../services/translationService';
import { TranslationSettings } from './TranslationSettings';

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface ProjectItem {
  id: number;
  name: string;
  full_name: string;
  html_url: string;
  description: string;
  stargazers_count: number;
  watchers_count: number;
  updated_at: string;
  created_at: string;
  language: string;
  private: boolean;
}

interface AuthorProjectsExpandedProps {
  authorId: string;
  projects: ProjectItem[];
  onMarkAsRead: (projectId: string) => void;
  onUpdateCounts?: (authorId: string, counts: { newCount: number; updateCount: number }) => void;
}

const AuthorProjectsExpanded: React.FC<AuthorProjectsExpandedProps> = ({
  authorId,
  projects,
  onMarkAsRead,
  onUpdateCounts,
}) => {
  const [readProjects, setReadProjects] = useState<Set<string>>(new Set());
  const [newProjects, setNewProjects] = useState<ProjectItem[]>([]);
  const [unreadUpdates, setUnreadUpdates] = useState<ProjectItem[]>([]);
  const [allProjects, setAllProjects] = useState<ProjectItem[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('zh');
  const [translatedDescriptions, setTranslatedDescriptions] = useState<Record<string, string>>({});
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showTranslationSettings, setShowTranslationSettings] = useState<boolean>(false);
  const [allProjectsCurrentPage, setAllProjectsCurrentPage] = useState<number>(1);
  const [allProjectsPageSize, setAllProjectsPageSize] = useState<number>(10);

  useEffect(() => {
    // 从localStorage加载已读状态
    const savedReadProjects = localStorage.getItem(`read-author-projects-${authorId}`);
    if (savedReadProjects) {
      setReadProjects(new Set(JSON.parse(savedReadProjects)));
    }
  }, [authorId]);

  // 当语言切换时清除翻译
  useEffect(() => {
    if (selectedLanguage === 'zh') {
      setTranslatedDescriptions({});
    }
  }, [selectedLanguage]);

  useEffect(() => {
    // 分类项目
    categorizeProjects();

    // 加载作者的新项目和更新项目
    loadAuthorProjectUpdates();
  }, [projects, authorId, readProjects]);

  const loadAuthorProjectUpdates = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (window.electronAPI) {
        // 获取新项目
        const newProjectsResponse = await window.electronAPI.github.getAuthorNewProjects(authorId);
        let unreadNewProjects: ProjectItem[] = [];
        if (newProjectsResponse.success) {
          const newProjectsData = newProjectsResponse.data || [];
          // 过滤掉已阅的项目
          unreadNewProjects = newProjectsData.filter((project: ProjectItem) =>
            !readProjects.has(project.id.toString())
          );
          setNewProjects(unreadNewProjects);
        } else if (newProjectsResponse.error) {
          console.warn('Failed to get new projects:', newProjectsResponse.error);
        }

        // 获取更新项目
        const updatedProjectsResponse = await window.electronAPI.github.getAuthorUpdatedProjects(authorId);
        let unreadUpdatedProjects: ProjectItem[] = [];
        if (updatedProjectsResponse.success) {
          const updatedProjectsData = updatedProjectsResponse.data || [];
          // 过滤掉已阅的项目
          unreadUpdatedProjects = updatedProjectsData.filter((project: ProjectItem) =>
            !readProjects.has(project.id.toString())
          );
          setUnreadUpdates(unreadUpdatedProjects);
        } else if (updatedProjectsResponse.error) {
          console.warn('Failed to get updated projects:', updatedProjectsResponse.error);
        }

        // 通知父组件更新数量
        if (onUpdateCounts) {
          onUpdateCounts(authorId, {
            newCount: unreadNewProjects.length,
            updateCount: unreadUpdatedProjects.length
          });
        }

        // 检查是否有API限制错误
        if (!newProjectsResponse.success && !updatedProjectsResponse.success) {
          const errorMsg = newProjectsResponse.error || updatedProjectsResponse.error || '';
          if (errorMsg.includes('rate limit')) {
            setError('GitHub API 速率限制，请稍后再试');
          } else {
            setError('加载项目数据失败，请稍后重试');
          }
        }
      }
    } catch (error) {
      console.error('Failed to load author project updates:', error);
      setError('加载项目数据时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  const categorizeProjects = () => {
    setAllProjects([...projects]);
  };

  const handleMarkAsRead = (project: ProjectItem) => {
    const newReadProjects = new Set(readProjects);
    newReadProjects.add(project.id.toString());
    setReadProjects(newReadProjects);

    // 保存到localStorage
    localStorage.setItem(`read-author-projects-${authorId}`, JSON.stringify([...newReadProjects]));

    // 重新分类项目 - 从新项目和未读更新中移除已阅项目
    setNewProjects(prev => prev.filter(p => p.id !== project.id));
    setUnreadUpdates(prev => prev.filter(p => p.id !== project.id));

    onMarkAsRead(project.id.toString());
  };

  // 一键已阅所有项目
  const handleMarkAllAsRead = () => {
    const allProjectIds = [...newProjects, ...unreadUpdates, ...allProjects].map(p => p.id.toString());
    const newReadProjects = new Set([...readProjects, ...allProjectIds]);
    setReadProjects(newReadProjects);

    // 保存到localStorage
    localStorage.setItem(`read-author-projects-${authorId}`, JSON.stringify([...newReadProjects]));

    // 清空新项目和未读更新列表
    setNewProjects([]);
    setUnreadUpdates([]);

    // 通知所有项目已标记为已读
    allProjectIds.forEach(id => onMarkAsRead(id));
  };

  // 获取当前页面显示的所有项目（限制数量以提高翻译速度）
  const getCurrentPageProjects = () => {
    const projects: ProjectItem[] = [];

    // 新项目（最多翻译前10个）
    const visibleNewProjects = newProjects.slice(0, 10);
    projects.push(...visibleNewProjects);

    // 更新项目（最多翻译前10个）
    const visibleUnreadUpdates = unreadUpdates.slice(0, 10);
    projects.push(...visibleUnreadUpdates);

    // 所有项目的当前页
    const startIndex = (allProjectsCurrentPage - 1) * allProjectsPageSize;
    const endIndex = startIndex + allProjectsPageSize;
    const visibleAllProjects = allProjects.slice(startIndex, endIndex);
    projects.push(...visibleAllProjects);

    // 去重（避免同一个项目在多个列表中出现）
    const uniqueProjects = projects.filter((project, index, self) =>
      index === self.findIndex(p => p.id === project.id)
    );

    return uniqueProjects;
  };

  // 处理翻译
  const handleTranslate = async () => {
    // 如果当前有翻译内容，清除翻译
    if (Object.keys(translatedDescriptions).length > 0) {
      setTranslatedDescriptions({});
      return;
    }

    setIsTranslating(true);
    try {
      console.log('开始翻译当前页面内容，目标语言:', selectedLanguage);

      // 获取当前页面显示的所有项目
      const currentPageProjects = getCurrentPageProjects();

      console.log('当前页面项目数量:', currentPageProjects.length);
      console.log('当前页面项目详情:', {
        newProjects: Math.min(newProjects.length, 10),
        unreadUpdates: Math.min(unreadUpdates.length, 10),
        allProjectsPage: allProjectsCurrentPage,
        allProjectsOnCurrentPage: Math.min(allProjects.length - (allProjectsCurrentPage - 1) * allProjectsPageSize, allProjectsPageSize)
      });

      if (currentPageProjects.length === 0) {
        console.log('当前页面没有需要翻译的项目');
        return;
      }

      const translationMap: Record<string, string> = {};

      // 逐个翻译项目描述（避免批量翻译可能的问题）
      for (const project of currentPageProjects) {
        if (project.description && project.description.trim()) {
          try {
            console.log(`翻译项目: ${project.name}`);
            const translatedText = await translationService.translateText(project.description, selectedLanguage);
            translationMap[project.id.toString()] = translatedText;
            console.log(`翻译成功: "${project.description.substring(0, 50)}..." -> "${translatedText.substring(0, 50)}..."`);
          } catch (error) {
            console.warn(`翻译项目 ${project.name} 失败:`, error);
            // 翻译失败时保持原文
            translationMap[project.id.toString()] = project.description;
          }
        }
      }

      console.log('翻译完成，成功翻译项目数量:', Object.keys(translationMap).length);
      setTranslatedDescriptions(translationMap);
    } catch (error) {
      console.error('Translation failed:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  // 获取项目描述（翻译后的或原始的）
  const getProjectDescription = (project: ProjectItem): string => {
    // 如果有翻译内容，优先使用翻译内容
    const translatedDesc = translatedDescriptions[project.id.toString()];
    if (translatedDesc) {
      return translatedDesc;
    }
    // 否则返回原始描述
    return project.description || '暂无描述';
  };

  const handleOpenProject = (project: ProjectItem) => {
    // 标记为已读
    handleMarkAsRead(project);

    // 使用系统默认浏览器打开
    if (window.electronAPI) {
      window.electronAPI.system.openExternal(project.html_url);
    } else {
      window.open(project.html_url, '_blank');
    }
  };

  const renderProjectItem = (project: ProjectItem, showReadButton: boolean = true) => {
    const isRead = readProjects.has(project.id.toString());

    return (
      <List.Item
        key={project.id}
        actions={[
          <Button
            type="link"
            icon={<LinkOutlined />}
            onClick={() => handleOpenProject(project)}
            size="small"
          >
            打开
          </Button>,
          showReadButton && (
            <Button
              type={isRead ? "default" : "primary"}
              icon={<CheckOutlined />}
              onClick={() => handleMarkAsRead(project)}
              size="small"
              style={{
                backgroundColor: isRead ? '#d9d9d9' : '#faad14',
                borderColor: isRead ? '#d9d9d9' : '#faad14',
                color: isRead ? '#666' : '#fff'
              }}
            >
              {isRead ? '已阅' : '已阅'}
            </Button>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <Space>
              <Text strong>{project.name}</Text>
              {project.private && <Tag color="red">私有</Tag>}
              {project.language && <Tag color="blue">{project.language}</Tag>}
            </Space>
          }
          description={
            <div>
              <Text type="secondary">{getProjectDescription(project)}</Text>
              <div style={{ marginTop: '8px' }}>
                <Space>
                  <Space size="small">
                    <StarOutlined />
                    <Text type="secondary">{project.stargazers_count}</Text>
                  </Space>
                  <Space size="small">
                    <EyeOutlined />
                    <Text type="secondary">{project.watchers_count}</Text>
                  </Space>
                  <Text type="secondary">
                    更新于: {new Date(project.updated_at).toLocaleString()}
                  </Text>
                </Space>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <Title level={4} style={{ margin: 0 }}>作者项目详情</Title>
        <Space size="small">
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={loadAuthorProjectUpdates}
            size="small"
            loading={isLoading}
            title="重新加载项目数据"
          >
            刷新
          </Button>
          <Select
            value={selectedLanguage}
            onChange={setSelectedLanguage}
            size="small"
            style={{ width: 120 }}
            options={SUPPORTED_LANGUAGES.map(lang => ({
              value: lang.code,
              label: (
                <span>
                  {lang.flag} {lang.name}
                </span>
              )
            }))}
          />
          <Button
            type="default"
            icon={<TranslationOutlined />}
            onClick={handleTranslate}
            size="small"
            loading={isTranslating}
            disabled={false}
          >
            {Object.keys(translatedDescriptions).length > 0 ? '清除翻译' : '翻译'}
          </Button>
          <Button
            type="default"
            icon={<SettingOutlined />}
            onClick={() => setShowTranslationSettings(true)}
            size="small"
            title="翻译设置"
          />
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={handleMarkAllAsRead}
            size="small"
            style={{
              backgroundColor: '#52c41a',
              borderColor: '#52c41a',
            }}
          >
            一键已阅所有
          </Button>
        </Space>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message="加载错误"
          description={error}
          type="warning"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: '16px' }}
          action={
            <Button
              size="small"
              type="primary"
              onClick={loadAuthorProjectUpdates}
              loading={isLoading}
            >
              重试
            </Button>
          }
        />
      )}

      {/* 加载状态 */}
      <Spin spinning={isLoading} tip="正在加载项目数据...">
        <Collapse
          defaultActiveKey={['1', '2', '3']}
          style={{ backgroundColor: 'transparent' }}
        >
        {/* 作者创建的新项目 */}
        <Panel
          header={
            <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
              🆕 作者创建的新项目 ({newProjects.length})
            </span>
          }
          key="1"
        >
          {newProjects.length > 0 ? (
            <List
              size="small"
              dataSource={newProjects}
              renderItem={(project) => renderProjectItem(project, true)}
              pagination={newProjects.length > 10 ? {
                pageSize: 10,
                size: 'small',
                showSizeChanger: false,
                onChange: () => {
                  // 翻页时清除翻译，用户需要重新点击翻译按钮
                  if (Object.keys(translatedDescriptions).length > 0) {
                    setTranslatedDescriptions({});
                  }
                },
              } : false}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              {allProjects.length > 0 ? '所有新项目都已查看' : '暂无新项目'}
            </div>
          )}
        </Panel>

        {/* 作者更新未查看 */}
        <Panel
          header={
            <span style={{ color: '#faad14', fontWeight: 'bold' }}>
              🔔 作者更新未查看 ({unreadUpdates.length})
            </span>
          }
          key="2"
        >
          {unreadUpdates.length > 0 ? (
            <List
              size="small"
              dataSource={unreadUpdates}
              renderItem={(project) => renderProjectItem(project, true)}
              pagination={unreadUpdates.length > 10 ? {
                pageSize: 10,
                size: 'small',
                showSizeChanger: false,
                onChange: () => {
                  // 翻页时清除翻译，用户需要重新点击翻译按钮
                  if (Object.keys(translatedDescriptions).length > 0) {
                    setTranslatedDescriptions({});
                  }
                },
              } : false}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              {allProjects.length > 0 ? '所有更新都已查看' : '暂无未查看的更新'}
            </div>
          )}
        </Panel>

        {/* 作者所有项目 */}
        <Panel
          header={
            <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
              📁 作者所有项目 ({allProjects.length})
            </span>
          }
          key="3"
        >
          {allProjects.length > 0 ? (
            <List
              size="small"
              dataSource={allProjects}
              renderItem={(project) => renderProjectItem(project, true)}
              pagination={{
                current: allProjectsCurrentPage,
                pageSize: allProjectsPageSize,
                total: allProjects.length,
                size: 'small',
                showSizeChanger: false,
                onChange: (page, pageSize) => {
                  setAllProjectsCurrentPage(page);
                  if (pageSize !== allProjectsPageSize) {
                    setAllProjectsPageSize(pageSize);
                  }
                  // 翻页时清除翻译，用户需要重新点击翻译按钮
                  if (Object.keys(translatedDescriptions).length > 0) {
                    setTranslatedDescriptions({});
                  }
                },
              }}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              暂无项目
            </div>
          )}
        </Panel>
        </Collapse>
      </Spin>

      {/* 翻译设置模态框 */}
      <TranslationSettings
        isOpen={showTranslationSettings}
        onClose={() => setShowTranslationSettings(false)}
      />
    </div>
  );
};

export default AuthorProjectsExpanded;
