/**
 * 作者项目展开组件
 */

import React, { useState, useEffect } from 'react';
import { Card, List, Button, Tag, Space, Typography, Divider, Collapse, Select, Spin, Alert, message } from 'antd';
import { LinkOutlined, CheckOutlined, StarOutlined, EyeOutlined, DownOutlined, UpOutlined, TranslationOutlined, ReloadOutlined, SettingOutlined } from '@ant-design/icons';
import { translationService, SUPPORTED_LANGUAGES } from '../services/translationService';
import { TranslationSettings } from './TranslationSettings';
import {
  loadAuthorProjectData,
  saveAuthorProjectData,
  detectNewProjects,
  detectUpdatedProjects,
  markProjectAsRead,
  markMultipleProjectsAsRead,
  checkProjectNeedsReview,
  AuthorProjectData,
  ProjectItem
} from '@/renderer/utils/authorProjectsManager';

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface AuthorProjectsExpandedProps {
  authorId: string;
  authorName?: string; // 添加作者名称
  projects: ProjectItem[];
  onMarkAsRead: (projectId: string) => void;
  onUpdateCounts?: (authorId: string, counts: { newCount: number; updateCount: number }) => void;
}

const AuthorProjectsExpanded: React.FC<AuthorProjectsExpandedProps> = ({
  authorId,
  authorName,
  projects,
  onMarkAsRead,
  onUpdateCounts,
}) => {
  // 新的状态管理：使用文件系统存储
  const [authorProjectData, setAuthorProjectData] = useState<AuthorProjectData | null>(null);
  const [newProjects, setNewProjects] = useState<ProjectItem[]>([]);
  const [unreadUpdates, setUnreadUpdates] = useState<ProjectItem[]>([]);
  const [allProjects, setAllProjects] = useState<ProjectItem[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('zh');
  const [translatedDescriptions, setTranslatedDescriptions] = useState<Record<string, string>>({});
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showTranslationSettings, setShowTranslationSettings] = useState<boolean>(false);
  const [allProjectsCurrentPage, setAllProjectsCurrentPage] = useState<number>(1);
  const [allProjectsPageSize, setAllProjectsPageSize] = useState<number>(10);

  // 初始化作者项目数据
  useEffect(() => {
    const initializeAuthorData = async () => {
      try {
        // 使用传入的作者名称，如果没有则使用默认格式
        const finalAuthorName = authorName || `Author-${authorId}`;

        // 加载作者项目数据
        const data = await loadAuthorProjectData(authorId, finalAuthorName);
        setAuthorProjectData(data);

        console.log('Loaded author project data:', data);
      } catch (error) {
        console.error('Failed to initialize author data:', error);
        setError('加载作者数据失败');
      }
    };

    initializeAuthorData();
  }, [authorId, authorName]);

  // 当语言切换时清除翻译
  useEffect(() => {
    if (selectedLanguage === 'zh') {
      setTranslatedDescriptions({});
    }
  }, [selectedLanguage]);

  // 当作者数据变化时，重新分类项目
  useEffect(() => {
    if (authorProjectData) {
      categorizeProjectsWithNewLogic();
    }
  }, [authorProjectData]);

  // 从本地缓存加载项目数据
  const loadProjectsFromCache = async (): Promise<ProjectItem[]> => {
    try {
      if (window.electronAPI) {
        // 尝试从文件系统读取缓存
        const cacheResponse = await window.electronAPI.fs.readFile(`data/authors/${authorId}-projects-cache.json`);
        if (cacheResponse.success && cacheResponse.data) {
          const cacheData = JSON.parse(cacheResponse.data);
          // 检查缓存是否过期（24小时）
          const cacheTime = new Date(cacheData.timestamp);
          const now = new Date();
          const hoursDiff = (now.getTime() - cacheTime.getTime()) / (1000 * 60 * 60);

          if (hoursDiff < 24) {
            return cacheData.projects || [];
          }
        }
      } else {
        // Fallback到localStorage
        const cacheKey = `author-projects-cache-${authorId}`;
        const cached = localStorage.getItem(cacheKey);
        if (cached) {
          const cacheData = JSON.parse(cached);
          const cacheTime = new Date(cacheData.timestamp);
          const now = new Date();
          const hoursDiff = (now.getTime() - cacheTime.getTime()) / (1000 * 60 * 60);

          if (hoursDiff < 24) {
            return cacheData.projects || [];
          }
        }
      }
    } catch (error) {
      console.error('Failed to load projects from cache:', error);
    }
    return [];
  };

  // 更新项目缓存
  const updateAuthorProjectCache = async (projects: ProjectItem[]) => {
    try {
      const cacheData = {
        timestamp: new Date().toISOString(),
        projects: projects
      };

      if (window.electronAPI) {
        // 保存到文件系统
        await window.electronAPI.fs.ensureDir('data/authors');
        await window.electronAPI.fs.writeFile(
          `data/authors/${authorId}-projects-cache.json`,
          JSON.stringify(cacheData, null, 2)
        );
      } else {
        // Fallback到localStorage
        const cacheKey = `author-projects-cache-${authorId}`;
        localStorage.setItem(cacheKey, JSON.stringify(cacheData));
      }

      console.log(`Updated project cache for author ${authorId} with ${projects.length} projects`);
    } catch (error) {
      console.error('Failed to update project cache:', error);
    }
  };

  // 新的项目分类逻辑
  const categorizeProjectsWithNewLogic = async () => {
    if (!authorProjectData) return;

    setIsLoading(true);
    setError(null);

    try {
      // 获取GitHub上的所有项目
      let githubProjects: ProjectItem[] = [];

      if (window.electronAPI) {
        const allProjectsResponse = await window.electronAPI.github.getAuthorProjects(authorId);
        if (allProjectsResponse.success && allProjectsResponse.data && allProjectsResponse.data.length > 0) {
          githubProjects = allProjectsResponse.data;
          console.log(`Successfully loaded ${githubProjects.length} projects from API for author ${authorId}`);

          // 成功获取数据时，更新本地缓存
          await updateAuthorProjectCache(githubProjects);
        } else {
          console.warn('API failed or returned empty data, trying to load from cache:', allProjectsResponse.error);

          // API失败时，尝试从本地缓存加载
          githubProjects = await loadProjectsFromCache();

          if (githubProjects.length === 0) {
            // 如果缓存也没有数据，使用props传入的项目
            githubProjects = [...projects];
            console.log(`Using props projects as fallback: ${githubProjects.length} projects`);
          } else {
            console.log(`Loaded ${githubProjects.length} projects from cache`);
          }
        }
      } else {
        // 没有electronAPI时，尝试从localStorage读取
        githubProjects = await loadProjectsFromCache();
        if (githubProjects.length === 0) {
          githubProjects = [...projects];
        }
      }

      // 设置所有项目
      setAllProjects(githubProjects);

      // 1. 检测新项目（GitHub项目 - 本地已知项目）
      const detectedNewProjects = detectNewProjects(githubProjects, authorProjectData.knownProjects);
      setNewProjects(detectedNewProjects);

      // 2. 检测需要查看的更新项目（金色按钮项目）
      const detectedUpdatedProjects = detectUpdatedProjects(
        githubProjects,
        authorProjectData.knownProjects,
        authorProjectData.readTimestamps
      );
      setUnreadUpdates(detectedUpdatedProjects);

      // 3. 更新计数并通知父组件
      const counts = {
        newCount: detectedNewProjects.length,
        updateCount: detectedUpdatedProjects.length
      };

      if (onUpdateCounts) {
        onUpdateCounts(authorId, counts);
      }

      console.log('Project categorization completed:', {
        total: githubProjects.length,
        new: detectedNewProjects.length,
        updated: detectedUpdatedProjects.length,
        known: authorProjectData.knownProjects.length
      });

    } catch (error) {
      console.error('Failed to categorize projects:', error);
      setError('项目分类失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 标记单个项目为已读
  const handleMarkAsRead = async (project: ProjectItem) => {
    if (!authorProjectData) return;

    const projectId = project.id.toString();

    try {
      // 使用传入的作者名称，如果没有则使用默认格式
      const finalAuthorName = authorName || `Author-${authorId}`;

      // 使用新的数据管理方式标记为已读
      const success = await markProjectAsRead(authorId, finalAuthorName, projectId);

      if (success) {
        // 重新加载作者数据
        const updatedData = await loadAuthorProjectData(authorId, finalAuthorName);
        setAuthorProjectData(updatedData);

        // 重新分类项目
        await categorizeProjectsWithNewLogic();

        // 通知父组件
        onMarkAsRead(projectId);

        console.log(`Successfully marked project ${project.name} as read`);
      } else {
        message.error('标记已读失败');
      }
    } catch (error) {
      console.error('Failed to mark project as read:', error);
      message.error('标记已读时发生错误');
    }
  };

  // 一键已阅所有项目
  const handleMarkAllAsRead = async () => {
    if (!authorProjectData) return;

    const allProjectIds = [...newProjects, ...unreadUpdates].map(p => p.id.toString());

    if (allProjectIds.length === 0) {
      message.info('没有需要标记的项目');
      return;
    }

    try {
      // 使用传入的作者名称，如果没有则使用默认格式
      const finalAuthorName = authorName || `Author-${authorId}`;

      // 使用新的数据管理方式批量标记为已读
      const success = await markMultipleProjectsAsRead(authorId, finalAuthorName, allProjectIds);

      if (success) {
        // 重新加载作者数据
        const updatedData = await loadAuthorProjectData(authorId, finalAuthorName);
        setAuthorProjectData(updatedData);

        // 重新分类项目
        await categorizeProjectsWithNewLogic();

        // 通知父组件
        allProjectIds.forEach(id => onMarkAsRead(id));

        message.success(`已标记 ${allProjectIds.length} 个项目为已读`);
        console.log(`Successfully marked ${allProjectIds.length} projects as read`);
      } else {
        message.error('批量标记已读失败');
      }
    } catch (error) {
      console.error('Failed to mark all projects as read:', error);
      message.error('批量标记已读时发生错误');
    }
  };

  // 获取当前页面显示的所有项目（限制数量以提高翻译速度）
  const getCurrentPageProjects = () => {
    const projects: ProjectItem[] = [];

    // 新项目（最多翻译前10个）
    const visibleNewProjects = newProjects.slice(0, 10);
    projects.push(...visibleNewProjects);

    // 更新项目（最多翻译前10个）
    const visibleUnreadUpdates = unreadUpdates.slice(0, 10);
    projects.push(...visibleUnreadUpdates);

    // 所有项目的当前页
    const startIndex = (allProjectsCurrentPage - 1) * allProjectsPageSize;
    const endIndex = startIndex + allProjectsPageSize;
    const visibleAllProjects = allProjects.slice(startIndex, endIndex);
    projects.push(...visibleAllProjects);

    // 去重（避免同一个项目在多个列表中出现）
    const uniqueProjects = projects.filter((project, index, self) =>
      index === self.findIndex(p => p.id === project.id)
    );

    return uniqueProjects;
  };

  // 处理翻译
  const handleTranslate = async () => {
    // 如果当前有翻译内容，清除翻译
    if (Object.keys(translatedDescriptions).length > 0) {
      setTranslatedDescriptions({});
      return;
    }

    setIsTranslating(true);
    try {
      console.log('开始翻译当前页面内容，目标语言:', selectedLanguage);

      // 获取当前页面显示的所有项目
      const currentPageProjects = getCurrentPageProjects();

      console.log('当前页面项目数量:', currentPageProjects.length);
      console.log('当前页面项目详情:', {
        newProjects: Math.min(newProjects.length, 10),
        unreadUpdates: Math.min(unreadUpdates.length, 10),
        allProjectsPage: allProjectsCurrentPage,
        allProjectsOnCurrentPage: Math.min(allProjects.length - (allProjectsCurrentPage - 1) * allProjectsPageSize, allProjectsPageSize)
      });

      if (currentPageProjects.length === 0) {
        console.log('当前页面没有需要翻译的项目');
        return;
      }

      const translationMap: Record<string, string> = {};

      // 逐个翻译项目描述（避免批量翻译可能的问题）
      for (const project of currentPageProjects) {
        if (project.description && project.description.trim()) {
          try {
            console.log(`翻译项目: ${project.name}`);
            const translatedText = await translationService.translateText(project.description, selectedLanguage);
            translationMap[project.id.toString()] = translatedText;
            console.log(`翻译成功: "${project.description.substring(0, 50)}..." -> "${translatedText.substring(0, 50)}..."`);
          } catch (error) {
            console.warn(`翻译项目 ${project.name} 失败:`, error);
            // 翻译失败时保持原文
            translationMap[project.id.toString()] = project.description;
          }
        }
      }

      console.log('翻译完成，成功翻译项目数量:', Object.keys(translationMap).length);
      setTranslatedDescriptions(translationMap);
    } catch (error) {
      console.error('Translation failed:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  // 获取项目描述（翻译后的或原始的）
  const getProjectDescription = (project: ProjectItem): string => {
    // 如果有翻译内容，优先使用翻译内容
    const translatedDesc = translatedDescriptions[project.id.toString()];
    if (translatedDesc) {
      return translatedDesc;
    }
    // 否则返回原始描述
    return project.description || '暂无描述';
  };

  const handleOpenProject = (project: ProjectItem) => {
    // 标记为已读
    handleMarkAsRead(project);

    // 使用系统默认浏览器打开
    if (window.electronAPI) {
      window.electronAPI.system.openExternal(project.html_url);
    } else {
      window.open(project.html_url, '_blank');
    }
  };

  const renderProjectItem = (project: ProjectItem, showReadButton: boolean = true) => {
    if (!authorProjectData) return null;

    // 使用新的逻辑判断项目是否需要查看（金色按钮）
    const needsReview = checkProjectNeedsReview(
      project,
      authorProjectData.knownProjects,
      authorProjectData.readTimestamps
    );

    // 按钮样式逻辑：
    // - 新项目（不在已知项目中）：金色按钮
    // - 已知项目但有更新（项目时间 > 已读时间）：金色按钮
    // - 已知项目且无更新（项目时间 <= 已读时间）：灰色按钮
    const shouldShowGold = needsReview;

    return (
      <List.Item
        key={project.id}
        actions={[
          <Button
            type="link"
            icon={<LinkOutlined />}
            onClick={() => handleOpenProject(project)}
            size="small"
          >
            打开
          </Button>,
          showReadButton && (
            <Button
              type={shouldShowGold ? "primary" : "default"}
              icon={<CheckOutlined />}
              onClick={() => handleMarkAsRead(project)}
              size="small"
              style={{
                backgroundColor: shouldShowGold ? '#faad14' : '#d9d9d9',
                borderColor: shouldShowGold ? '#faad14' : '#d9d9d9',
                color: shouldShowGold ? '#fff' : '#666'
              }}
            >
              已阅
            </Button>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <Space>
              <Text strong>{project.name}</Text>
              {project.private && <Tag color="red">私有</Tag>}
              {project.language && <Tag color="blue">{project.language}</Tag>}
            </Space>
          }
          description={
            <div>
              <Text type="secondary">{getProjectDescription(project)}</Text>
              <div style={{ marginTop: '8px' }}>
                <Space>
                  <Space size="small">
                    <StarOutlined />
                    <Text type="secondary">{project.stargazers_count}</Text>
                  </Space>
                  <Space size="small">
                    <EyeOutlined />
                    <Text type="secondary">{project.watchers_count}</Text>
                  </Space>
                  <Text type="secondary">
                    更新于: {new Date(project.updated_at).toLocaleString()}
                  </Text>
                </Space>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <Title level={4} style={{ margin: 0 }}>作者项目详情</Title>
        <Space size="small">
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={() => categorizeProjectsWithNewLogic()}
            size="small"
            loading={isLoading}
            title="重新加载项目数据"
          >
            刷新
          </Button>
          <Select
            value={selectedLanguage}
            onChange={setSelectedLanguage}
            size="small"
            style={{ width: 120 }}
            options={SUPPORTED_LANGUAGES.map(lang => ({
              value: lang.code,
              label: (
                <span>
                  {lang.flag} {lang.name}
                </span>
              )
            }))}
          />
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={() => categorizeProjectsWithNewLogic()}
            size="small"
            loading={isLoading}
            title="刷新项目数据"
          >
            刷新
          </Button>
          <Button
            type="default"
            icon={<TranslationOutlined />}
            onClick={handleTranslate}
            size="small"
            loading={isTranslating}
            disabled={false}
          >
            {Object.keys(translatedDescriptions).length > 0 ? '清除翻译' : '翻译'}
          </Button>
          <Button
            type="default"
            icon={<SettingOutlined />}
            onClick={() => setShowTranslationSettings(true)}
            size="small"
            title="翻译设置"
          />
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={handleMarkAllAsRead}
            size="small"
            style={{
              backgroundColor: '#52c41a',
              borderColor: '#52c41a',
            }}
          >
            一键已阅所有
          </Button>
        </Space>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message="加载错误"
          description={error}
          type="warning"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: '16px' }}
          action={
            <Button
              size="small"
              type="primary"
              onClick={() => categorizeProjectsWithNewLogic()}
              loading={isLoading}
            >
              重试
            </Button>
          }
        />
      )}

      {/* 加载状态 */}
      <Spin spinning={isLoading} tip="正在加载项目数据...">
        <Collapse
          defaultActiveKey={['1', '2', '3']}
          style={{ backgroundColor: 'transparent' }}
        >
        {/* 作者创建的新项目 */}
        <Panel
          header={
            <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
              🆕 作者创建的新项目 ({newProjects.length})
            </span>
          }
          key="1"
        >
          {newProjects.length > 0 ? (
            <List
              size="small"
              dataSource={newProjects}
              renderItem={(project) => renderProjectItem(project, true)}
              pagination={newProjects.length > 10 ? {
                pageSize: 10,
                size: 'small',
                showSizeChanger: false,
                onChange: () => {
                  // 翻页时清除翻译，用户需要重新点击翻译按钮
                  if (Object.keys(translatedDescriptions).length > 0) {
                    setTranslatedDescriptions({});
                  }
                },
              } : false}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              {allProjects.length > 0 ? '所有新项目都已查看' : '暂无新项目'}
            </div>
          )}
        </Panel>

        {/* 作者更新未查看 */}
        <Panel
          header={
            <span style={{ color: '#faad14', fontWeight: 'bold' }}>
              🔔 作者更新未查看 ({unreadUpdates.length})
            </span>
          }
          key="2"
        >
          {unreadUpdates.length > 0 ? (
            <List
              size="small"
              dataSource={unreadUpdates}
              renderItem={(project) => renderProjectItem(project, true)}
              pagination={unreadUpdates.length > 10 ? {
                pageSize: 10,
                size: 'small',
                showSizeChanger: false,
                onChange: () => {
                  // 翻页时清除翻译，用户需要重新点击翻译按钮
                  if (Object.keys(translatedDescriptions).length > 0) {
                    setTranslatedDescriptions({});
                  }
                },
              } : false}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              {allProjects.length > 0 ? '所有更新都已查看' : '暂无未查看的更新'}
            </div>
          )}
        </Panel>

        {/* 作者所有项目 */}
        <Panel
          header={
            <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
              📁 作者所有项目 ({allProjects.length})
            </span>
          }
          key="3"
        >
          {allProjects.length > 0 ? (
            <List
              size="small"
              dataSource={allProjects}
              renderItem={(project) => renderProjectItem(project, true)}
              pagination={{
                current: allProjectsCurrentPage,
                pageSize: allProjectsPageSize,
                total: allProjects.length,
                size: 'small',
                showSizeChanger: false,
                onChange: (page, pageSize) => {
                  setAllProjectsCurrentPage(page);
                  if (pageSize !== allProjectsPageSize) {
                    setAllProjectsPageSize(pageSize);
                  }
                  // 翻页时清除翻译，用户需要重新点击翻译按钮
                  if (Object.keys(translatedDescriptions).length > 0) {
                    setTranslatedDescriptions({});
                  }
                },
              }}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              暂无项目
            </div>
          )}
        </Panel>
        </Collapse>
      </Spin>

      {/* 翻译设置模态框 */}
      <TranslationSettings
        isOpen={showTranslationSettings}
        onClose={() => setShowTranslationSettings(false)}
      />
    </div>
  );
};

export default AuthorProjectsExpanded;
