/**
 * 作者项目展开组件
 */

import React, { useState, useEffect } from 'react';
import { Card, List, Button, Tag, Space, Typography, Divider } from 'antd';
import { LinkOutlined, CheckOutlined, StarOutlined, EyeOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface ProjectItem {
  id: number;
  name: string;
  full_name: string;
  html_url: string;
  description: string;
  stargazers_count: number;
  watchers_count: number;
  updated_at: string;
  created_at: string;
  language: string;
  private: boolean;
}

interface AuthorProjectsExpandedProps {
  authorId: string;
  projects: ProjectItem[];
  onMarkAsRead: (projectId: string) => void;
}

const AuthorProjectsExpanded: React.FC<AuthorProjectsExpandedProps> = ({
  authorId,
  projects,
  onMarkAsRead,
}) => {
  const [readProjects, setReadProjects] = useState<Set<string>>(new Set());
  const [unreadUpdates, setUnreadUpdates] = useState<ProjectItem[]>([]);
  const [hasUpdates, setHasUpdates] = useState<ProjectItem[]>([]);
  const [allProjects, setAllProjects] = useState<ProjectItem[]>([]);

  useEffect(() => {
    // 从localStorage加载已读状态
    const savedReadProjects = localStorage.getItem(`read-projects-${authorId}`);
    if (savedReadProjects) {
      setReadProjects(new Set(JSON.parse(savedReadProjects)));
    }

    // 分类项目
    categorizeProjects();

    // 加载作者的新项目和更新项目
    loadAuthorProjectUpdates();
  }, [projects, authorId]);

  const loadAuthorProjectUpdates = async () => {
    try {
      if (window.electronAPI) {
        // 获取新项目
        const newProjectsResponse = await window.electronAPI.github.getAuthorNewProjects(authorId);
        if (newProjectsResponse.success) {
          setUnreadUpdates(newProjectsResponse.data || []);
        }

        // 获取更新项目
        const updatedProjectsResponse = await window.electronAPI.github.getAuthorUpdatedProjects(authorId);
        if (updatedProjectsResponse.success) {
          setHasUpdates(updatedProjectsResponse.data || []);
        }
      }
    } catch (error) {
      console.error('Failed to load author project updates:', error);
    }
  };

  const categorizeProjects = () => {
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    // 获取上次检查时间
    const lastCheckTime = localStorage.getItem(`last-check-${authorId}`);
    const lastCheck = lastCheckTime ? new Date(lastCheckTime) : sevenDaysAgo;

    const unread: ProjectItem[] = [];
    const updated: ProjectItem[] = [];
    const all: ProjectItem[] = [...projects];

    projects.forEach(project => {
      const updatedAt = new Date(project.updated_at);
      
      // 如果项目在上次检查后有更新，且未被标记为已读
      if (updatedAt > lastCheck && !readProjects.has(project.id.toString())) {
        unread.push(project);
      }
      
      // 如果项目在最近7天内有更新
      if (updatedAt > sevenDaysAgo) {
        updated.push(project);
      }
    });

    setUnreadUpdates(unread);
    setHasUpdates(updated);
    setAllProjects(all);
  };

  const handleMarkAsRead = (project: ProjectItem) => {
    const newReadProjects = new Set(readProjects);
    newReadProjects.add(project.id.toString());
    setReadProjects(newReadProjects);
    
    // 保存到localStorage
    localStorage.setItem(`read-projects-${authorId}`, JSON.stringify([...newReadProjects]));
    
    // 重新分类项目
    categorizeProjects();
    
    onMarkAsRead(project.id.toString());
  };

  const renderProjectItem = (project: ProjectItem, showReadButton: boolean = true) => {
    const isRead = readProjects.has(project.id.toString());
    
    return (
      <List.Item
        key={project.id}
        actions={[
          <Button
            type="link"
            icon={<LinkOutlined />}
            onClick={() => window.open(project.html_url, '_blank')}
            size="small"
          >
            打开
          </Button>,
          showReadButton && (
            <Button
              type={isRead ? "default" : "primary"}
              icon={<CheckOutlined />}
              onClick={() => handleMarkAsRead(project)}
              size="small"
              style={{
                backgroundColor: isRead ? '#d9d9d9' : '#faad14',
                borderColor: isRead ? '#d9d9d9' : '#faad14',
                color: isRead ? '#666' : '#fff'
              }}
            >
              {isRead ? '已阅' : '已阅'}
            </Button>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <Space>
              <Text strong>{project.name}</Text>
              {project.private && <Tag color="red">私有</Tag>}
              {project.language && <Tag color="blue">{project.language}</Tag>}
            </Space>
          }
          description={
            <div>
              <Text type="secondary">{project.description || '暂无描述'}</Text>
              <div style={{ marginTop: '8px' }}>
                <Space>
                  <Space size="small">
                    <StarOutlined />
                    <Text type="secondary">{project.stargazers_count}</Text>
                  </Space>
                  <Space size="small">
                    <EyeOutlined />
                    <Text type="secondary">{project.watchers_count}</Text>
                  </Space>
                  <Text type="secondary">
                    更新于: {new Date(project.updated_at).toLocaleString()}
                  </Text>
                </Space>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <div>
      <Title level={4}>作者项目详情</Title>
      
      {/* 未查看的更新 */}
      {unreadUpdates.length > 0 && (
        <>
          <Title level={5} style={{ color: '#faad14' }}>
            🔔 作者更新未查看 ({unreadUpdates.length})
          </Title>
          <List
            size="small"
            dataSource={unreadUpdates}
            renderItem={(project) => renderProjectItem(project, true)}
            style={{ marginBottom: '24px' }}
          />
          <Divider />
        </>
      )}

      {/* 有更新的项目 */}
      {hasUpdates.length > 0 && (
        <>
          <Title level={5} style={{ color: '#52c41a' }}>
            📈 最近有更新的项目 ({hasUpdates.length})
          </Title>
          <List
            size="small"
            dataSource={hasUpdates}
            renderItem={(project) => renderProjectItem(project, true)}
            style={{ marginBottom: '24px' }}
          />
          <Divider />
        </>
      )}

      {/* 所有项目 */}
      <Title level={5}>📁 作者所有项目 ({allProjects.length})</Title>
      <List
        size="small"
        dataSource={allProjects}
        renderItem={(project) => renderProjectItem(project, false)}
        pagination={{
          pageSize: 10,
          size: 'small',
          showSizeChanger: false,
        }}
      />
    </div>
  );
};

export default AuthorProjectsExpanded;
