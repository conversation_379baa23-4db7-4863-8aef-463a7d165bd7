import type { Octokit } from "@octokit/core";
export type { RestEndpointMethodTypes } from "./generated/parameters-and-response-types.js";
import type { Api } from "./types.js";
export type { Api };
export declare function restEndpointMethods(octokit: Octokit): Api;
export declare namespace restEndpointMethods {
    var VERSION: string;
}
export declare function legacyRestEndpointMethods(octokit: Octokit): Api["rest"] & Api;
export declare namespace legacyRestEndpointMethods {
    var VERSION: string;
}
