"use strict";
/**
 * 数据操作IPC处理器
 * 负责处理数据相关的IPC通信
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerDataHandlers = registerDataHandlers;
const electron_1 = require("electron");
const index_1 = require("./index");
const dataManager_1 = require("../services/dataManager");
const configManager_1 = require("../services/configManager");
// 获取服务实例
const dataManager = dataManager_1.DataManager.getInstance();
const configManager = configManager_1.ConfigManager.getInstance();
/**
 * 注册数据操作IPC处理器
 */
function registerDataHandlers() {
    // 配置相关
    electron_1.ipcMain.handle('data:getConfig', async () => {
        try {
            const config = configManager.loadConfig();
            return (0, index_1.handleIPCSuccess)(config);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:getConfig', error);
        }
    });
    electron_1.ipcMain.handle('data:setConfig', async (_, config) => {
        try {
            configManager.saveConfig(config);
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:setConfig', error);
        }
    });
    // 分类相关
    electron_1.ipcMain.handle('data:getCategories', async () => {
        try {
            const categories = dataManager.getCategories();
            return (0, index_1.handleIPCSuccess)(categories);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:getCategories', error);
        }
    });
    electron_1.ipcMain.handle('data:setCategories', async (_, categories) => {
        try {
            dataManager.setCategories(categories);
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:setCategories', error);
        }
    });
    // 作者相关
    electron_1.ipcMain.handle('data:getAuthors', async () => {
        try {
            const authors = dataManager.getAuthors();
            return (0, index_1.handleIPCSuccess)(authors);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:getAuthors', error);
        }
    });
    electron_1.ipcMain.handle('data:setAuthors', async (_, authors) => {
        try {
            dataManager.setAuthors(authors);
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:setAuthors', error);
        }
    });
    // 项目相关
    electron_1.ipcMain.handle('data:getProjects', async () => {
        try {
            const projects = dataManager.getProjects();
            return (0, index_1.handleIPCSuccess)(projects);
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:getProjects', error);
        }
    });
    electron_1.ipcMain.handle('data:setProjects', async (_, projects) => {
        try {
            dataManager.setProjects(projects);
            return (0, index_1.handleIPCSuccess)();
        }
        catch (error) {
            return (0, index_1.handleIPCError)('data:setProjects', error);
        }
    });
    console.log('✓ Data IPC handlers registered');
}
