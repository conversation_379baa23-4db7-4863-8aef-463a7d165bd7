<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .loading {
            color: #999;
        }
    </style>
</head>
<body>
    <h1>翻译功能测试页面</h1>
    
    <div class="test-section">
        <h3>测试文本</h3>
        <p><strong>中文项目描述：</strong></p>
        <p id="chinese-text">这是一个基于React和TypeScript开发的GitHub项目监控工具，支持实时监控作者的项目更新，提供多语言翻译功能。</p>
        
        <p><strong>英文项目描述：</strong></p>
        <p id="english-text">Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.</p>
        
        <button onclick="testTranslation('zh', 'en')">中文→英文</button>
        <button onclick="testTranslation('zh', 'ja')">中文→日文</button>
        <button onclick="testTranslation('en', 'zh')">英文→中文</button>
        <button onclick="testTranslation('en', 'ja')">英文→日文</button>
        
        <div id="translation-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>MyMemory API 测试</h3>
        <button onclick="testMyMemoryAPI()">测试 MyMemory API</button>
        <div id="mymemory-result" class="result" style="display: none;"></div>
    </div>

    <script>
        async function testTranslation(from, to) {
            const resultDiv = document.getElementById('translation-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">翻译中...</div>';
            
            const text = from === 'zh' ? 
                document.getElementById('chinese-text').textContent :
                document.getElementById('english-text').textContent;
            
            try {
                // 这里模拟调用翻译服务
                const result = await mockTranslationService(text, from, to);
                resultDiv.innerHTML = `
                    <strong>原文 (${from}):</strong><br>
                    ${text}<br><br>
                    <strong>译文 (${to}):</strong><br>
                    ${result}
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">翻译失败: ${error.message}</div>`;
            }
        }

        async function testMyMemoryAPI() {
            const resultDiv = document.getElementById('mymemory-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">测试中...</div>';
            
            const testText = '这是一个测试文本';
            
            try {
                const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(testText)}&langpair=zh-CN|en`;
                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>API响应:</strong><br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">API测试失败: ${error.message}</div>`;
            }
        }

        // 模拟翻译服务
        async function mockTranslationService(text, from, to) {
            // 模拟API延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 简单的翻译映射
            const translations = {
                'zh-en': {
                    '这是一个基于React和TypeScript开发的GitHub项目监控工具，支持实时监控作者的项目更新，提供多语言翻译功能。': 'This is a GitHub project monitoring tool developed based on React and TypeScript, supporting real-time monitoring of author project updates and providing multi-language translation functions.'
                },
                'zh-ja': {
                    '这是一个基于React和TypeScript开发的GitHub项目监控工具，支持实时监控作者的项目更新，提供多语言翻译功能。': 'これは、ReactとTypeScriptに基づいて開発されたGitHubプロジェクト監視ツールで、作者のプロジェクト更新のリアルタイム監視をサポートし、多言語翻訳機能を提供します。'
                },
                'en-zh': {
                    'Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.': 'Claude-Flow v2.0.0 Alpha代表了AI驱动开发编排的革命性飞跃。采用企业级架构、先进的群体智能和无缝的Claude Code集成，从头开始构建。'
                },
                'en-ja': {
                    'Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.': 'Claude-Flow v2.0.0 Alphaは、AI駆動開発オーケストレーションにおける革命的な飛躍を表しています。エンタープライズグレードのアーキテクチャ、高度なスウォームインテリジェンス、シームレスなClaude Code統合により、ゼロから構築されています。'
                }
            };
            
            const key = `${from}-${to}`;
            if (translations[key] && translations[key][text]) {
                return translations[key][text];
            }
            
            return `[模拟翻译] ${text}`;
        }
    </script>
</body>
</html>
