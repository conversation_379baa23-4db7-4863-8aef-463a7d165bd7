import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SisternodeOutlinedSvg from "@ant-design/icons-svg/es/asn/SisternodeOutlined";
import AntdIcon from "../components/AntdIcon";
var SisternodeOutlined = function SisternodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SisternodeOutlinedSvg
  }));
};

/**![sisternode](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02NzIgNDMyYy0xMjAuMyAwLTIxOS45IDg4LjUtMjM3LjMgMjA0SDMyMGMtMTUuNSAwLTI4LTEyLjUtMjgtMjhWMjQ0aDI5MWMxNC4yIDM1LjIgNDguNyA2MCA4OSA2MCA1MyAwIDk2LTQzIDk2LTk2cy00My05Ni05Ni05NmMtNDAuMyAwLTc0LjggMjQuOC04OSA2MEgxMTJ2NzJoMTA4djM2NGMwIDU1LjIgNDQuOCAxMDAgMTAwIDEwMGgxMTQuN2MxNy40IDExNS41IDExNyAyMDQgMjM3LjMgMjA0IDEzMi41IDAgMjQwLTEwNy41IDI0MC0yNDBTODA0LjUgNDMyIDY3MiA0MzJ6bTEyOCAyNjZjMCA0LjQtMy42IDgtOCA4aC04NnY4NmMwIDQuNC0zLjYgOC04IDhoLTUyYy00LjQgMC04LTMuNi04LTh2LTg2aC04NmMtNC40IDAtOC0zLjYtOC04di01MmMwLTQuNCAzLjYtOCA4LThoODZ2LTg2YzAtNC40IDMuNi04IDgtOGg1MmM0LjQgMCA4IDMuNiA4IDh2ODZoODZjNC40IDAgOCAzLjYgOCA4djUyeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(SisternodeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SisternodeOutlined';
}
export default RefIcon;