import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AntDesignOutlinedSvg from "@ant-design/icons-svg/es/asn/AntDesignOutlined";
import AntdIcon from "../components/AntdIcon";
var AntDesignOutlined = function AntDesignOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AntDesignOutlinedSvg
  }));
};

/**![ant-design](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcxNi4zIDMxMy44YzE5LTE4LjkgMTktNDkuNyAwLTY4LjZsLTY5LjktNjkuOS4xLjFjLTE4LjUtMTguNS01MC4zLTUwLjMtOTUuMy05NS4yLTIxLjItMjAuNy01NS41LTIwLjUtNzYuNS41TDgwLjkgNDc0LjJhNTMuODQgNTMuODQgMCAwMDAgNzYuNEw0NzQuNiA5NDRhNTQuMTQgNTQuMTQgMCAwMDc2LjUgMGwxNjUuMS0xNjVjMTktMTguOSAxOS00OS43IDAtNjguNmE0OC43IDQ4LjcgMCAwMC02OC43IDBsLTEyNSAxMjUuMmMtNS4yIDUuMi0xMy4zIDUuMi0xOC41IDBMMTg5LjUgNTIxLjRjLTUuMi01LjItNS4yLTEzLjMgMC0xOC41bDMxNC40LTMxNC4yYy40LS40LjktLjcgMS4zLTEuMSA1LjItNC4xIDEyLjQtMy43IDE3LjIgMS4xbDEyNS4yIDEyNS4xYzE5IDE5IDQ5LjggMTkgNjguNyAwek00MDguNiA1MTQuNGExMDYuMyAxMDYuMiAwIDEwMjEyLjYgMCAxMDYuMyAxMDYuMiAwIDEwLTIxMi42IDB6bTUzNi4yLTM4LjZMODIxLjkgMzUzLjVjLTE5LTE4LjktNDkuOC0xOC45LTY4LjcuMWE0OC40IDQ4LjQgMCAwMDAgNjguNmw4MyA4Mi45YzUuMiA1LjIgNS4yIDEzLjMgMCAxOC41bC04MS44IDgxLjdhNDguNCA0OC40IDAgMDAwIDY4LjYgNDguNyA0OC43IDAgMDA2OC43IDBsMTIxLjgtMTIxLjdhNTMuOTMgNTMuOTMgMCAwMC0uMS03Ni40eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(AntDesignOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AntDesignOutlined';
}
export default RefIcon;