name: Test
on:
  push:
    branches:
      - master
  pull_request:
    types:
      - opened
      - synchronize
      - reopened

jobs:
  test-deno:
    runs-on: ubuntu-latest
    needs: test-node
    steps:
      - uses: actions/checkout@v4
      - uses: denoland/setup-deno@v2
        with:
          deno-version: v2.x # Run with latest stable Deno.
      - run: deno install
      - run: deno test

  test-bun:
    runs-on: ubuntu-latest
    needs: test-node
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bun test

  test-node:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node_version:
          - 12
          - 14
          - 16
          - 18
          - 20
          - 22
          - 24

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node_version }}
          cache: npm
      - run: npm ci
      - run: npm run test:node

  test-types:
    runs-on: ubuntu-latest
    needs:
      - test-node
      - test-deno
      - test-bun
    steps:
      - uses: actions/checkout@v2
      - run: npm ci
      - run: npm run lint
      - run: npm run test:types
