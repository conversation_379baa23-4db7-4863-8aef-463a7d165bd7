/**
 * 更新检查服务
 * 负责处理GitHub数据的定时更新和检查
 */

import { DataManager } from './dataManager';
import { ConfigManager } from './configManager';
import { Author, Project } from '@/shared/types';

/**
 * 更新检查器类
 */
export class UpdateChecker {
  private static instance: UpdateChecker;
  private dataManager: DataManager;
  private configManager: ConfigManager;
  private updateTimer: NodeJS.Timeout | null = null;
  private isUpdating = false;

  private constructor() {
    this.dataManager = DataManager.getInstance();
    this.configManager = ConfigManager.getInstance();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): UpdateChecker {
    if (!UpdateChecker.instance) {
      UpdateChecker.instance = new UpdateChecker();
    }
    return UpdateChecker.instance;
  }

  /**
   * 启动定时更新
   */
  public startScheduledUpdates(): void {
    const config = this.configManager.loadConfig();
    
    if (config.update.autoCheck && config.update.interval > 0) {
      this.stopScheduledUpdates();
      
      this.updateTimer = setInterval(() => {
        this.checkForUpdates();
      }, config.update.interval);

      console.log(`Scheduled updates started with interval: ${config.update.interval}ms`);
    }
  }

  /**
   * 停止定时更新
   */
  public stopScheduledUpdates(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
      console.log('Scheduled updates stopped');
    }
  }

  /**
   * 检查更新
   */
  public async checkForUpdates(): Promise<void> {
    if (this.isUpdating) {
      console.log('Update already in progress, skipping...');
      return;
    }

    this.isUpdating = true;
    console.log('Starting update check...');

    try {
      const config = this.configManager.loadConfig();
      
      // 更新最后检查时间
      config.update.lastCheck = new Date().toISOString();
      this.configManager.saveConfig(config);

      // 检查作者更新
      await this.checkAuthorsUpdates();

      // 检查项目更新
      await this.checkProjectsUpdates();

      // 记录更新历史
      this.dataManager.addHistoryItem({
        type: 'update_check',
        status: 'completed',
        message: 'Update check completed successfully',
      });

      console.log('Update check completed');
    } catch (error) {
      console.error('Error during update check:', error);
      
      this.dataManager.addHistoryItem({
        type: 'update_check',
        status: 'failed',
        message: `Update check failed: ${error}`,
        error: error instanceof Error ? error.message : String(error),
      });
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 检查作者更新
   */
  private async checkAuthorsUpdates(): Promise<void> {
    const authors = this.dataManager.getAuthors();
    const activeAuthors = authors.filter(author => author.isActive && author.isWatching);

    console.log(`Checking updates for ${activeAuthors.length} authors...`);

    for (const author of activeAuthors) {
      try {
        await this.checkAuthorUpdate(author);
        
        // 添加延迟以避免API限制
        await this.delay(1000);
      } catch (error) {
        console.error(`Error checking author ${author.username}:`, error);
      }
    }
  }

  /**
   * 检查单个作者更新
   */
  private async checkAuthorUpdate(author: Author): Promise<void> {
    // 这里应该调用GitHub API检查作者信息
    // 暂时模拟更新
    const now = new Date().toISOString();
    
    this.dataManager.updateAuthor(author.id, {
      lastCheckedAt: now,
    });

    console.log(`Checked author: ${author.username}`);
  }

  /**
   * 检查项目更新
   */
  private async checkProjectsUpdates(): Promise<void> {
    const projects = this.dataManager.getProjects();
    const activeProjects = projects.filter(project => project.isActive && project.isWatching);

    console.log(`Checking updates for ${activeProjects.length} projects...`);

    for (const project of activeProjects) {
      try {
        await this.checkProjectUpdate(project);
        
        // 添加延迟以避免API限制
        await this.delay(1000);
      } catch (error) {
        console.error(`Error checking project ${project.fullName}:`, error);
      }
    }
  }

  /**
   * 检查单个项目更新
   */
  private async checkProjectUpdate(project: Project): Promise<void> {
    // 这里应该调用GitHub API检查项目信息
    // 暂时模拟更新
    const now = new Date().toISOString();
    
    this.dataManager.updateProject(project.id, {
      lastCheckedAt: now,
    });

    console.log(`Checked project: ${project.fullName}`);
  }

  /**
   * 手动触发更新检查
   */
  public async manualUpdateCheck(): Promise<void> {
    console.log('Manual update check triggered');
    await this.checkForUpdates();
  }

  /**
   * 获取更新状态
   */
  public getUpdateStatus(): {
    isUpdating: boolean;
    lastCheck?: string;
    nextCheck?: string;
  } {
    const config = this.configManager.loadConfig();
    const nextCheck = config.update.lastCheck 
      ? new Date(new Date(config.update.lastCheck).getTime() + config.update.interval).toISOString()
      : undefined;

    return {
      isUpdating: this.isUpdating,
      lastCheck: config.update.lastCheck,
      nextCheck,
    };
  }

  /**
   * 更新配置
   */
  public updateConfig(): void {
    this.stopScheduledUpdates();
    this.startScheduledUpdates();
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.stopScheduledUpdates();
  }

  /**
   * 获取更新统计
   */
  public getUpdateStatistics(): {
    totalAuthors: <AUTHORS>
    activeAuthors: <AUTHORS>
    watchingAuthors: <AUTHORS>
    totalProjects: number;
    activeProjects: number;
    watchingProjects: number;
  } {
    const authors = this.dataManager.getAuthors();
    const projects = this.dataManager.getProjects();

    return {
      totalAuthors: <AUTHORS>
      activeAuthors: <AUTHORS>
      watchingAuthors: <AUTHORS>
      totalProjects: projects.length,
      activeProjects: projects.filter(p => p.isActive).length,
      watchingProjects: projects.filter(p => p.isActive && p.isWatching).length,
    };
  }

  /**
   * 强制更新所有数据
   */
  public async forceUpdateAll(): Promise<void> {
    console.log('Force updating all data...');
    
    // 重置所有lastCheckedAt时间戳
    const authors = this.dataManager.getAuthors();
    const projects = this.dataManager.getProjects();

    authors.forEach(author => {
      if (author.isActive && author.isWatching) {
        this.dataManager.updateAuthor(author.id, {
          lastCheckedAt: undefined,
        });
      }
    });

    projects.forEach(project => {
      if (project.isActive && project.isWatching) {
        this.dataManager.updateProject(project.id, {
          lastCheckedAt: undefined,
        });
      }
    });

    // 执行更新检查
    await this.checkForUpdates();
  }
}
