import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CarFilledSvg from "@ant-design/icons-svg/es/asn/CarFilled";
import AntdIcon from "../components/AntdIcon";
var CarFilled = function CarFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CarFilledSvg
  }));
};

/**![car](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1OSA0MTMuNEw5MzUuMyAzNzJhOCA4IDAgMDAtMTAuOS0yLjlsLTUwLjcgMjkuNi03OC4zLTIxNi4yYTYzLjkgNjMuOSAwIDAwLTYwLjktNDQuNEgzMDEuMmMtMzQuNyAwLTY1LjUgMjIuNC03Ni4yIDU1LjVsLTc0LjYgMjA1LjItNTAuOC0yOS42YTggOCAwIDAwLTEwLjkgMi45TDY1IDQxMy40Yy0yLjIgMy44LS45IDguNiAyLjkgMTAuOGw2MC40IDM1LjItMTQuNSA0MGMtMS4yIDMuMi0xLjggNi42LTEuOCAxMHYzNDguMmMwIDE1LjcgMTEuOCAyOC40IDI2LjMgMjguNGg2Ny42YzEyLjMgMCAyMy05LjMgMjUuNi0yMi4zbDcuNy0zNy43aDU0NS42bDcuNyAzNy43YzIuNyAxMyAxMy4zIDIyLjMgMjUuNiAyMi4zaDY3LjZjMTQuNSAwIDI2LjMtMTIuNyAyNi4zLTI4LjRWNTA5LjRjMC0zLjQtLjYtNi44LTEuOC0xMGwtMTQuNS00MCA2MC4zLTM1LjJhOCA4IDAgMDAzLTEwLjh6TTI2NCA2MjFjLTIyLjEgMC00MC0xNy45LTQwLTQwczE3LjktNDAgNDAtNDAgNDAgMTcuOSA0MCA0MC0xNy45IDQwLTQwIDQwem0zODggNzVjMCA0LjQtMy42IDgtOCA4SDM4MGMtNC40IDAtOC0zLjYtOC04di04NGMwLTQuNCAzLjYtOCA4LThoNDBjNC40IDAgOCAzLjYgOCA4djM2aDE2OHYtMzZjMC00LjQgMy42LTggOC04aDQwYzQuNCAwIDggMy42IDggOHY4NHptMTA4LTc1Yy0yMi4xIDAtNDAtMTcuOS00MC00MHMxNy45LTQwIDQwLTQwIDQwIDE3LjkgNDAgNDAtMTcuOSA0MC00MCA0MHpNMjIwIDQxOGw3Mi43LTE5OS45LjUtMS4zLjQtMS4zYzEuMS0zLjMgNC4xLTUuNSA3LjYtNS41aDQyNy42bDc1LjQgMjA4SDIyMHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CarFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CarFilled';
}
export default RefIcon;