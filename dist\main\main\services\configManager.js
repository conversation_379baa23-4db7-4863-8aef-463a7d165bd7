"use strict";
/**
 * 配置管理服务
 * 负责处理应用配置的加载、保存和验证
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * 配置管理器类
 */
class ConfigManager {
    constructor() {
        this.config = null;
        this.configPath = path.join(process.cwd(), 'data', 'config.json');
        this.ensureConfigFile();
    }
    /**
     * 获取单例实例
     */
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    /**
     * 确保配置文件存在
     */
    ensureConfigFile() {
        const dir = path.dirname(this.configPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        if (!fs.existsSync(this.configPath)) {
            this.saveConfig(this.getDefaultConfig());
        }
    }
    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            version: '1.0.0',
            theme: 'dark',
            language: 'zh',
            github: {
                token: '',
                rateLimit: 5000,
                concurrent: 3,
                apiUrl: 'https://api.github.com',
            },
            update: {
                interval: 3600000, // 1小时
                autoCheck: true,
                lastCheck: undefined,
            },
            ui: {
                windowSize: {
                    width: 1200,
                    height: 800,
                },
                windowPosition: {
                    x: 100,
                    y: 100,
                },
                sidebarWidth: 240,
                showStatusBar: true,
                compactMode: false,
            },
        };
    }
    /**
     * 加载配置
     */
    loadConfig() {
        try {
            if (this.config) {
                return this.config;
            }
            const content = fs.readFileSync(this.configPath, 'utf-8');
            const loadedConfig = JSON.parse(content);
            // 合并默认配置以确保所有字段都存在
            this.config = this.mergeWithDefaults(loadedConfig);
            return this.config;
        }
        catch (error) {
            console.error('Error loading config:', error);
            this.config = this.getDefaultConfig();
            this.saveConfig(this.config);
            return this.config;
        }
    }
    /**
     * 保存配置
     */
    saveConfig(config) {
        try {
            this.config = config;
            fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2), 'utf-8');
        }
        catch (error) {
            console.error('Error saving config:', error);
            throw error;
        }
    }
    /**
     * 更新配置
     */
    updateConfig(updates) {
        const currentConfig = this.loadConfig();
        const updatedConfig = this.deepMerge(currentConfig, updates);
        this.saveConfig(updatedConfig);
        return updatedConfig;
    }
    /**
     * 获取配置项
     */
    getConfigValue(key) {
        const config = this.loadConfig();
        return this.getNestedValue(config, key);
    }
    /**
     * 设置配置项
     */
    setConfigValue(key, value) {
        const config = this.loadConfig();
        this.setNestedValue(config, key, value);
        this.saveConfig(config);
    }
    /**
     * 重置配置
     */
    resetConfig() {
        const defaultConfig = this.getDefaultConfig();
        this.saveConfig(defaultConfig);
        return defaultConfig;
    }
    /**
     * 验证配置
     */
    validateConfig(config) {
        const errors = [];
        // 验证必需字段
        if (!config.version)
            errors.push('Missing version');
        if (!config.theme)
            errors.push('Missing theme');
        if (!config.language)
            errors.push('Missing language');
        // 验证GitHub配置
        if (!config.github) {
            errors.push('Missing github config');
        }
        else {
            if (typeof config.github.rateLimit !== 'number') {
                errors.push('Invalid github.rateLimit');
            }
            if (typeof config.github.concurrent !== 'number') {
                errors.push('Invalid github.concurrent');
            }
        }
        // 验证更新配置
        if (!config.update) {
            errors.push('Missing update config');
        }
        else {
            if (typeof config.update.interval !== 'number') {
                errors.push('Invalid update.interval');
            }
            if (typeof config.update.autoCheck !== 'boolean') {
                errors.push('Invalid update.autoCheck');
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * 导出配置
     */
    exportConfig() {
        const config = this.loadConfig();
        return JSON.stringify(config, null, 2);
    }
    /**
     * 导入配置
     */
    importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            const validation = this.validateConfig(importedConfig);
            if (!validation.isValid) {
                throw new Error(`Invalid config: ${validation.errors.join(', ')}`);
            }
            const mergedConfig = this.mergeWithDefaults(importedConfig);
            this.saveConfig(mergedConfig);
            return mergedConfig;
        }
        catch (error) {
            console.error('Error importing config:', error);
            throw error;
        }
    }
    /**
     * 与默认配置合并
     */
    mergeWithDefaults(config) {
        const defaultConfig = this.getDefaultConfig();
        return this.deepMerge(defaultConfig, config);
    }
    /**
     * 深度合并对象
     */
    deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(target[key] || {}, source[key]);
                }
                else {
                    result[key] = source[key];
                }
            }
        }
        return result;
    }
    /**
     * 获取嵌套值
     */
    getNestedValue(obj, key) {
        return key.split('.').reduce((current, prop) => current?.[prop], obj);
    }
    /**
     * 设置嵌套值
     */
    setNestedValue(obj, key, value) {
        const keys = key.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, prop) => {
            if (!current[prop])
                current[prop] = {};
            return current[prop];
        }, obj);
        target[lastKey] = value;
    }
    /**
     * 获取配置文件路径
     */
    getConfigPath() {
        return this.configPath;
    }
    /**
     * 检查配置文件是否存在
     */
    configExists() {
        return fs.existsSync(this.configPath);
    }
}
exports.ConfigManager = ConfigManager;
