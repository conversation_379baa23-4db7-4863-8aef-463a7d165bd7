"use strict";
/**
 * 分类相关类型定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CATEGORY_ICONS = exports.CATEGORY_COLORS = exports.CategoryType = void 0;
// 分类类型枚举
var CategoryType;
(function (CategoryType) {
    CategoryType["PROJECT"] = "project";
    CategoryType["AUTHOR"] = "author";
})(CategoryType || (exports.CategoryType = CategoryType = {}));
// 预定义分类颜色
exports.CATEGORY_COLORS = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#faad14', // 黄色
    '#f5222d', // 红色
    '#722ed1', // 紫色
    '#fa541c', // 橙色
    '#13c2c2', // 青色
    '#eb2f96', // 品红
    '#666666', // 灰色
    '#2f54eb', // 深蓝
];
// 预定义分类图标
exports.CATEGORY_ICONS = [
    'folder',
    'tag',
    'star',
    'heart',
    'bookmark',
    'flag',
    'trophy',
    'diamond',
    'crown',
    'fire',
];
