import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FileAddTwoToneSvg from "@ant-design/icons-svg/es/asn/FileAddTwoTone";
import AntdIcon from "../components/AntdIcon";
var FileAddTwoTone = function FileAddTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FileAddTwoToneSvg
  }));
};

/**![file-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNCAzNTJWMTM2SDIzMnY3NTJoNTYwVjM5NEg1NzZhNDIgNDIgMCAwMS00Mi00MnptMTI2IDIzNnY0OGMwIDQuNC0zLjYgOC04IDhINTQ0djEwOGMwIDQuNC0zLjYgOC04IDhoLTQ4Yy00LjQgMC04LTMuNi04LThWNjQ0SDM3MmMtNC40IDAtOC0zLjYtOC04di00OGMwLTQuNCAzLjYtOCA4LThoMTA4VjQ3MmMwLTQuNCAzLjYtOCA4LThoNDhjNC40IDAgOCAzLjYgOCA4djEwOGgxMDhjNC40IDAgOCAzLjYgOCA4eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODU0LjYgMjg4LjZMNjM5LjQgNzMuNGMtNi02LTE0LjEtOS40LTIyLjYtOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNy05LjQtMjIuN3pNNjAyIDEzNy44TDc5MC4yIDMyNkg2MDJWMTM3Ljh6TTc5MiA4ODhIMjMyVjEzNmgzMDJ2MjE2YTQyIDQyIDAgMDA0MiA0MmgyMTZ2NDk0eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNNTQ0IDQ3MmMwLTQuNC0zLjYtOC04LThoLTQ4Yy00LjQgMC04IDMuNi04IDh2MTA4SDM3MmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxMDh2MTA4YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNjQ0aDEwOGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEg1NDRWNDcyeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileAddTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileAddTwoTone';
}
export default RefIcon;