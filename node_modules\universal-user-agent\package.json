{"name": "universal-user-agent", "version": "7.0.3", "type": "module", "description": "Get a user agent string across all JavaScript Runtime Environments", "exports": "./index.js", "types": "index.d.ts", "repository": "github:gr2m/universal-user-agent", "keywords": [], "author": "<PERSON> (https://github.com/gr2m)", "license": "ISC", "scripts": {"lint": "prettier --check '*.{js,json,md}'", "lint:fix": "prettier --write '*.{js,json,md}'", "test": "npm run test:node && npm run test:types", "test:bun": "bun test", "test:node": "node index.test.js", "test:deno": "deno test", "test:types": "tsd"}, "devDependencies": {"prettier": "^2.0.0", "tsd": "^0.17.0"}, "publishConfig": {"provenance": true}}