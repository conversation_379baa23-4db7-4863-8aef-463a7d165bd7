import type { Endpoints } from "@octokit/types";
export interface PaginatingEndpoints {
    /**
     * @see https://docs.github.com/rest/security-advisories/global-advisories#list-global-security-advisories
     */
    "GET /advisories": {
        parameters: Endpoints["GET /advisories"]["parameters"];
        response: Endpoints["GET /advisories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/webhooks#list-deliveries-for-an-app-webhook
     */
    "GET /app/hook/deliveries": {
        parameters: Endpoints["GET /app/hook/deliveries"]["parameters"];
        response: Endpoints["GET /app/hook/deliveries"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/apps#list-installation-requests-for-the-authenticated-app
     */
    "GET /app/installation-requests": {
        parameters: Endpoints["GET /app/installation-requests"]["parameters"];
        response: Endpoints["GET /app/installation-requests"]["response"];
    };
    /**
     * @see https://docs.github.com/enterprise-server@3.9/rest/apps/apps#list-installations-for-the-authenticated-app
     */
    "GET /app/installations": {
        parameters: Endpoints["GET /app/installations"]["parameters"];
        response: Endpoints["GET /app/installations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/classroom/classroom#list-accepted-assignments-for-an-assignment
     */
    "GET /assignments/{assignment_id}/accepted_assignments": {
        parameters: Endpoints["GET /assignments/{assignment_id}/accepted_assignments"]["parameters"];
        response: Endpoints["GET /assignments/{assignment_id}/accepted_assignments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/classroom/classroom#list-classrooms
     */
    "GET /classrooms": {
        parameters: Endpoints["GET /classrooms"]["parameters"];
        response: Endpoints["GET /classrooms"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/classroom/classroom#list-assignments-for-a-classroom
     */
    "GET /classrooms/{classroom_id}/assignments": {
        parameters: Endpoints["GET /classrooms/{classroom_id}/assignments"]["parameters"];
        response: Endpoints["GET /classrooms/{classroom_id}/assignments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/code-security/configurations#get-code-security-configurations-for-an-enterprise
     */
    "GET /enterprises/{enterprise}/code-security/configurations": {
        parameters: Endpoints["GET /enterprises/{enterprise}/code-security/configurations"]["parameters"];
        response: Endpoints["GET /enterprises/{enterprise}/code-security/configurations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/code-security/configurations#get-repositories-associated-with-an-enterprise-code-security-configuration
     */
    "GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories": {
        parameters: Endpoints["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories"]["parameters"];
        response: Endpoints["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/dependabot/alerts#list-dependabot-alerts-for-an-enterprise
     */
    "GET /enterprises/{enterprise}/dependabot/alerts": {
        parameters: Endpoints["GET /enterprises/{enterprise}/dependabot/alerts"]["parameters"];
        response: Endpoints["GET /enterprises/{enterprise}/dependabot/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/secret-scanning/secret-scanning#list-secret-scanning-alerts-for-an-enterprise
     */
    "GET /enterprises/{enterprise}/secret-scanning/alerts": {
        parameters: Endpoints["GET /enterprises/{enterprise}/secret-scanning/alerts"]["parameters"];
        response: Endpoints["GET /enterprises/{enterprise}/secret-scanning/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-public-events
     */
    "GET /events": {
        parameters: Endpoints["GET /events"]["parameters"];
        response: Endpoints["GET /events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/gists/gists#list-gists-for-the-authenticated-user
     */
    "GET /gists": {
        parameters: Endpoints["GET /gists"]["parameters"];
        response: Endpoints["GET /gists"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/gists/gists#list-public-gists
     */
    "GET /gists/public": {
        parameters: Endpoints["GET /gists/public"]["parameters"];
        response: Endpoints["GET /gists/public"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/gists/gists#list-starred-gists
     */
    "GET /gists/starred": {
        parameters: Endpoints["GET /gists/starred"]["parameters"];
        response: Endpoints["GET /gists/starred"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/gists/comments#list-gist-comments
     */
    "GET /gists/{gist_id}/comments": {
        parameters: Endpoints["GET /gists/{gist_id}/comments"]["parameters"];
        response: Endpoints["GET /gists/{gist_id}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/gists/gists#list-gist-commits
     */
    "GET /gists/{gist_id}/commits": {
        parameters: Endpoints["GET /gists/{gist_id}/commits"]["parameters"];
        response: Endpoints["GET /gists/{gist_id}/commits"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/gists/gists#list-gist-forks
     */
    "GET /gists/{gist_id}/forks": {
        parameters: Endpoints["GET /gists/{gist_id}/forks"]["parameters"];
        response: Endpoints["GET /gists/{gist_id}/forks"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/installations#list-repositories-accessible-to-the-app-installation
     */
    "GET /installation/repositories": {
        parameters: Endpoints["GET /installation/repositories"]["parameters"];
        response: Endpoints["GET /installation/repositories"]["response"] & {
            data: Endpoints["GET /installation/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/issues/issues#list-issues-assigned-to-the-authenticated-user
     */
    "GET /issues": {
        parameters: Endpoints["GET /issues"]["parameters"];
        response: Endpoints["GET /issues"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/licenses/licenses#get-all-commonly-used-licenses
     */
    "GET /licenses": {
        parameters: Endpoints["GET /licenses"]["parameters"];
        response: Endpoints["GET /licenses"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/marketplace#list-plans
     */
    "GET /marketplace_listing/plans": {
        parameters: Endpoints["GET /marketplace_listing/plans"]["parameters"];
        response: Endpoints["GET /marketplace_listing/plans"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/marketplace#list-accounts-for-a-plan
     */
    "GET /marketplace_listing/plans/{plan_id}/accounts": {
        parameters: Endpoints["GET /marketplace_listing/plans/{plan_id}/accounts"]["parameters"];
        response: Endpoints["GET /marketplace_listing/plans/{plan_id}/accounts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/marketplace#list-plans-stubbed
     */
    "GET /marketplace_listing/stubbed/plans": {
        parameters: Endpoints["GET /marketplace_listing/stubbed/plans"]["parameters"];
        response: Endpoints["GET /marketplace_listing/stubbed/plans"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/marketplace#list-accounts-for-a-plan-stubbed
     */
    "GET /marketplace_listing/stubbed/plans/{plan_id}/accounts": {
        parameters: Endpoints["GET /marketplace_listing/stubbed/plans/{plan_id}/accounts"]["parameters"];
        response: Endpoints["GET /marketplace_listing/stubbed/plans/{plan_id}/accounts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-public-events-for-a-network-of-repositories
     */
    "GET /networks/{owner}/{repo}/events": {
        parameters: Endpoints["GET /networks/{owner}/{repo}/events"]["parameters"];
        response: Endpoints["GET /networks/{owner}/{repo}/events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/notifications#list-notifications-for-the-authenticated-user
     */
    "GET /notifications": {
        parameters: Endpoints["GET /notifications"]["parameters"];
        response: Endpoints["GET /notifications"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/orgs#list-organizations
     */
    "GET /organizations": {
        parameters: Endpoints["GET /organizations"]["parameters"];
        response: Endpoints["GET /organizations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/actions/cache#list-repositories-with-github-actions-cache-usage-for-an-organization
     */
    "GET /orgs/{org}/actions/cache/usage-by-repository": {
        parameters: Endpoints["GET /orgs/{org}/actions/cache/usage-by-repository"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/cache/usage-by-repository"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/cache/usage-by-repository"]["response"]["data"]["repository_cache_usages"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/hosted-runners#list-github-hosted-runners-for-an-organization
     */
    "GET /orgs/{org}/actions/hosted-runners": {
        parameters: Endpoints["GET /orgs/{org}/actions/hosted-runners"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/hosted-runners"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/hosted-runners"]["response"]["data"]["runners"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/permissions#list-selected-repositories-enabled-for-github-actions-in-an-organization
     */
    "GET /orgs/{org}/actions/permissions/repositories": {
        parameters: Endpoints["GET /orgs/{org}/actions/permissions/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/permissions/repositories"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/permissions/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/self-hosted-runner-groups#list-self-hosted-runner-groups-for-an-organization
     */
    "GET /orgs/{org}/actions/runner-groups": {
        parameters: Endpoints["GET /orgs/{org}/actions/runner-groups"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/runner-groups"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/runner-groups"]["response"]["data"]["runner_groups"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/self-hosted-runner-groups#list-github-hosted-runners-in-a-group-for-an-organization
     */
    "GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners": {
        parameters: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners"]["response"]["data"]["runners"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/self-hosted-runner-groups#list-repository-access-to-a-self-hosted-runner-group-in-an-organization
     */
    "GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/self-hosted-runner-groups#list-self-hosted-runners-in-a-group-for-an-organization
     */
    "GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners": {
        parameters: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners"]["response"]["data"]["runners"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/self-hosted-runners#list-self-hosted-runners-for-an-organization
     */
    "GET /orgs/{org}/actions/runners": {
        parameters: Endpoints["GET /orgs/{org}/actions/runners"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/runners"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/runners"]["response"]["data"]["runners"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/secrets#list-organization-secrets
     */
    "GET /orgs/{org}/actions/secrets": {
        parameters: Endpoints["GET /orgs/{org}/actions/secrets"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/secrets"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/secrets#list-selected-repositories-for-an-organization-secret
     */
    "GET /orgs/{org}/actions/secrets/{secret_name}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/variables#list-organization-variables
     */
    "GET /orgs/{org}/actions/variables": {
        parameters: Endpoints["GET /orgs/{org}/actions/variables"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/variables"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/variables"]["response"]["data"]["variables"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/variables#list-selected-repositories-for-an-organization-variable
     */
    "GET /orgs/{org}/actions/variables/{name}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/actions/variables/{name}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/actions/variables/{name}/repositories"]["response"] & {
            data: Endpoints["GET /orgs/{org}/actions/variables/{name}/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/orgs/orgs#list-attestations
     */
    "GET /orgs/{org}/attestations/{subject_digest}": {
        parameters: Endpoints["GET /orgs/{org}/attestations/{subject_digest}"]["parameters"];
        response: Endpoints["GET /orgs/{org}/attestations/{subject_digest}"]["response"] & {
            data: Endpoints["GET /orgs/{org}/attestations/{subject_digest}"]["response"]["data"]["attestations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/orgs/blocking#list-users-blocked-by-an-organization
     */
    "GET /orgs/{org}/blocks": {
        parameters: Endpoints["GET /orgs/{org}/blocks"]["parameters"];
        response: Endpoints["GET /orgs/{org}/blocks"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/campaigns/campaigns#list-campaigns-for-an-organization
     */
    "GET /orgs/{org}/campaigns": {
        parameters: Endpoints["GET /orgs/{org}/campaigns"]["parameters"];
        response: Endpoints["GET /orgs/{org}/campaigns"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/code-scanning/code-scanning#list-code-scanning-alerts-for-an-organization
     */
    "GET /orgs/{org}/code-scanning/alerts": {
        parameters: Endpoints["GET /orgs/{org}/code-scanning/alerts"]["parameters"];
        response: Endpoints["GET /orgs/{org}/code-scanning/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/code-security/configurations#get-code-security-configurations-for-an-organization
     */
    "GET /orgs/{org}/code-security/configurations": {
        parameters: Endpoints["GET /orgs/{org}/code-security/configurations"]["parameters"];
        response: Endpoints["GET /orgs/{org}/code-security/configurations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/code-security/configurations#get-repositories-associated-with-a-code-security-configuration
     */
    "GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/codespaces/organizations#list-codespaces-for-the-organization
     */
    "GET /orgs/{org}/codespaces": {
        parameters: Endpoints["GET /orgs/{org}/codespaces"]["parameters"];
        response: Endpoints["GET /orgs/{org}/codespaces"]["response"] & {
            data: Endpoints["GET /orgs/{org}/codespaces"]["response"]["data"]["codespaces"];
        };
    };
    /**
     * @see https://docs.github.com/rest/codespaces/organization-secrets#list-organization-secrets
     */
    "GET /orgs/{org}/codespaces/secrets": {
        parameters: Endpoints["GET /orgs/{org}/codespaces/secrets"]["parameters"];
        response: Endpoints["GET /orgs/{org}/codespaces/secrets"]["response"] & {
            data: Endpoints["GET /orgs/{org}/codespaces/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/codespaces/organization-secrets#list-selected-repositories-for-an-organization-secret
     */
    "GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories"]["response"] & {
            data: Endpoints["GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/copilot/copilot-user-management#list-all-copilot-seat-assignments-for-an-organization
     */
    "GET /orgs/{org}/copilot/billing/seats": {
        parameters: Endpoints["GET /orgs/{org}/copilot/billing/seats"]["parameters"];
        response: Endpoints["GET /orgs/{org}/copilot/billing/seats"]["response"] & {
            data: Endpoints["GET /orgs/{org}/copilot/billing/seats"]["response"]["data"]["seats"];
        };
    };
    /**
     * @see https://docs.github.com/rest/copilot/copilot-metrics#get-copilot-metrics-for-an-organization
     */
    "GET /orgs/{org}/copilot/metrics": {
        parameters: Endpoints["GET /orgs/{org}/copilot/metrics"]["parameters"];
        response: Endpoints["GET /orgs/{org}/copilot/metrics"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/dependabot/alerts#list-dependabot-alerts-for-an-organization
     */
    "GET /orgs/{org}/dependabot/alerts": {
        parameters: Endpoints["GET /orgs/{org}/dependabot/alerts"]["parameters"];
        response: Endpoints["GET /orgs/{org}/dependabot/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/dependabot/secrets#list-organization-secrets
     */
    "GET /orgs/{org}/dependabot/secrets": {
        parameters: Endpoints["GET /orgs/{org}/dependabot/secrets"]["parameters"];
        response: Endpoints["GET /orgs/{org}/dependabot/secrets"]["response"] & {
            data: Endpoints["GET /orgs/{org}/dependabot/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/dependabot/secrets#list-selected-repositories-for-an-organization-secret
     */
    "GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"]["response"] & {
            data: Endpoints["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-public-organization-events
     */
    "GET /orgs/{org}/events": {
        parameters: Endpoints["GET /orgs/{org}/events"]["parameters"];
        response: Endpoints["GET /orgs/{org}/events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/members#list-failed-organization-invitations
     */
    "GET /orgs/{org}/failed_invitations": {
        parameters: Endpoints["GET /orgs/{org}/failed_invitations"]["parameters"];
        response: Endpoints["GET /orgs/{org}/failed_invitations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/webhooks#list-organization-webhooks
     */
    "GET /orgs/{org}/hooks": {
        parameters: Endpoints["GET /orgs/{org}/hooks"]["parameters"];
        response: Endpoints["GET /orgs/{org}/hooks"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/webhooks#list-deliveries-for-an-organization-webhook
     */
    "GET /orgs/{org}/hooks/{hook_id}/deliveries": {
        parameters: Endpoints["GET /orgs/{org}/hooks/{hook_id}/deliveries"]["parameters"];
        response: Endpoints["GET /orgs/{org}/hooks/{hook_id}/deliveries"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/api-insights#get-route-stats-by-actor
     */
    "GET /orgs/{org}/insights/api/route-stats/{actor_type}/{actor_id}": {
        parameters: Endpoints["GET /orgs/{org}/insights/api/route-stats/{actor_type}/{actor_id}"]["parameters"];
        response: Endpoints["GET /orgs/{org}/insights/api/route-stats/{actor_type}/{actor_id}"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/api-insights#get-subject-stats
     */
    "GET /orgs/{org}/insights/api/subject-stats": {
        parameters: Endpoints["GET /orgs/{org}/insights/api/subject-stats"]["parameters"];
        response: Endpoints["GET /orgs/{org}/insights/api/subject-stats"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/api-insights#get-user-stats
     */
    "GET /orgs/{org}/insights/api/user-stats/{user_id}": {
        parameters: Endpoints["GET /orgs/{org}/insights/api/user-stats/{user_id}"]["parameters"];
        response: Endpoints["GET /orgs/{org}/insights/api/user-stats/{user_id}"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/orgs#list-app-installations-for-an-organization
     */
    "GET /orgs/{org}/installations": {
        parameters: Endpoints["GET /orgs/{org}/installations"]["parameters"];
        response: Endpoints["GET /orgs/{org}/installations"]["response"] & {
            data: Endpoints["GET /orgs/{org}/installations"]["response"]["data"]["installations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/orgs/members#list-pending-organization-invitations
     */
    "GET /orgs/{org}/invitations": {
        parameters: Endpoints["GET /orgs/{org}/invitations"]["parameters"];
        response: Endpoints["GET /orgs/{org}/invitations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/members#list-organization-invitation-teams
     */
    "GET /orgs/{org}/invitations/{invitation_id}/teams": {
        parameters: Endpoints["GET /orgs/{org}/invitations/{invitation_id}/teams"]["parameters"];
        response: Endpoints["GET /orgs/{org}/invitations/{invitation_id}/teams"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/issues#list-organization-issues-assigned-to-the-authenticated-user
     */
    "GET /orgs/{org}/issues": {
        parameters: Endpoints["GET /orgs/{org}/issues"]["parameters"];
        response: Endpoints["GET /orgs/{org}/issues"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/members#list-organization-members
     */
    "GET /orgs/{org}/members": {
        parameters: Endpoints["GET /orgs/{org}/members"]["parameters"];
        response: Endpoints["GET /orgs/{org}/members"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/codespaces/organizations#list-codespaces-for-a-user-in-organization
     */
    "GET /orgs/{org}/members/{username}/codespaces": {
        parameters: Endpoints["GET /orgs/{org}/members/{username}/codespaces"]["parameters"];
        response: Endpoints["GET /orgs/{org}/members/{username}/codespaces"]["response"] & {
            data: Endpoints["GET /orgs/{org}/members/{username}/codespaces"]["response"]["data"]["codespaces"];
        };
    };
    /**
     * @see https://docs.github.com/rest/migrations/orgs#list-organization-migrations
     */
    "GET /orgs/{org}/migrations": {
        parameters: Endpoints["GET /orgs/{org}/migrations"]["parameters"];
        response: Endpoints["GET /orgs/{org}/migrations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/migrations/orgs#list-repositories-in-an-organization-migration
     */
    "GET /orgs/{org}/migrations/{migration_id}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/migrations/{migration_id}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/migrations/{migration_id}/repositories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/organization-roles#list-teams-that-are-assigned-to-an-organization-role
     */
    "GET /orgs/{org}/organization-roles/{role_id}/teams": {
        parameters: Endpoints["GET /orgs/{org}/organization-roles/{role_id}/teams"]["parameters"];
        response: Endpoints["GET /orgs/{org}/organization-roles/{role_id}/teams"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/organization-roles#list-users-that-are-assigned-to-an-organization-role
     */
    "GET /orgs/{org}/organization-roles/{role_id}/users": {
        parameters: Endpoints["GET /orgs/{org}/organization-roles/{role_id}/users"]["parameters"];
        response: Endpoints["GET /orgs/{org}/organization-roles/{role_id}/users"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/outside-collaborators#list-outside-collaborators-for-an-organization
     */
    "GET /orgs/{org}/outside_collaborators": {
        parameters: Endpoints["GET /orgs/{org}/outside_collaborators"]["parameters"];
        response: Endpoints["GET /orgs/{org}/outside_collaborators"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/packages/packages#list-packages-for-an-organization
     */
    "GET /orgs/{org}/packages": {
        parameters: Endpoints["GET /orgs/{org}/packages"]["parameters"];
        response: Endpoints["GET /orgs/{org}/packages"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/packages/packages#list-package-versions-for-a-package-owned-by-an-organization
     */
    "GET /orgs/{org}/packages/{package_type}/{package_name}/versions": {
        parameters: Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"]["parameters"];
        response: Endpoints["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/personal-access-tokens#list-requests-to-access-organization-resources-with-fine-grained-personal-access-tokens
     */
    "GET /orgs/{org}/personal-access-token-requests": {
        parameters: Endpoints["GET /orgs/{org}/personal-access-token-requests"]["parameters"];
        response: Endpoints["GET /orgs/{org}/personal-access-token-requests"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/personal-access-tokens#list-repositories-requested-to-be-accessed-by-a-fine-grained-personal-access-token
     */
    "GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/personal-access-tokens#list-fine-grained-personal-access-tokens-with-access-to-organization-resources
     */
    "GET /orgs/{org}/personal-access-tokens": {
        parameters: Endpoints["GET /orgs/{org}/personal-access-tokens"]["parameters"];
        response: Endpoints["GET /orgs/{org}/personal-access-tokens"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/personal-access-tokens#list-repositories-a-fine-grained-personal-access-token-has-access-to
     */
    "GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories": {
        parameters: Endpoints["GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/private-registries/organization-configurations#list-private-registries-for-an-organization
     */
    "GET /orgs/{org}/private-registries": {
        parameters: Endpoints["GET /orgs/{org}/private-registries"]["parameters"];
        response: Endpoints["GET /orgs/{org}/private-registries"]["response"] & {
            data: Endpoints["GET /orgs/{org}/private-registries"]["response"]["data"]["configurations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/projects/projects#list-organization-projects
     */
    "GET /orgs/{org}/projects": {
        parameters: Endpoints["GET /orgs/{org}/projects"]["parameters"];
        response: Endpoints["GET /orgs/{org}/projects"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/custom-properties#list-custom-property-values-for-organization-repositories
     */
    "GET /orgs/{org}/properties/values": {
        parameters: Endpoints["GET /orgs/{org}/properties/values"]["parameters"];
        response: Endpoints["GET /orgs/{org}/properties/values"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/members#list-public-organization-members
     */
    "GET /orgs/{org}/public_members": {
        parameters: Endpoints["GET /orgs/{org}/public_members"]["parameters"];
        response: Endpoints["GET /orgs/{org}/public_members"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-organization-repositories
     */
    "GET /orgs/{org}/repos": {
        parameters: Endpoints["GET /orgs/{org}/repos"]["parameters"];
        response: Endpoints["GET /orgs/{org}/repos"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/rules#get-all-organization-repository-rulesets
     */
    "GET /orgs/{org}/rulesets": {
        parameters: Endpoints["GET /orgs/{org}/rulesets"]["parameters"];
        response: Endpoints["GET /orgs/{org}/rulesets"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/rule-suites#list-organization-rule-suites
     */
    "GET /orgs/{org}/rulesets/rule-suites": {
        parameters: Endpoints["GET /orgs/{org}/rulesets/rule-suites"]["parameters"];
        response: Endpoints["GET /orgs/{org}/rulesets/rule-suites"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/rules#get-organization-ruleset-history
     */
    "GET /orgs/{org}/rulesets/{ruleset_id}/history": {
        parameters: Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}/history"]["parameters"];
        response: Endpoints["GET /orgs/{org}/rulesets/{ruleset_id}/history"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/secret-scanning/secret-scanning#list-secret-scanning-alerts-for-an-organization
     */
    "GET /orgs/{org}/secret-scanning/alerts": {
        parameters: Endpoints["GET /orgs/{org}/secret-scanning/alerts"]["parameters"];
        response: Endpoints["GET /orgs/{org}/secret-scanning/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/security-advisories/repository-advisories#list-repository-security-advisories-for-an-organization
     */
    "GET /orgs/{org}/security-advisories": {
        parameters: Endpoints["GET /orgs/{org}/security-advisories"]["parameters"];
        response: Endpoints["GET /orgs/{org}/security-advisories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/network-configurations#list-hosted-compute-network-configurations-for-an-organization
     */
    "GET /orgs/{org}/settings/network-configurations": {
        parameters: Endpoints["GET /orgs/{org}/settings/network-configurations"]["parameters"];
        response: Endpoints["GET /orgs/{org}/settings/network-configurations"]["response"] & {
            data: Endpoints["GET /orgs/{org}/settings/network-configurations"]["response"]["data"]["network_configurations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/copilot/copilot-metrics#get-copilot-metrics-for-a-team
     */
    "GET /orgs/{org}/team/{team_slug}/copilot/metrics": {
        parameters: Endpoints["GET /orgs/{org}/team/{team_slug}/copilot/metrics"]["parameters"];
        response: Endpoints["GET /orgs/{org}/team/{team_slug}/copilot/metrics"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-teams
     */
    "GET /orgs/{org}/teams": {
        parameters: Endpoints["GET /orgs/{org}/teams"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/discussions#list-discussions
     */
    "GET /orgs/{org}/teams/{team_slug}/discussions": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/discussion-comments#list-discussion-comments
     */
    "GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-a-team-discussion-comment
     */
    "GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-a-team-discussion
     */
    "GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/members#list-pending-team-invitations
     */
    "GET /orgs/{org}/teams/{team_slug}/invitations": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/invitations"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/invitations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/members#list-team-members
     */
    "GET /orgs/{org}/teams/{team_slug}/members": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/members"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/members"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-team-projects
     */
    "GET /orgs/{org}/teams/{team_slug}/projects": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/projects"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/projects"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-team-repositories
     */
    "GET /orgs/{org}/teams/{team_slug}/repos": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/repos"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/repos"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-child-teams
     */
    "GET /orgs/{org}/teams/{team_slug}/teams": {
        parameters: Endpoints["GET /orgs/{org}/teams/{team_slug}/teams"]["parameters"];
        response: Endpoints["GET /orgs/{org}/teams/{team_slug}/teams"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/projects/cards#list-project-cards
     */
    "GET /projects/columns/{column_id}/cards": {
        parameters: Endpoints["GET /projects/columns/{column_id}/cards"]["parameters"];
        response: Endpoints["GET /projects/columns/{column_id}/cards"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/projects/collaborators#list-project-collaborators
     */
    "GET /projects/{project_id}/collaborators": {
        parameters: Endpoints["GET /projects/{project_id}/collaborators"]["parameters"];
        response: Endpoints["GET /projects/{project_id}/collaborators"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/projects/columns#list-project-columns
     */
    "GET /projects/{project_id}/columns": {
        parameters: Endpoints["GET /projects/{project_id}/columns"]["parameters"];
        response: Endpoints["GET /projects/{project_id}/columns"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/actions/artifacts#list-artifacts-for-a-repository
     */
    "GET /repos/{owner}/{repo}/actions/artifacts": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/artifacts"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/artifacts"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/artifacts"]["response"]["data"]["artifacts"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/cache#list-github-actions-caches-for-a-repository
     */
    "GET /repos/{owner}/{repo}/actions/caches": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/caches"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/caches"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/caches"]["response"]["data"]["actions_caches"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/secrets#list-repository-organization-secrets
     */
    "GET /repos/{owner}/{repo}/actions/organization-secrets": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/organization-secrets"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/organization-secrets"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/organization-secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/variables#list-repository-organization-variables
     */
    "GET /repos/{owner}/{repo}/actions/organization-variables": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/organization-variables"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/organization-variables"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/organization-variables"]["response"]["data"]["variables"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/self-hosted-runners#list-self-hosted-runners-for-a-repository
     */
    "GET /repos/{owner}/{repo}/actions/runners": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/runners"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/runners"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/runners"]["response"]["data"]["runners"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/workflow-runs#list-workflow-runs-for-a-repository
     */
    "GET /repos/{owner}/{repo}/actions/runs": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/runs"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/runs"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/runs"]["response"]["data"]["workflow_runs"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/artifacts#list-workflow-run-artifacts
     */
    "GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"]["response"]["data"]["artifacts"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/workflow-jobs#list-jobs-for-a-workflow-run-attempt
     */
    "GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"]["response"]["data"]["jobs"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/workflow-jobs#list-jobs-for-a-workflow-run
     */
    "GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"]["response"]["data"]["jobs"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/secrets#list-repository-secrets
     */
    "GET /repos/{owner}/{repo}/actions/secrets": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/secrets"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/secrets"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/variables#list-repository-variables
     */
    "GET /repos/{owner}/{repo}/actions/variables": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/variables"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/variables"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/variables"]["response"]["data"]["variables"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/workflows#list-repository-workflows
     */
    "GET /repos/{owner}/{repo}/actions/workflows": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/workflows"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/workflows"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/workflows"]["response"]["data"]["workflows"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/workflow-runs#list-workflow-runs-for-a-workflow
     */
    "GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"]["response"]["data"]["workflow_runs"];
        };
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-repository-activities
     */
    "GET /repos/{owner}/{repo}/activity": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/activity"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/activity"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/assignees#list-assignees
     */
    "GET /repos/{owner}/{repo}/assignees": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/assignees"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/assignees"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-attestations
     */
    "GET /repos/{owner}/{repo}/attestations/{subject_digest}": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/attestations/{subject_digest}"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/attestations/{subject_digest}"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/attestations/{subject_digest}"]["response"]["data"]["attestations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/branches/branches#list-branches
     */
    "GET /repos/{owner}/{repo}/branches": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/branches"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/branches"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/checks/runs#list-check-run-annotations
     */
    "GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/checks/runs#list-check-runs-in-a-check-suite
     */
    "GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"]["response"]["data"]["check_runs"];
        };
    };
    /**
     * @see https://docs.github.com/rest/reference/code-scanning#list-code-scanning-alerts-for-a-repository
     */
    "GET /repos/{owner}/{repo}/code-scanning/alerts": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/code-scanning/code-scanning#list-instances-of-a-code-scanning-alert
     */
    "GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/code-scanning/code-scanning#list-code-scanning-analyses-for-a-repository
     */
    "GET /repos/{owner}/{repo}/code-scanning/analyses": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/code-scanning/analyses"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/code-scanning/analyses"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/codespaces/codespaces#list-codespaces-in-a-repository-for-the-authenticated-user
     */
    "GET /repos/{owner}/{repo}/codespaces": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/codespaces"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/codespaces"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/codespaces"]["response"]["data"]["codespaces"];
        };
    };
    /**
     * @see https://docs.github.com/rest/codespaces/codespaces#list-devcontainer-configurations-in-a-repository-for-the-authenticated-user
     */
    "GET /repos/{owner}/{repo}/codespaces/devcontainers": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/codespaces/devcontainers"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/codespaces/devcontainers"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/codespaces/devcontainers"]["response"]["data"]["devcontainers"];
        };
    };
    /**
     * @see https://docs.github.com/rest/codespaces/repository-secrets#list-repository-secrets
     */
    "GET /repos/{owner}/{repo}/codespaces/secrets": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/codespaces/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/collaborators/collaborators#list-repository-collaborators
     */
    "GET /repos/{owner}/{repo}/collaborators": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/collaborators"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/collaborators"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/commits/comments#list-commit-comments-for-a-repository
     */
    "GET /repos/{owner}/{repo}/comments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/comments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-a-commit-comment
     */
    "GET /repos/{owner}/{repo}/comments/{comment_id}/reactions": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/comments/{comment_id}/reactions"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/comments/{comment_id}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/commits/commits#list-commits
     */
    "GET /repos/{owner}/{repo}/commits": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/commits"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/commits"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/commits/comments#list-commit-comments
     */
    "GET /repos/{owner}/{repo}/commits/{commit_sha}/comments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/comments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/commits/commits#list-pull-requests-associated-with-a-commit
     */
    "GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/checks/runs#list-check-runs-for-a-git-reference
     */
    "GET /repos/{owner}/{repo}/commits/{ref}/check-runs": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"]["response"]["data"]["check_runs"];
        };
    };
    /**
     * @see https://docs.github.com/rest/checks/suites#list-check-suites-for-a-git-reference
     */
    "GET /repos/{owner}/{repo}/commits/{ref}/check-suites": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"]["response"]["data"]["check_suites"];
        };
    };
    /**
     * @see https://docs.github.com/rest/commits/statuses#get-the-combined-status-for-a-specific-reference
     */
    "GET /repos/{owner}/{repo}/commits/{ref}/status": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/status"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/status"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/status"]["response"]["data"]["statuses"];
        };
    };
    /**
     * @see https://docs.github.com/rest/commits/statuses#list-commit-statuses-for-a-reference
     */
    "GET /repos/{owner}/{repo}/commits/{ref}/statuses": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/statuses"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/commits/{ref}/statuses"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/commits/commits#compare-two-commits
     */
    "GET /repos/{owner}/{repo}/compare/{basehead}": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/compare/{basehead}"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/compare/{basehead}"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/compare/{basehead}"]["response"]["data"]["commits"];
        };
    };
    /**
     * @see https://docs.github.com/rest/reference/repos#compare-two-commits
     */
    "GET /repos/{owner}/{repo}/compare/{base}...{head}": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/compare/{base}...{head}"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/compare/{base}...{head}"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/compare/{base}...{head}"]["response"]["data"]["commits"];
        };
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-repository-contributors
     */
    "GET /repos/{owner}/{repo}/contributors": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/contributors"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/contributors"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/dependabot/alerts#list-dependabot-alerts-for-a-repository
     */
    "GET /repos/{owner}/{repo}/dependabot/alerts": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/dependabot/alerts"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/dependabot/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/dependabot/secrets#list-repository-secrets
     */
    "GET /repos/{owner}/{repo}/dependabot/secrets": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/dependabot/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/deployments/deployments#list-deployments
     */
    "GET /repos/{owner}/{repo}/deployments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/deployments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/deployments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/deployments/statuses#list-deployment-statuses
     */
    "GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/deployments/environments#list-environments
     */
    "GET /repos/{owner}/{repo}/environments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/environments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/environments"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/environments"]["response"]["data"]["environments"];
        };
    };
    /**
     * @see https://docs.github.com/rest/deployments/branch-policies#list-deployment-branch-policies
     */
    "GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"]["response"]["data"]["branch_policies"];
        };
    };
    /**
     * @see https://docs.github.com/rest/deployments/protection-rules#list-custom-deployment-rule-integrations-available-for-an-environment
     */
    "GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps"]["response"]["data"]["available_custom_deployment_protection_rule_integrations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/secrets#list-environment-secrets
     */
    "GET /repos/{owner}/{repo}/environments/{environment_name}/secrets": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/actions/variables#list-environment-variables
     */
    "GET /repos/{owner}/{repo}/environments/{environment_name}/variables": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/variables"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/variables"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/environments/{environment_name}/variables"]["response"]["data"]["variables"];
        };
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-repository-events
     */
    "GET /repos/{owner}/{repo}/events": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/events"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/forks#list-forks
     */
    "GET /repos/{owner}/{repo}/forks": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/forks"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/forks"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/webhooks#list-repository-webhooks
     */
    "GET /repos/{owner}/{repo}/hooks": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/hooks"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/hooks"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/webhooks#list-deliveries-for-a-repository-webhook
     */
    "GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/collaborators/invitations#list-repository-invitations
     */
    "GET /repos/{owner}/{repo}/invitations": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/invitations"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/invitations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/issues#list-repository-issues
     */
    "GET /repos/{owner}/{repo}/issues": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/comments#list-issue-comments-for-a-repository
     */
    "GET /repos/{owner}/{repo}/issues/comments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/comments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-an-issue-comment
     */
    "GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/events#list-issue-events-for-a-repository
     */
    "GET /repos/{owner}/{repo}/issues/events": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/events"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/comments#list-issue-comments
     */
    "GET /repos/{owner}/{repo}/issues/{issue_number}/comments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/comments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/events#list-issue-events
     */
    "GET /repos/{owner}/{repo}/issues/{issue_number}/events": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/events"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/labels#list-labels-for-an-issue
     */
    "GET /repos/{owner}/{repo}/issues/{issue_number}/labels": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/labels"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/labels"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-an-issue
     */
    "GET /repos/{owner}/{repo}/issues/{issue_number}/reactions": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/reactions"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/sub-issues#list-sub-issues
     */
    "GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/timeline#list-timeline-events-for-an-issue
     */
    "GET /repos/{owner}/{repo}/issues/{issue_number}/timeline": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/timeline"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/issues/{issue_number}/timeline"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/deploy-keys/deploy-keys#list-deploy-keys
     */
    "GET /repos/{owner}/{repo}/keys": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/keys"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/keys"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/labels#list-labels-for-a-repository
     */
    "GET /repos/{owner}/{repo}/labels": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/labels"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/labels"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/milestones#list-milestones
     */
    "GET /repos/{owner}/{repo}/milestones": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/milestones"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/milestones"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/issues/labels#list-labels-for-issues-in-a-milestone
     */
    "GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/notifications#list-repository-notifications-for-the-authenticated-user
     */
    "GET /repos/{owner}/{repo}/notifications": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/notifications"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/notifications"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pages/pages#list-apiname-pages-builds
     */
    "GET /repos/{owner}/{repo}/pages/builds": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pages/builds"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pages/builds"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/projects/projects#list-repository-projects
     */
    "GET /repos/{owner}/{repo}/projects": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/projects"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/projects"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pulls/pulls#list-pull-requests
     */
    "GET /repos/{owner}/{repo}/pulls": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pulls/comments#list-review-comments-in-a-repository
     */
    "GET /repos/{owner}/{repo}/pulls/comments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls/comments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-a-pull-request-review-comment
     */
    "GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pulls/comments#list-review-comments-on-a-pull-request
     */
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/comments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/comments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pulls/pulls#list-commits-on-a-pull-request
     */
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/commits": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/commits"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/commits"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pulls/pulls#list-pull-requests-files
     */
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/files": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/files"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/files"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pulls/reviews#list-reviews-for-a-pull-request
     */
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/pulls/reviews#list-comments-for-a-pull-request-review
     */
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/releases/releases#list-releases
     */
    "GET /repos/{owner}/{repo}/releases": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/releases"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/releases"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/releases/assets#list-release-assets
     */
    "GET /repos/{owner}/{repo}/releases/{release_id}/assets": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/assets"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/assets"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-a-release
     */
    "GET /repos/{owner}/{repo}/releases/{release_id}/reactions": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/reactions"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/releases/{release_id}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/rules#get-rules-for-a-branch
     */
    "GET /repos/{owner}/{repo}/rules/branches/{branch}": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/rules/branches/{branch}"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/rules/branches/{branch}"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/rules#get-all-repository-rulesets
     */
    "GET /repos/{owner}/{repo}/rulesets": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/rulesets"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/rulesets"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/rule-suites#list-repository-rule-suites
     */
    "GET /repos/{owner}/{repo}/rulesets/rule-suites": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/rulesets/rule-suites"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/rulesets/rule-suites"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/rules#get-repository-ruleset-history
     */
    "GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/secret-scanning/secret-scanning#list-secret-scanning-alerts-for-a-repository
     */
    "GET /repos/{owner}/{repo}/secret-scanning/alerts": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/secret-scanning/secret-scanning#list-locations-for-a-secret-scanning-alert
     */
    "GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/security-advisories/repository-advisories#list-repository-security-advisories
     */
    "GET /repos/{owner}/{repo}/security-advisories": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/security-advisories"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/security-advisories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/starring#list-stargazers
     */
    "GET /repos/{owner}/{repo}/stargazers": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/stargazers"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/stargazers"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/watching#list-watchers
     */
    "GET /repos/{owner}/{repo}/subscribers": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/subscribers"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/subscribers"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-repository-tags
     */
    "GET /repos/{owner}/{repo}/tags": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/tags"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/tags"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-repository-teams
     */
    "GET /repos/{owner}/{repo}/teams": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/teams"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/teams"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#get-all-repository-topics
     */
    "GET /repos/{owner}/{repo}/topics": {
        parameters: Endpoints["GET /repos/{owner}/{repo}/topics"]["parameters"];
        response: Endpoints["GET /repos/{owner}/{repo}/topics"]["response"] & {
            data: Endpoints["GET /repos/{owner}/{repo}/topics"]["response"]["data"]["names"];
        };
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-public-repositories
     */
    "GET /repositories": {
        parameters: Endpoints["GET /repositories"]["parameters"];
        response: Endpoints["GET /repositories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/search/search#search-code
     */
    "GET /search/code": {
        parameters: Endpoints["GET /search/code"]["parameters"];
        response: Endpoints["GET /search/code"]["response"] & {
            data: Endpoints["GET /search/code"]["response"]["data"]["items"];
        };
    };
    /**
     * @see https://docs.github.com/rest/search/search#search-commits
     */
    "GET /search/commits": {
        parameters: Endpoints["GET /search/commits"]["parameters"];
        response: Endpoints["GET /search/commits"]["response"] & {
            data: Endpoints["GET /search/commits"]["response"]["data"]["items"];
        };
    };
    /**
     * @see https://docs.github.com/rest/search/search#search-issues-and-pull-requests
     */
    "GET /search/issues": {
        parameters: Endpoints["GET /search/issues"]["parameters"];
        response: Endpoints["GET /search/issues"]["response"] & {
            data: Endpoints["GET /search/issues"]["response"]["data"]["items"];
        };
    };
    /**
     * @see https://docs.github.com/rest/search/search#search-labels
     */
    "GET /search/labels": {
        parameters: Endpoints["GET /search/labels"]["parameters"];
        response: Endpoints["GET /search/labels"]["response"] & {
            data: Endpoints["GET /search/labels"]["response"]["data"]["items"];
        };
    };
    /**
     * @see https://docs.github.com/rest/search/search#search-repositories
     */
    "GET /search/repositories": {
        parameters: Endpoints["GET /search/repositories"]["parameters"];
        response: Endpoints["GET /search/repositories"]["response"] & {
            data: Endpoints["GET /search/repositories"]["response"]["data"]["items"];
        };
    };
    /**
     * @see https://docs.github.com/rest/search/search#search-topics
     */
    "GET /search/topics": {
        parameters: Endpoints["GET /search/topics"]["parameters"];
        response: Endpoints["GET /search/topics"]["response"] & {
            data: Endpoints["GET /search/topics"]["response"]["data"]["items"];
        };
    };
    /**
     * @see https://docs.github.com/rest/search/search#search-users
     */
    "GET /search/users": {
        parameters: Endpoints["GET /search/users"]["parameters"];
        response: Endpoints["GET /search/users"]["response"] & {
            data: Endpoints["GET /search/users"]["response"]["data"]["items"];
        };
    };
    /**
     * @see https://docs.github.com/rest/teams/discussions#list-discussions-legacy
     */
    "GET /teams/{team_id}/discussions": {
        parameters: Endpoints["GET /teams/{team_id}/discussions"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/discussions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/discussion-comments#list-discussion-comments-legacy
     */
    "GET /teams/{team_id}/discussions/{discussion_number}/comments": {
        parameters: Endpoints["GET /teams/{team_id}/discussions/{discussion_number}/comments"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/discussions/{discussion_number}/comments"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-a-team-discussion-comment-legacy
     */
    "GET /teams/{team_id}/discussions/{discussion_number}/comments/{comment_number}/reactions": {
        parameters: Endpoints["GET /teams/{team_id}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/discussions/{discussion_number}/comments/{comment_number}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/reactions/reactions#list-reactions-for-a-team-discussion-legacy
     */
    "GET /teams/{team_id}/discussions/{discussion_number}/reactions": {
        parameters: Endpoints["GET /teams/{team_id}/discussions/{discussion_number}/reactions"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/discussions/{discussion_number}/reactions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/members#list-pending-team-invitations-legacy
     */
    "GET /teams/{team_id}/invitations": {
        parameters: Endpoints["GET /teams/{team_id}/invitations"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/invitations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/members#list-team-members-legacy
     */
    "GET /teams/{team_id}/members": {
        parameters: Endpoints["GET /teams/{team_id}/members"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/members"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-team-projects-legacy
     */
    "GET /teams/{team_id}/projects": {
        parameters: Endpoints["GET /teams/{team_id}/projects"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/projects"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-team-repositories-legacy
     */
    "GET /teams/{team_id}/repos": {
        parameters: Endpoints["GET /teams/{team_id}/repos"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/repos"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-child-teams-legacy
     */
    "GET /teams/{team_id}/teams": {
        parameters: Endpoints["GET /teams/{team_id}/teams"]["parameters"];
        response: Endpoints["GET /teams/{team_id}/teams"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/blocking#list-users-blocked-by-the-authenticated-user
     */
    "GET /user/blocks": {
        parameters: Endpoints["GET /user/blocks"]["parameters"];
        response: Endpoints["GET /user/blocks"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/codespaces/codespaces#list-codespaces-for-the-authenticated-user
     */
    "GET /user/codespaces": {
        parameters: Endpoints["GET /user/codespaces"]["parameters"];
        response: Endpoints["GET /user/codespaces"]["response"] & {
            data: Endpoints["GET /user/codespaces"]["response"]["data"]["codespaces"];
        };
    };
    /**
     * @see https://docs.github.com/rest/codespaces/secrets#list-secrets-for-the-authenticated-user
     */
    "GET /user/codespaces/secrets": {
        parameters: Endpoints["GET /user/codespaces/secrets"]["parameters"];
        response: Endpoints["GET /user/codespaces/secrets"]["response"] & {
            data: Endpoints["GET /user/codespaces/secrets"]["response"]["data"]["secrets"];
        };
    };
    /**
     * @see https://docs.github.com/rest/users/emails#list-email-addresses-for-the-authenticated-user
     */
    "GET /user/emails": {
        parameters: Endpoints["GET /user/emails"]["parameters"];
        response: Endpoints["GET /user/emails"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/followers#list-followers-of-the-authenticated-user
     */
    "GET /user/followers": {
        parameters: Endpoints["GET /user/followers"]["parameters"];
        response: Endpoints["GET /user/followers"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/followers#list-the-people-the-authenticated-user-follows
     */
    "GET /user/following": {
        parameters: Endpoints["GET /user/following"]["parameters"];
        response: Endpoints["GET /user/following"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/gpg-keys#list-gpg-keys-for-the-authenticated-user
     */
    "GET /user/gpg_keys": {
        parameters: Endpoints["GET /user/gpg_keys"]["parameters"];
        response: Endpoints["GET /user/gpg_keys"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/installations#list-app-installations-accessible-to-the-user-access-token
     */
    "GET /user/installations": {
        parameters: Endpoints["GET /user/installations"]["parameters"];
        response: Endpoints["GET /user/installations"]["response"] & {
            data: Endpoints["GET /user/installations"]["response"]["data"]["installations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/apps/installations#list-repositories-accessible-to-the-user-access-token
     */
    "GET /user/installations/{installation_id}/repositories": {
        parameters: Endpoints["GET /user/installations/{installation_id}/repositories"]["parameters"];
        response: Endpoints["GET /user/installations/{installation_id}/repositories"]["response"] & {
            data: Endpoints["GET /user/installations/{installation_id}/repositories"]["response"]["data"]["repositories"];
        };
    };
    /**
     * @see https://docs.github.com/rest/issues/issues#list-user-account-issues-assigned-to-the-authenticated-user
     */
    "GET /user/issues": {
        parameters: Endpoints["GET /user/issues"]["parameters"];
        response: Endpoints["GET /user/issues"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/keys#list-public-ssh-keys-for-the-authenticated-user
     */
    "GET /user/keys": {
        parameters: Endpoints["GET /user/keys"]["parameters"];
        response: Endpoints["GET /user/keys"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/marketplace#list-subscriptions-for-the-authenticated-user
     */
    "GET /user/marketplace_purchases": {
        parameters: Endpoints["GET /user/marketplace_purchases"]["parameters"];
        response: Endpoints["GET /user/marketplace_purchases"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/apps/marketplace#list-subscriptions-for-the-authenticated-user-stubbed
     */
    "GET /user/marketplace_purchases/stubbed": {
        parameters: Endpoints["GET /user/marketplace_purchases/stubbed"]["parameters"];
        response: Endpoints["GET /user/marketplace_purchases/stubbed"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/members#list-organization-memberships-for-the-authenticated-user
     */
    "GET /user/memberships/orgs": {
        parameters: Endpoints["GET /user/memberships/orgs"]["parameters"];
        response: Endpoints["GET /user/memberships/orgs"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/migrations/users#list-user-migrations
     */
    "GET /user/migrations": {
        parameters: Endpoints["GET /user/migrations"]["parameters"];
        response: Endpoints["GET /user/migrations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/migrations/users#list-repositories-for-a-user-migration
     */
    "GET /user/migrations/{migration_id}/repositories": {
        parameters: Endpoints["GET /user/migrations/{migration_id}/repositories"]["parameters"];
        response: Endpoints["GET /user/migrations/{migration_id}/repositories"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/orgs#list-organizations-for-the-authenticated-user
     */
    "GET /user/orgs": {
        parameters: Endpoints["GET /user/orgs"]["parameters"];
        response: Endpoints["GET /user/orgs"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/packages/packages#list-packages-for-the-authenticated-users-namespace
     */
    "GET /user/packages": {
        parameters: Endpoints["GET /user/packages"]["parameters"];
        response: Endpoints["GET /user/packages"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/packages/packages#list-package-versions-for-a-package-owned-by-the-authenticated-user
     */
    "GET /user/packages/{package_type}/{package_name}/versions": {
        parameters: Endpoints["GET /user/packages/{package_type}/{package_name}/versions"]["parameters"];
        response: Endpoints["GET /user/packages/{package_type}/{package_name}/versions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/emails#list-public-email-addresses-for-the-authenticated-user
     */
    "GET /user/public_emails": {
        parameters: Endpoints["GET /user/public_emails"]["parameters"];
        response: Endpoints["GET /user/public_emails"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-repositories-for-the-authenticated-user
     */
    "GET /user/repos": {
        parameters: Endpoints["GET /user/repos"]["parameters"];
        response: Endpoints["GET /user/repos"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/collaborators/invitations#list-repository-invitations-for-the-authenticated-user
     */
    "GET /user/repository_invitations": {
        parameters: Endpoints["GET /user/repository_invitations"]["parameters"];
        response: Endpoints["GET /user/repository_invitations"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/social-accounts#list-social-accounts-for-the-authenticated-user
     */
    "GET /user/social_accounts": {
        parameters: Endpoints["GET /user/social_accounts"]["parameters"];
        response: Endpoints["GET /user/social_accounts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/ssh-signing-keys#list-ssh-signing-keys-for-the-authenticated-user
     */
    "GET /user/ssh_signing_keys": {
        parameters: Endpoints["GET /user/ssh_signing_keys"]["parameters"];
        response: Endpoints["GET /user/ssh_signing_keys"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/starring#list-repositories-starred-by-the-authenticated-user
     */
    "GET /user/starred": {
        parameters: Endpoints["GET /user/starred"]["parameters"];
        response: Endpoints["GET /user/starred"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/watching#list-repositories-watched-by-the-authenticated-user
     */
    "GET /user/subscriptions": {
        parameters: Endpoints["GET /user/subscriptions"]["parameters"];
        response: Endpoints["GET /user/subscriptions"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/teams/teams#list-teams-for-the-authenticated-user
     */
    "GET /user/teams": {
        parameters: Endpoints["GET /user/teams"]["parameters"];
        response: Endpoints["GET /user/teams"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/users#list-users
     */
    "GET /users": {
        parameters: Endpoints["GET /users"]["parameters"];
        response: Endpoints["GET /users"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/attestations#list-attestations
     */
    "GET /users/{username}/attestations/{subject_digest}": {
        parameters: Endpoints["GET /users/{username}/attestations/{subject_digest}"]["parameters"];
        response: Endpoints["GET /users/{username}/attestations/{subject_digest}"]["response"] & {
            data: Endpoints["GET /users/{username}/attestations/{subject_digest}"]["response"]["data"]["attestations"];
        };
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-events-for-the-authenticated-user
     */
    "GET /users/{username}/events": {
        parameters: Endpoints["GET /users/{username}/events"]["parameters"];
        response: Endpoints["GET /users/{username}/events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-organization-events-for-the-authenticated-user
     */
    "GET /users/{username}/events/orgs/{org}": {
        parameters: Endpoints["GET /users/{username}/events/orgs/{org}"]["parameters"];
        response: Endpoints["GET /users/{username}/events/orgs/{org}"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-public-events-for-a-user
     */
    "GET /users/{username}/events/public": {
        parameters: Endpoints["GET /users/{username}/events/public"]["parameters"];
        response: Endpoints["GET /users/{username}/events/public"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/followers#list-followers-of-a-user
     */
    "GET /users/{username}/followers": {
        parameters: Endpoints["GET /users/{username}/followers"]["parameters"];
        response: Endpoints["GET /users/{username}/followers"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/followers#list-the-people-a-user-follows
     */
    "GET /users/{username}/following": {
        parameters: Endpoints["GET /users/{username}/following"]["parameters"];
        response: Endpoints["GET /users/{username}/following"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/gists/gists#list-gists-for-a-user
     */
    "GET /users/{username}/gists": {
        parameters: Endpoints["GET /users/{username}/gists"]["parameters"];
        response: Endpoints["GET /users/{username}/gists"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/gpg-keys#list-gpg-keys-for-a-user
     */
    "GET /users/{username}/gpg_keys": {
        parameters: Endpoints["GET /users/{username}/gpg_keys"]["parameters"];
        response: Endpoints["GET /users/{username}/gpg_keys"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/keys#list-public-keys-for-a-user
     */
    "GET /users/{username}/keys": {
        parameters: Endpoints["GET /users/{username}/keys"]["parameters"];
        response: Endpoints["GET /users/{username}/keys"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/orgs/orgs#list-organizations-for-a-user
     */
    "GET /users/{username}/orgs": {
        parameters: Endpoints["GET /users/{username}/orgs"]["parameters"];
        response: Endpoints["GET /users/{username}/orgs"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/packages/packages#list-packages-for-a-user
     */
    "GET /users/{username}/packages": {
        parameters: Endpoints["GET /users/{username}/packages"]["parameters"];
        response: Endpoints["GET /users/{username}/packages"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/projects/projects#list-user-projects
     */
    "GET /users/{username}/projects": {
        parameters: Endpoints["GET /users/{username}/projects"]["parameters"];
        response: Endpoints["GET /users/{username}/projects"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-events-received-by-the-authenticated-user
     */
    "GET /users/{username}/received_events": {
        parameters: Endpoints["GET /users/{username}/received_events"]["parameters"];
        response: Endpoints["GET /users/{username}/received_events"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/events#list-public-events-received-by-a-user
     */
    "GET /users/{username}/received_events/public": {
        parameters: Endpoints["GET /users/{username}/received_events/public"]["parameters"];
        response: Endpoints["GET /users/{username}/received_events/public"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/repos/repos#list-repositories-for-a-user
     */
    "GET /users/{username}/repos": {
        parameters: Endpoints["GET /users/{username}/repos"]["parameters"];
        response: Endpoints["GET /users/{username}/repos"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/social-accounts#list-social-accounts-for-a-user
     */
    "GET /users/{username}/social_accounts": {
        parameters: Endpoints["GET /users/{username}/social_accounts"]["parameters"];
        response: Endpoints["GET /users/{username}/social_accounts"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/users/ssh-signing-keys#list-ssh-signing-keys-for-a-user
     */
    "GET /users/{username}/ssh_signing_keys": {
        parameters: Endpoints["GET /users/{username}/ssh_signing_keys"]["parameters"];
        response: Endpoints["GET /users/{username}/ssh_signing_keys"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/starring#list-repositories-starred-by-a-user
     */
    "GET /users/{username}/starred": {
        parameters: Endpoints["GET /users/{username}/starred"]["parameters"];
        response: Endpoints["GET /users/{username}/starred"]["response"];
    };
    /**
     * @see https://docs.github.com/rest/activity/watching#list-repositories-watched-by-a-user
     */
    "GET /users/{username}/subscriptions": {
        parameters: Endpoints["GET /users/{username}/subscriptions"]["parameters"];
        response: Endpoints["GET /users/{username}/subscriptions"]["response"];
    };
}
export declare const paginatingEndpoints: (keyof PaginatingEndpoints)[];
