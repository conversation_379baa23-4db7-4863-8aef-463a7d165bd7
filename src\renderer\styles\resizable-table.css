/* 可调整大小表格样式 */
.resizable-table .react-resizable {
  position: relative;
  background-clip: padding-box;
}

.resizable-table .react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 1;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}

.resizable-table .react-resizable-handle:hover {
  background-color: #1890ff;
  opacity: 0.3;
}

.resizable-table .react-resizable-handle::after {
  content: '';
  position: absolute;
  right: 3px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background-color: #d9d9d9;
}

.resizable-table .react-resizable-handle:hover::after {
  background-color: #1890ff;
}

/* 表格排序图标样式 */
.resizable-table .ant-table-column-sorter {
  margin-left: 4px;
}

.resizable-table .ant-table-column-sorter-up.active,
.resizable-table .ant-table-column-sorter-down.active {
  color: #1890ff;
}

/* 表格头部样式 */
.resizable-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
}

.resizable-table .ant-table-thead > tr > th:hover {
  background-color: #f5f5f5;
}

/* 表格行样式 */
.resizable-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.resizable-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resizable-table .react-resizable-handle {
    width: 15px;
    right: -7px;
  }
}
