/* 可调整大小表格样式 - 深色主题 */
.resizable-table {
  background-color: #1f1f1f;
  color: #ffffff;
}

/* 固定表格布局，防止列宽自动调整 */
.resizable-table .ant-table {
  table-layout: fixed;
  width: 100%;
}

.resizable-table .ant-table-thead > tr > th,
.resizable-table .ant-table-tbody > tr > td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
}

/* 确保表格列宽度严格按照设置的值 */
.resizable-table .ant-table-thead > tr > th {
  padding: 8px 12px;
  border-right: 1px solid #303030;
}

.resizable-table .ant-table-tbody > tr > td {
  padding: 8px 12px;
  border-right: 1px solid #303030;
}

/* 最后一列不显示右边框 */
.resizable-table .ant-table-thead > tr > th:last-child,
.resizable-table .ant-table-tbody > tr > td:last-child {
  border-right: none;
}

/* 自定义拖拽手柄样式 */
.resizable-table .custom-resize-handle {
  position: absolute;
  right: -5px;
  top: 0;
  bottom: 0;
  width: 10px;
  cursor: col-resize;
  z-index: 10;
  background: transparent;
  border: none;
  outline: none;
}

.resizable-table .custom-resize-handle:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.resizable-table .custom-resize-handle::after {
  content: '';
  position: absolute;
  right: 3px;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.resizable-table .custom-resize-handle:hover::after {
  background-color: #1890ff;
}

/* 拖拽时的视觉反馈 */
.resizable-table .custom-resize-handle:active::after {
  background-color: #40a9ff;
  width: 3px;
  right: 2px;
}

/* 表格排序图标样式 */
.resizable-table .ant-table-column-sorter {
  margin-left: 4px;
}

.resizable-table .ant-table-column-sorter-up.active,
.resizable-table .ant-table-column-sorter-down.active {
  color: #1890ff;
}

.resizable-table .ant-table-column-sorter-up,
.resizable-table .ant-table-column-sorter-down {
  color: #8c8c8c;
}

/* 表格头部样式 - 深色主题 */
.resizable-table .ant-table-thead > tr > th {
  background-color: #262626;
  color: #ffffff;
  font-weight: 600;
  border-bottom: 2px solid #434343;
}

.resizable-table .ant-table-thead > tr > th:hover {
  background-color: #303030;
}

/* 表格行样式 - 深色主题 */
.resizable-table .ant-table-tbody > tr > td {
  background-color: #1f1f1f;
  color: #ffffff;
  border-bottom: 1px solid #303030;
}

.resizable-table .ant-table-tbody > tr:hover > td {
  background-color: #262626;
}

/* 表格容器样式 */
.resizable-table .ant-table {
  background-color: #1f1f1f;
  color: #ffffff;
}

.resizable-table .ant-table-container {
  background-color: #1f1f1f;
}

/* 表格滚动条样式 */
.resizable-table .ant-table-body {
  scrollbar-width: thin;
  scrollbar-color: #434343 #1f1f1f;
}

.resizable-table .ant-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.resizable-table .ant-table-body::-webkit-scrollbar-track {
  background: #1f1f1f;
}

.resizable-table .ant-table-body::-webkit-scrollbar-thumb {
  background-color: #434343;
  border-radius: 4px;
}

.resizable-table .ant-table-body::-webkit-scrollbar-thumb:hover {
  background-color: #595959;
}

/* 防止表格内容溢出 */
.resizable-table .ant-table-cell {
  padding: 8px 12px;
  word-break: break-word;
  overflow-wrap: break-word;
}

/* 分页器样式 */
.resizable-table .ant-pagination {
  color: #ffffff;
}

.resizable-table .ant-pagination .ant-pagination-item {
  background-color: #262626;
  border-color: #434343;
}

.resizable-table .ant-pagination .ant-pagination-item a {
  color: #ffffff;
}

.resizable-table .ant-pagination .ant-pagination-item:hover {
  border-color: #1890ff;
}

.resizable-table .ant-pagination .ant-pagination-item-active {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 加载状态样式 */
.resizable-table .ant-spin-container {
  background-color: #1f1f1f;
}

.resizable-table .ant-spin-dot-item {
  background-color: #1890ff;
}

/* 空状态样式 */
.resizable-table .ant-empty {
  color: #8c8c8c;
}

.resizable-table .ant-empty-description {
  color: #8c8c8c;
}

/* 选择器样式 */
.resizable-table .ant-select {
  color: #ffffff;
}

.resizable-table .ant-select-selector {
  background-color: #262626;
  border-color: #434343;
  color: #ffffff;
}

.resizable-table .ant-select-arrow {
  color: #8c8c8c;
}

/* 输入框样式 */
.resizable-table .ant-input {
  background-color: #262626;
  border-color: #434343;
  color: #ffffff;
}

.resizable-table .ant-input:focus,
.resizable-table .ant-input:hover {
  border-color: #1890ff;
}

/* 按钮样式 */
.resizable-table .ant-btn {
  border-color: #434343;
  color: #ffffff;
}

.resizable-table .ant-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.resizable-table .ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 标签样式 */
.resizable-table .ant-tag {
  border-color: #434343;
}

/* 开关样式 */
.resizable-table .ant-switch {
  background-color: #434343;
}

.resizable-table .ant-switch-checked {
  background-color: #1890ff;
}

/* 评分样式 */
.resizable-table .ant-rate-star {
  color: #434343;
}

.resizable-table .ant-rate-star.ant-rate-star-full {
  color: #faad14;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resizable-table .react-resizable-handle {
    width: 15px;
    right: -7px;
  }
}
