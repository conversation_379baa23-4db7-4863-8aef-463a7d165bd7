{"version": 3, "sources": ["../dist-src/is-jwt.js", "../dist-src/auth.js", "../dist-src/with-authorization-prefix.js", "../dist-src/hook.js", "../dist-src/index.js"], "sourcesContent": ["const b64url = \"(?:[a-zA-Z0-9_-]+)\";\nconst sep = \"\\\\.\";\nconst jwtRE = new RegExp(`^${b64url}${sep}${b64url}${sep}${b64url}$`);\nconst isJWT = jwtRE.test.bind(jwtRE);\nexport {\n  isJWT\n};\n", "import { isJWT } from \"./is-jwt.js\";\nasync function auth(token) {\n  const isApp = isJWT(token);\n  const isInstallation = token.startsWith(\"v1.\") || token.startsWith(\"ghs_\");\n  const isUserToServer = token.startsWith(\"ghu_\");\n  const tokenType = isApp ? \"app\" : isInstallation ? \"installation\" : isUserToServer ? \"user-to-server\" : \"oauth\";\n  return {\n    type: \"token\",\n    token,\n    tokenType\n  };\n}\nexport {\n  auth\n};\n", "function withAuthorizationPrefix(token) {\n  if (token.split(/\\./).length === 3) {\n    return `bearer ${token}`;\n  }\n  return `token ${token}`;\n}\nexport {\n  withAuthorizationPrefix\n};\n", "import { withAuthorizationPrefix } from \"./with-authorization-prefix.js\";\nasync function hook(token, request, route, parameters) {\n  const endpoint = request.endpoint.merge(\n    route,\n    parameters\n  );\n  endpoint.headers.authorization = withAuthorizationPrefix(token);\n  return request(endpoint);\n}\nexport {\n  hook\n};\n", "import { auth } from \"./auth.js\";\nimport { hook } from \"./hook.js\";\nconst createTokenAuth = function createTokenAuth2(token) {\n  if (!token) {\n    throw new Error(\"[@octokit/auth-token] No token passed to createTokenAuth\");\n  }\n  if (typeof token !== \"string\") {\n    throw new Error(\n      \"[@octokit/auth-token] Token passed to createTokenAuth is not a string\"\n    );\n  }\n  token = token.replace(/^(token|bearer) +/i, \"\");\n  return Object.assign(auth.bind(null, token), {\n    hook: hook.bind(null, token)\n  });\n};\nexport {\n  createTokenAuth\n};\n"], "mappings": ";AAAA,IAAM,SAAS;AACf,IAAM,MAAM;AACZ,IAAM,QAAQ,IAAI,OAAO,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG;AACpE,IAAM,QAAQ,MAAM,KAAK,KAAK,KAAK;;;ACFnC,eAAe,KAAK,OAAO;AACzB,QAAM,QAAQ,MAAM,KAAK;AACzB,QAAM,iBAAiB,MAAM,WAAW,KAAK,KAAK,MAAM,WAAW,MAAM;AACzE,QAAM,iBAAiB,MAAM,WAAW,MAAM;AAC9C,QAAM,YAAY,QAAQ,QAAQ,iBAAiB,iBAAiB,iBAAiB,mBAAmB;AACxG,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;;;ACXA,SAAS,wBAAwB,OAAO;AACtC,MAAI,MAAM,MAAM,IAAI,EAAE,WAAW,GAAG;AAClC,WAAO,UAAU,KAAK;AAAA,EACxB;AACA,SAAO,SAAS,KAAK;AACvB;;;ACJA,eAAe,KAAK,OAAO,SAAS,OAAO,YAAY;AACrD,QAAM,WAAW,QAAQ,SAAS;AAAA,IAChC;AAAA,IACA;AAAA,EACF;AACA,WAAS,QAAQ,gBAAgB,wBAAwB,KAAK;AAC9D,SAAO,QAAQ,QAAQ;AACzB;;;ACNA,IAAM,kBAAkB,SAAS,iBAAiB,OAAO;AACvD,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC5E;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,UAAQ,MAAM,QAAQ,sBAAsB,EAAE;AAC9C,SAAO,OAAO,OAAO,KAAK,KAAK,MAAM,KAAK,GAAG;AAAA,IAC3C,MAAM,KAAK,KAAK,MAAM,KAAK;AAAA,EAC7B,CAAC;AACH;", "names": []}