{"name": "@octokit/plugin-request-log", "version": "6.0.0", "description": "Log all requests and request errors", "type": "module", "repository": "github:octokit/plugin-request-log.js", "keywords": ["github", "api", "sdk", "toolkit"], "author": "<PERSON> (https://twitter.com/gr2m)", "license": "MIT", "peerDependencies": {"@octokit/core": ">=6"}, "devDependencies": {"@octokit/core": "^7.0.0", "@octokit/tsconfig": "^4.0.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^3.1.4", "esbuild": "^0.25.0", "fetch-mock": "^11.0.0", "prettier": "3.5.3", "semantic-release-plugin-update-version-in-files": "^2.0.0", "tinyglobby": "^0.2.13", "typescript": "^5.0.0", "vitest": "^3.1.4"}, "publishConfig": {"access": "public", "provenance": true}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-src/index.js", "default": "./dist-src/index.js"}}, "sideEffects": false}