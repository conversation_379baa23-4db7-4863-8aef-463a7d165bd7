"use strict";
/**
 * Electron主进程入口文件
 * 负责创建应用窗口和管理应用生命周期
 */
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const window_1 = require("./window");
const menu_1 = require("./menu");
const ipc_1 = require("./ipc");
const updateChecker_1 = require("./services/updateChecker");
/**
 * 初始化应用
 */
function initializeApp() {
    // 初始化IPC通信
    (0, ipc_1.initializeIPC)();
    // 创建主窗口
    (0, window_1.createMainWindow)();
    // 创建应用菜单
    (0, menu_1.createApplicationMenu)();
    // 启动更新检查服务
    const updateChecker = updateChecker_1.UpdateChecker.getInstance();
    updateChecker.startScheduledUpdates();
}
/**
 * 应用准备就绪
 */
electron_1.app.whenReady().then(() => {
    initializeApp();
    electron_1.app.on('activate', () => {
        if (!(0, window_1.hasMainWindow)()) {
            (0, window_1.createMainWindow)();
        }
    });
});
/**
 * 所有窗口关闭时退出应用（macOS除外）
 */
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        // 清理服务
        const updateChecker = updateChecker_1.UpdateChecker.getInstance();
        updateChecker.cleanup();
        (0, ipc_1.cleanupIPC)();
        electron_1.app.quit();
    }
});
/**
 * 安全设置
 */
// 限制导航到外部网站
electron_1.app.on('web-contents-created', (_, contents) => {
    contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        if (!parsedUrl.origin.startsWith('http://localhost:') && parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });
});
