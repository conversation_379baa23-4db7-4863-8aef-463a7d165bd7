import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CheckSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/CheckSquareOutlined";
import AntdIcon from "../components/AntdIcon";
var CheckSquareOutlined = function CheckSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CheckSquareOutlinedSvg
  }));
};

/**![check-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQzMy4xIDY1Ny43YTMxLjggMzEuOCAwIDAwNTEuNyAwbDIxMC42LTI5MmMzLjgtNS4zIDAtMTIuNy02LjUtMTIuN0g2NDJjLTEwLjIgMC0xOS45IDQuOS0yNS45IDEzLjNMNDU5IDU4NC4zbC03MS4yLTk4LjhjLTYtOC4zLTE1LjYtMTMuMy0yNS45LTEzLjNIMzE1Yy02LjUgMC0xMC4zIDcuNC02LjUgMTIuN2wxMjQuNiAxNzIuOHoiIC8+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CheckSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CheckSquareOutlined';
}
export default RefIcon;