{"name": "@octokit/endpoint", "version": "11.0.0", "type": "module", "publishConfig": {"access": "public", "provenance": true}, "description": "Turns REST API endpoints into generic request options", "repository": "github:octokit/endpoint.js", "keywords": ["octokit", "github", "api", "rest"], "author": "<PERSON> (https://github.com/gr2m)", "license": "MIT", "devDependencies": {"@octokit/tsconfig": "^4.0.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^3.0.0", "esbuild": "^0.25.0", "glob": "^11.0.0", "prettier": "3.5.3", "semantic-release": "^24.0.0", "semantic-release-plugin-update-version-in-files": "^2.0.0", "typescript": "^5.0.0", "vitest": "^3.0.0"}, "dependencies": {"@octokit/types": "^14.0.0", "universal-user-agent": "^7.0.2"}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "./dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-bundle/index.js", "default": "./dist-bundle/index.js"}}, "sideEffects": false}