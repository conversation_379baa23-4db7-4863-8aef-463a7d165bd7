"use strict";
/**
 * 数据管理服务
 * 负责处理JSON文件的读写操作和数据管理
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.dataManager = exports.DataManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
// 数据文件路径
const DATA_DIR = path.join(process.cwd(), 'data');
const CONFIG_FILE = path.join(DATA_DIR, 'config.json');
const CATEGORIES_FILE = path.join(DATA_DIR, 'categories.json');
const AUTHORS_FILE = path.join(DATA_DIR, 'authors.json');
const PROJECTS_FILE = path.join(DATA_DIR, 'projects.json');
const CACHE_FILE = path.join(DATA_DIR, 'cache.json');
const HISTORY_FILE = path.join(DATA_DIR, 'history.json');
const STATISTICS_FILE = path.join(DATA_DIR, 'statistics.json');
/**
 * 数据管理器类
 */
class DataManager {
    constructor() {
        this.ensureDataDirectory();
    }
    /**
     * 获取单例实例
     */
    static getInstance() {
        if (!DataManager.instance) {
            DataManager.instance = new DataManager();
        }
        return DataManager.instance;
    }
    /**
     * 确保数据目录存在
     */
    ensureDataDirectory() {
        if (!fs.existsSync(DATA_DIR)) {
            fs.mkdirSync(DATA_DIR, { recursive: true });
        }
    }
    /**
     * 读取JSON文件
     */
    readJSONFile(filePath, defaultValue) {
        try {
            if (!fs.existsSync(filePath)) {
                this.writeJSONFile(filePath, defaultValue);
                return defaultValue;
            }
            const content = fs.readFileSync(filePath, 'utf-8');
            const data = JSON.parse(content);
            return data;
        }
        catch (error) {
            console.error(`Error reading file ${filePath}:`, error);
            return defaultValue;
        }
    }
    /**
     * 写入JSON文件
     */
    writeJSONFile(filePath, data) {
        try {
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
        }
        catch (error) {
            console.error(`Error writing file ${filePath}:`, error);
            throw error;
        }
    }
    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            version: '1.0.0',
            theme: 'dark',
            language: 'zh',
            github: {
                token: '',
                rateLimit: 5000,
                concurrent: 3,
            },
            update: {
                interval: 3600000,
                autoCheck: true,
            },
        };
    }
    // 配置相关方法
    getConfig() {
        return this.readJSONFile(CONFIG_FILE, this.getDefaultConfig());
    }
    setConfig(config) {
        this.writeJSONFile(CONFIG_FILE, config);
    }
    // 分类相关方法
    getCategories() {
        return this.readJSONFile(CATEGORIES_FILE, []);
    }
    setCategories(categories) {
        this.writeJSONFile(CATEGORIES_FILE, categories);
    }
    addCategory(category) {
        const categories = this.getCategories();
        categories.push(category);
        this.setCategories(categories);
    }
    updateCategory(categoryId, updates) {
        const categories = this.getCategories();
        const index = categories.findIndex(c => c.id === categoryId);
        if (index !== -1) {
            categories[index] = { ...categories[index], ...updates, updatedAt: new Date().toISOString() };
            this.setCategories(categories);
        }
    }
    deleteCategory(categoryId) {
        const categories = this.getCategories();
        const filtered = categories.filter(c => c.id !== categoryId);
        this.setCategories(filtered);
    }
    // 作者相关方法
    getAuthors() {
        return this.readJSONFile(AUTHORS_FILE, []);
    }
    setAuthors(authors) {
        this.writeJSONFile(AUTHORS_FILE, authors);
    }
    addAuthor(author) {
        const authors = this.getAuthors();
        authors.push(author);
        this.setAuthors(authors);
    }
    updateAuthor(authorId, updates) {
        const authors = this.getAuthors();
        const index = authors.findIndex(a => a.id === authorId);
        if (index !== -1) {
            authors[index] = { ...authors[index], ...updates, updatedAt: new Date().toISOString() };
            this.setAuthors(authors);
        }
    }
    deleteAuthor(authorId) {
        const authors = this.getAuthors();
        const filtered = authors.filter(a => a.id !== authorId);
        this.setAuthors(filtered);
    }
    // 项目相关方法
    getProjects() {
        return this.readJSONFile(PROJECTS_FILE, []);
    }
    setProjects(projects) {
        this.writeJSONFile(PROJECTS_FILE, projects);
    }
    addProject(project) {
        const projects = this.getProjects();
        projects.push(project);
        this.setProjects(projects);
    }
    updateProject(projectId, updates) {
        const projects = this.getProjects();
        const index = projects.findIndex(p => p.id === projectId);
        if (index !== -1) {
            projects[index] = { ...projects[index], ...updates, updatedAt: new Date().toISOString() };
            this.setProjects(projects);
        }
    }
    deleteProject(projectId) {
        const projects = this.getProjects();
        const filtered = projects.filter(p => p.id !== projectId);
        this.setProjects(filtered);
    }
    // 缓存相关方法
    getCache() {
        return this.readJSONFile(CACHE_FILE, {});
    }
    setCache(cache) {
        this.writeJSONFile(CACHE_FILE, cache);
    }
    getCacheItem(key) {
        const cache = this.getCache();
        return cache[key];
    }
    setCacheItem(key, value, ttl) {
        const cache = this.getCache();
        cache[key] = {
            value,
            timestamp: Date.now(),
            ttl: ttl || 3600000, // 默认1小时
        };
        this.setCache(cache);
    }
    clearExpiredCache() {
        const cache = this.getCache();
        const now = Date.now();
        const cleaned = {};
        for (const [key, item] of Object.entries(cache)) {
            const cacheItem = item;
            if (cacheItem.timestamp + cacheItem.ttl > now) {
                cleaned[key] = item;
            }
        }
        this.setCache(cleaned);
    }
    // 历史记录相关方法
    getHistory() {
        return this.readJSONFile(HISTORY_FILE, []);
    }
    addHistoryItem(item) {
        const history = this.getHistory();
        history.unshift({
            ...item,
            timestamp: new Date().toISOString(),
        });
        // 保持最近1000条记录
        if (history.length > 1000) {
            history.splice(1000);
        }
        this.writeJSONFile(HISTORY_FILE, history);
    }
    // 统计数据相关方法
    getStatistics() {
        return this.readJSONFile(STATISTICS_FILE, {});
    }
    setStatistics(statistics) {
        this.writeJSONFile(STATISTICS_FILE, statistics);
    }
    // 数据备份和恢复
    backupData() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupDir = path.join(DATA_DIR, 'backups');
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }
        const backupFile = path.join(backupDir, `backup-${timestamp}.json`);
        const backupData = {
            timestamp,
            config: this.getConfig(),
            categories: this.getCategories(),
            authors: this.getAuthors(),
            projects: this.getProjects(),
            statistics: this.getStatistics(),
        };
        this.writeJSONFile(backupFile, backupData);
        return backupFile;
    }
    restoreData(backupFile) {
        const backupData = this.readJSONFile(backupFile, null);
        if (!backupData) {
            throw new Error('Invalid backup file');
        }
        if (backupData.config)
            this.setConfig(backupData.config);
        if (backupData.categories)
            this.setCategories(backupData.categories);
        if (backupData.authors)
            this.setAuthors(backupData.authors);
        if (backupData.projects)
            this.setProjects(backupData.projects);
        if (backupData.statistics)
            this.setStatistics(backupData.statistics);
    }
}
exports.DataManager = DataManager;
// 导出单例实例
exports.dataManager = DataManager.getInstance();
