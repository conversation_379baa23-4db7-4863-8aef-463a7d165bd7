/**
 * 作者相关类型定义
 */

// 作者接口
export interface Author {
  id: string;
  username: string; // GitHub用户名
  displayName?: string; // 显示名称
  avatar?: string; // 头像URL
  bio?: string; // 个人简介
  email?: string;
  blog?: string; // 个人网站
  location?: string;
  company?: string;
  categoryId: string; // 所属分类ID
  isActive: boolean;
  isWatching: boolean; // 是否监控该作者
  createdAt: string;
  updatedAt: string;
  lastCheckedAt?: string; // 最后检查时间
  metadata?: AuthorMetadata;
  githubData?: GitHubAuthorData;
}

// 作者元数据接口
export interface AuthorMetadata {
  repositoryCount?: number; // 仓库数量
  followerCount?: number; // 粉丝数量
  followingCount?: number; // 关注数量
  totalStars?: number; // 总星标数
  totalForks?: number; // 总分叉数
  languages?: string[]; // 主要编程语言
  tags?: string[]; // 标签
  notes?: string; // 备注
  priority?: number; // 优先级 (1-5)
  hasUpdate?: boolean; // 是否有更新
  lastUpdateCheck?: string; // 最后更新检查时间
}

// GitHub作者数据接口
export interface GitHubAuthorData {
  id: number;
  nodeId: string;
  type: 'User' | 'Organization';
  siteAdmin: boolean;
  publicRepos: number;
  publicGists: number;
  followers: number;
  following: number;
  createdAt: string;
  updatedAt: string;
  hireable?: boolean;
  twitterUsername?: string;
  gravatarId?: string;
}

// 作者创建请求接口
export interface CreateAuthorRequest {
  username: string;
  displayName?: string;
  categoryId: string;
  isWatching?: boolean;
  priority?: number;
  tags?: string[];
  notes?: string;
}

// 作者更新请求接口
export interface UpdateAuthorRequest {
  id: string;
  displayName?: string;
  categoryId?: string;
  isActive?: boolean;
  isWatching?: boolean;
  priority?: number;
  tags?: string[];
  notes?: string;
}

// 作者统计接口
export interface AuthorStats {
  id: string;
  username: string;
  repositoryCount: number;
  totalStars: number;
  totalForks: number;
  totalWatchers: number;
  totalIssues: number;
  totalPullRequests: number;
  contributionCount: number;
  lastActivityAt: string;
  activityScore: number; // 活跃度评分
  popularRepositories: string[]; // 热门仓库名称
}

// 作者活动记录接口
export interface AuthorActivity {
  id: string;
  authorId: string;
  type: AuthorActivityType;
  repositoryName?: string;
  title: string;
  description?: string;
  url?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

// 作者活动类型
export type AuthorActivityType = 
  | 'repository_created'
  | 'repository_updated'
  | 'repository_deleted'
  | 'release_published'
  | 'issue_opened'
  | 'issue_closed'
  | 'pull_request_opened'
  | 'pull_request_merged'
  | 'commit_pushed'
  | 'star_received'
  | 'fork_created';

// 作者排序选项
export type AuthorSortField = 
  | 'username'
  | 'displayName'
  | 'createdAt'
  | 'updatedAt'
  | 'lastCheckedAt'
  | 'repositoryCount'
  | 'totalStars'
  | 'totalForks'
  | 'activityScore';

// 作者过滤选项接口
export interface AuthorFilter {
  categoryId?: string;
  isActive?: boolean;
  isWatching?: boolean;
  priority?: number;
  languages?: string[];
  tags?: string[];
  search?: string;
  hasActivity?: boolean;
  lastActivityDays?: number;
}

// 作者批量操作接口
export interface AuthorBatchOperation {
  action: 'activate' | 'deactivate' | 'watch' | 'unwatch' | 'delete' | 'move' | 'update';
  authorIds: string[];
  data?: Partial<Author>;
}

// 作者搜索结果接口
export interface AuthorSearchResult {
  authors: Author[];
  total: number;
  hasMore: boolean;
  searchTime: number;
  suggestions?: string[];
}

// 作者导入/导出接口
export interface AuthorExportData {
  authors: Author[];
  metadata: {
    exportedAt: string;
    version: string;
    totalCount: number;
    categories: { id: string; name: string }[];
  };
}

// 作者同步状态接口
export interface AuthorSyncStatus {
  authorId: string;
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  progress: number; // 0-100
  message?: string;
  startedAt: string;
  completedAt?: string;
  error?: string;
}

// 作者推荐接口
export interface AuthorRecommendation {
  username: string;
  displayName?: string;
  avatar?: string;
  bio?: string;
  repositoryCount: number;
  totalStars: number;
  languages: string[];
  reason: string; // 推荐理由
  score: number; // 推荐分数
  similarAuthors: <AUTHORS>
}

// 作者关系接口
export interface AuthorRelation {
  fromAuthorId: string;
  toAuthorId: string;
  type: 'following' | 'collaborator' | 'contributor' | 'similar';
  strength: number; // 关系强度 0-1
  metadata?: Record<string, any>;
}
