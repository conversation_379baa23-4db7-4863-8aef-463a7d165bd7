/**
 * 持久化中间件
 * 处理状态的持久化存储
 */

import { Middleware } from '@reduxjs/toolkit';

// 持久化中间件
export const persistMiddleware: Middleware = (store) => (next) => (action: any) => {
  const result = next(action);

  // 处理需要持久化的action
  if (action.type && typeof action.type === 'string' && (
      action.type.includes('category/') ||
      action.type.includes('author/') ||
      action.type.includes('project/') ||
      action.type.includes('settings/'))) {

    // 这里可以添加持久化逻辑
    console.log('Persist action:', action.type);
  }

  return result;
};
