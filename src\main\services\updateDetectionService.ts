/**
 * 更新检测服务
 * 负责检测项目和作者的更新情况
 */

import { GitHubService } from './githubService';
import { DataManager } from './dataManager';
import type { Project, Author } from '@/shared/types';

export class UpdateDetectionService {
  private githubService: GitHubService;
  private dataManager: DataManager;
  private requestCache: Map<string, { data: any[]; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
  private lastRequestTime: number = 0;
  private readonly MIN_REQUEST_INTERVAL = 1000; // 最小请求间隔1秒

  constructor(githubService: GitHubService, dataManager: DataManager) {
    this.githubService = githubService;
    this.dataManager = dataManager;
  }

  /**
   * 带缓存和速率限制的获取用户仓库
   */
  private async getUserRepositoriesWithCache(username: string): Promise<any[]> {
    const cacheKey = `repos_${username}`;
    const now = Date.now();

    // 检查缓存
    const cached = this.requestCache.get(cacheKey);
    if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
      console.log(`Using cached repositories for ${username}`);
      return cached.data;
    }

    // 速率限制：确保请求间隔
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
      const waitTime = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
      console.log(`Waiting ${waitTime}ms before next request...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    try {
      this.lastRequestTime = Date.now();
      const repos = await this.githubService.getUserRepositories(username);

      // 缓存结果
      this.requestCache.set(cacheKey, {
        data: repos,
        timestamp: Date.now()
      });

      return repos;
    } catch (error) {
      console.error(`Failed to get repositories for ${username}:`, error);

      // 如果有缓存数据，返回缓存（即使过期）
      if (cached) {
        console.log(`Using expired cache for ${username} due to error`);
        return cached.data;
      }

      return [];
    }
  }

  /**
   * 检测项目是否有更新
   */
  async checkProjectUpdate(project: Project): Promise<boolean> {
    try {
      // 获取项目的最新信息
      const repoInfo = await this.githubService.getRepositoryInfo(project.owner, project.name);
      
      if (!repoInfo) {
        return false;
      }

      // 获取上次检查的时间
      const lastCheck = project.metadata?.lastUpdateCheck;
      const lastCheckDate = lastCheck ? new Date(lastCheck) : new Date(0);
      
      // 检查项目是否在上次检查后有更新
      const updatedAt = new Date((repoInfo as any).updated_at);
      const pushedAt = new Date((repoInfo as any).pushed_at);
      
      // 如果项目更新时间或推送时间晚于上次检查时间，则认为有更新
      const hasUpdate = updatedAt > lastCheckDate || pushedAt > lastCheckDate;
      
      // 更新项目的检查时间和更新状态
      const updatedProject: Project = {
        ...project,
        metadata: {
          ...project.metadata,
          hasUpdate,
          lastUpdateCheck: new Date().toISOString(),
          stars: (repoInfo as any).stargazers_count,
          forks: (repoInfo as any).forks_count,
          watchers: (repoInfo as any).watchers_count,
          lastCommitAt: (repoInfo as any).pushed_at,
        }
      };

      // 保存更新后的项目信息
      const projects = this.dataManager.getProjects();
      const updatedProjects = projects.map(p => p.id === project.id ? updatedProject : p);
      this.dataManager.setProjects(updatedProjects);

      return hasUpdate;
    } catch (error) {
      console.error('Failed to check project update:', error);
      return false;
    }
  }

  /**
   * 检测作者是否有更新（新项目或项目更新）
   */
  async checkAuthorUpdate(author: Author): Promise<boolean> {
    try {
      // 获取作者的所有仓库（使用缓存）
      const repos = await this.getUserRepositoriesWithCache(author.username);
      
      if (!repos || repos.length === 0) {
        return false;
      }

      // 获取上次检查的时间
      const lastCheck = author.metadata?.lastUpdateCheck;
      const lastCheckDate = lastCheck ? new Date(lastCheck) : new Date(0);
      
      let hasUpdate = false;
      let newRepoCount = 0;
      let updatedRepoCount = 0;

      // 获取缓存的作者项目列表
      const cachedRepos = this.getCachedAuthorRepos(author.id);
      const cachedRepoIds = new Set(cachedRepos.map(r => r.id));

      for (const repo of repos) {
        const createdAt = new Date((repo as any).created_at);
        const updatedAt = new Date((repo as any).updated_at);
        const pushedAt = new Date((repo as any).pushed_at);

        // 检查是否是新项目
        if (createdAt > lastCheckDate || !cachedRepoIds.has((repo as any).id)) {
          hasUpdate = true;
          newRepoCount++;
        }

        // 检查项目是否有更新
        else if (updatedAt > lastCheckDate || pushedAt > lastCheckDate) {
          hasUpdate = true;
          updatedRepoCount++;
        }
      }

      // 保存当前的项目列表（用于跟踪最新状态）
      this.updateAuthorCurrentRepos(author.id, repos);

      // 只有在没有更新时才更新主缓存
      if (!hasUpdate) {
        this.setCachedAuthorRepos(author.id, repos);
      }

      // 更新作者的检查时间和更新状态
      const updatedAuthor: Author = {
        ...author,
        metadata: {
          ...author.metadata,
          hasUpdate,
          lastUpdateCheck: new Date().toISOString(),
          repositoryCount: repos.length,
          totalStars: repos.reduce((sum, repo) => sum + (repo as any).stargazers_count, 0),
          totalForks: repos.reduce((sum, repo) => sum + (repo as any).forks_count, 0),
        }
      };

      // 保存更新后的作者信息
      const authors = this.dataManager.getAuthors();
      const updatedAuthors = authors.map(a => a.id === author.id ? updatedAuthor : a);
      this.dataManager.setAuthors(updatedAuthors);

      console.log(`Author ${author.username} update check: ${hasUpdate ? 'HAS UPDATES' : 'NO UPDATES'} (${newRepoCount} new, ${updatedRepoCount} updated)`);
      
      return hasUpdate;
    } catch (error) {
      console.error('Failed to check author update:', error);
      return false;
    }
  }

  /**
   * 批量检测所有监控项目的更新
   */
  async checkAllProjectUpdates(): Promise<void> {
    const projects = this.dataManager.getProjects();
    const watchingProjects = projects.filter(p => p.isWatching && p.isActive);

    console.log(`Checking updates for ${watchingProjects.length} projects...`);

    for (const project of watchingProjects) {
      try {
        await this.checkProjectUpdate(project);
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Failed to check update for project ${project.fullName}:`, error);
      }
    }
  }

  /**
   * 批量检测所有监控作者的更新
   */
  async checkAllAuthorUpdates(): Promise<void> {
    const authors = this.dataManager.getAuthors();
    const watchingAuthors = authors.filter(a => a.isWatching && a.isActive);

    console.log(`Checking updates for ${watchingAuthors.length} authors...`);

    for (const author of watchingAuthors) {
      try {
        await this.checkAuthorUpdate(author);
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`Failed to check update for author ${author.username}:`, error);
      }
    }
  }

  /**
   * 获取缓存的作者项目列表
   */
  private getCachedAuthorRepos(authorId: string): any[] {
    try {
      const cached = this.dataManager.readJSONFile(`author-repos-${authorId}.json`, []);
      return cached;
    } catch (error) {
      return [];
    }
  }

  /**
   * 缓存作者的项目列表
   */
  private setCachedAuthorRepos(authorId: string, repos: any[]): void {
    try {
      this.dataManager.writeJSONFile(`author-repos-${authorId}.json`, repos);
    } catch (error) {
      console.error('Failed to cache author repos:', error);
    }
  }

  /**
   * 获取作者的新项目（相比上次检查）
   */
  async getAuthorNewProjects(authorId: string): Promise<any[]> {
    const author = this.dataManager.getAuthors().find(a => a.id === authorId);
    if (!author) return [];

    try {
      const currentRepos = await this.getUserRepositoriesWithCache(author.username);
      const cachedRepos = this.getCachedAuthorRepos(authorId);
      const cachedRepoIds = new Set(cachedRepos.map(r => r.id));

      // 获取上次检查时间，如果没有则使用30天前
      const lastCheck = author.metadata?.lastUpdateCheck;
      const lastCheckDate = lastCheck ? new Date(lastCheck) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const newProjects = currentRepos.filter(repo => {
        const createdAt = new Date((repo as any).created_at);
        // 项目创建时间晚于上次检查时间，或者不在缓存中
        return createdAt > lastCheckDate || !cachedRepoIds.has((repo as any).id);
      });

      console.log(`Author ${author.username} new projects: ${newProjects.length}`);
      return newProjects;
    } catch (error) {
      console.error('Failed to get author new projects:', error);
      return [];
    }
  }

  /**
   * 获取作者的更新项目（相比上次检查）
   */
  async getAuthorUpdatedProjects(authorId: string): Promise<any[]> {
    const author = this.dataManager.getAuthors().find(a => a.id === authorId);
    if (!author) return [];

    try {
      const currentRepos = await this.getUserRepositoriesWithCache(author.username);
      const cachedRepos = this.getCachedAuthorRepos(authorId);

      // 获取上次检查时间，如果没有则使用30天前
      const lastCheck = author.metadata?.lastUpdateCheck;
      const lastCheckDate = lastCheck ? new Date(lastCheck) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      // 创建缓存项目的映射，用于对比
      const cachedRepoMap = new Map(cachedRepos.map(repo => [repo.id, repo]));

      const updatedProjects = currentRepos.filter(repo => {
        const updatedAt = new Date((repo as any).updated_at);
        const pushedAt = new Date((repo as any).pushed_at);
        const createdAt = new Date((repo as any).created_at);

        // 排除新项目（创建时间晚于上次检查）
        if (createdAt > lastCheckDate) {
          return false;
        }

        // 检查是否有更新
        const hasRecentUpdate = updatedAt > lastCheckDate || pushedAt > lastCheckDate;

        // 如果有缓存，对比缓存中的更新时间
        const cachedRepo = cachedRepoMap.get((repo as any).id);
        if (cachedRepo) {
          const cachedUpdatedAt = new Date(cachedRepo.updated_at);
          const cachedPushedAt = new Date(cachedRepo.pushed_at);
          const hasUpdateSinceCache = updatedAt > cachedUpdatedAt || pushedAt > cachedPushedAt;
          return hasRecentUpdate || hasUpdateSinceCache;
        }

        return hasRecentUpdate;
      });

      console.log(`Author ${author.username} updated projects: ${updatedProjects.length}`);
      return updatedProjects;
    } catch (error) {
      console.error('Failed to get author updated projects:', error);
      return [];
    }
  }

  /**
   * 标记项目为已读，并更新缓存
   */
  public markProjectAsRead(authorId: string, projectId: string): void {
    try {
      const cachedRepos = this.getCachedAuthorRepos(authorId);
      const currentRepos = this.getCachedAuthorRepos(`${authorId}-current`) || [];

      // 找到对应的项目
      const project = currentRepos.find((repo: any) => repo.id.toString() === projectId);
      if (project) {
        // 更新缓存中的项目信息
        const updatedCachedRepos = [...cachedRepos];
        const existingIndex = updatedCachedRepos.findIndex((repo: any) => repo.id.toString() === projectId);

        if (existingIndex >= 0) {
          // 更新现有项目
          updatedCachedRepos[existingIndex] = project;
        } else {
          // 添加新项目到缓存
          updatedCachedRepos.push(project);
        }

        this.setCachedAuthorRepos(authorId, updatedCachedRepos);
        console.log(`Marked project ${projectId} as read for author ${authorId}`);
      }
    } catch (error) {
      console.error('Failed to mark project as read:', error);
    }
  }

  /**
   * 更新作者的当前项目列表（用于跟踪最新状态）
   */
  public updateAuthorCurrentRepos(authorId: string, repos: any[]): void {
    this.setCachedAuthorRepos(`${authorId}-current`, repos);
  }

  /**
   * 清除请求缓存
   */
  public clearCache(): void {
    this.requestCache.clear();
    console.log('UpdateDetectionService cache cleared');
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.requestCache.size,
      keys: Array.from(this.requestCache.keys())
    };
  }
}
