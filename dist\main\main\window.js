"use strict";
/**
 * 窗口管理模块
 * 负责创建和管理应用窗口
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMainWindow = createMainWindow;
exports.getMainWindow = getMainWindow;
exports.closeMainWindow = closeMainWindow;
exports.minimizeMainWindow = minimizeMainWindow;
exports.toggleMaximizeMainWindow = toggleMaximizeMainWindow;
exports.hasMainWindow = hasMainWindow;
const electron_1 = require("electron");
const path = __importStar(require("path"));
// 开发环境标识
const isDev = process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
// 窗口实例
let mainWindow = null;
/**
 * 获取窗口默认配置
 */
function getWindowConfig() {
    const { width: screenWidth, height: screenHeight } = electron_1.screen.getPrimaryDisplay().workAreaSize;
    return {
        width: Math.min(1200, screenWidth - 100),
        height: Math.min(800, screenHeight - 100),
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: true,
        },
        show: false,
        titleBarStyle: 'default',
        icon: path.join(__dirname, '../../public/icon.png'),
        backgroundColor: '#1f1f1f',
    };
}
/**
 * 创建主窗口
 */
function createMainWindow() {
    if (mainWindow) {
        return mainWindow;
    }
    mainWindow = new electron_1.BrowserWindow(getWindowConfig());
    // 加载应用
    if (isDev) {
        // 开发服务器URL - 默认使用5754端口
        const devUrl = process.env.VITE_DEV_SERVER_URL || 'http://localhost:5754';
        console.log('Loading dev URL:', devUrl);
        mainWindow.loadURL(devUrl);
        // 开发环境下打开开发者工具
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }
    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        if (mainWindow) {
            mainWindow.show();
            // 居中显示
            const { width, height } = electron_1.screen.getPrimaryDisplay().workAreaSize;
            const windowBounds = mainWindow.getBounds();
            const x = Math.round((width - windowBounds.width) / 2);
            const y = Math.round((height - windowBounds.height) / 2);
            mainWindow.setPosition(x, y);
        }
    });
    // 窗口关闭事件
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
    // 阻止新窗口打开
    mainWindow.webContents.setWindowOpenHandler(() => {
        return { action: 'deny' };
    });
    return mainWindow;
}
/**
 * 获取主窗口实例
 */
function getMainWindow() {
    return mainWindow;
}
/**
 * 关闭主窗口
 */
function closeMainWindow() {
    if (mainWindow) {
        mainWindow.close();
    }
}
/**
 * 最小化主窗口
 */
function minimizeMainWindow() {
    if (mainWindow) {
        mainWindow.minimize();
    }
}
/**
 * 最大化/还原主窗口
 */
function toggleMaximizeMainWindow() {
    if (mainWindow) {
        if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
        }
        else {
            mainWindow.maximize();
        }
    }
}
/**
 * 检查窗口是否存在
 */
function hasMainWindow() {
    return mainWindow !== null && !mainWindow.isDestroyed();
}
