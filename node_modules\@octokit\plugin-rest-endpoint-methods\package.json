{"name": "@octokit/plugin-rest-endpoint-methods", "version": "16.0.0", "type": "module", "description": "Octokit plugin adding one method for all of api.github.com REST API endpoints", "repository": "github:octokit/plugin-rest-endpoint-methods.js", "keywords": ["github", "api", "sdk", "toolkit"], "author": "<PERSON> (https://twitter.com/gr2m)", "license": "MIT", "dependencies": {"@octokit/types": "^14.1.0"}, "devDependencies": {"@octokit/core": "^7.0.0", "@octokit/tsconfig": "^4.0.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^3.0.5", "camelcase": "^8.0.0", "esbuild": "^0.25.0", "fetch-mock": "^12.0.0", "github-openapi-graphql-query": "^5.0.0", "glob": "^11.0.0", "prettier": "3.5.3", "semantic-release-plugin-update-version-in-files": "^2.0.0", "sinon": "^20.0.0", "sort-keys": "^5.0.0", "string-to-jsdoc-comment": "^1.0.0", "typescript": "^5.0.0", "vitest": "^3.0.5"}, "peerDependencies": {"@octokit/core": ">=6"}, "publishConfig": {"access": "public", "provenance": true}, "engines": {"node": ">= 20"}, "files": ["dist-*/**"], "types": "./dist-types/index.d.ts", "exports": {".": {"import": "./dist-src/index.js", "types": "./dist-types/index.d.ts", "default": "./dist-src/index.js"}}, "sideEffects": false}