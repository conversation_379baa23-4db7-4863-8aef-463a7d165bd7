{"name": "@octokit/plugin-paginate-rest", "publishConfig": {"access": "public", "provenance": true}, "type": "module", "version": "13.1.1", "description": "Octokit plugin to paginate REST API endpoint responses", "repository": "github:octokit/plugin-paginate-rest.js", "keywords": ["github", "api", "sdk", "toolkit"], "license": "MIT", "dependencies": {"@octokit/types": "^14.1.0"}, "peerDependencies": {"@octokit/core": ">=6"}, "devDependencies": {"@octokit/core": "^7.0.0", "@octokit/plugin-rest-endpoint-methods": "^16.0.0", "@octokit/tsconfig": "^4.0.0", "@types/node": "^22.0.0", "@vitest/coverage-v8": "^3.0.0", "esbuild": "^0.25.0", "fetch-mock": "^12.0.0", "github-openapi-graphql-query": "^5.0.0", "glob": "^11.0.0", "npm-run-all2": "^8.0.0", "prettier": "3.5.3", "semantic-release-plugin-update-version-in-files": "^2.0.0", "typescript": "^5.0.0", "vitest": "^3.0.0"}, "engines": {"node": ">= 20"}, "files": ["dist-*/**", "bin/**"], "types": "dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "import": "./dist-bundle/index.js", "default": "./dist-bundle/index.js"}, "./types": {"types": "./dist-types/.d.ts"}}, "sideEffects": false}