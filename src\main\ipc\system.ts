/**
 * 系统操作IPC处理器
 * 负责处理系统相关的IPC通信
 */

import { ipcMain, shell, dialog } from 'electron';
import { handleIPCError, handleIPCSuccess } from './index';
import { getMainWindow } from '../window';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * 注册系统操作IPC处理器
 */
export function registerSystemHandlers(): void {
  // 打开外部链接
  ipcMain.handle('system:openExternal', async (_, url: string) => {
    try {
      if (!url) {
        throw new Error('URL is required');
      }

      // 验证URL格式
      try {
        new URL(url);
      } catch {
        throw new Error('Invalid URL format');
      }

      await shell.openExternal(url);
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('system:openExternal', error);
    }
  });

  // 显示消息框
  ipcMain.handle('system:showMessageBox', async (_, options: any) => {
    try {
      const mainWindow = getMainWindow();
      if (!mainWindow) {
        throw new Error('Main window not available');
      }

      const result = await dialog.showMessageBox(mainWindow, {
        type: options.type || 'info',
        title: options.title || '提示',
        message: options.message || '',
        detail: options.detail || '',
        buttons: options.buttons || ['确定'],
        defaultId: options.defaultId || 0,
        cancelId: options.cancelId,
        ...options,
      });

      return handleIPCSuccess(result);
    } catch (error) {
      return handleIPCError('system:showMessageBox', error);
    }
  });

  // 显示打开文件对话框
  ipcMain.handle('system:showOpenDialog', async (_, options: any) => {
    try {
      const mainWindow = getMainWindow();
      if (!mainWindow) {
        throw new Error('Main window not available');
      }

      const result = await dialog.showOpenDialog(mainWindow, {
        title: options.title || '选择文件',
        defaultPath: options.defaultPath,
        buttonLabel: options.buttonLabel || '打开',
        filters: options.filters || [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] },
        ],
        properties: options.properties || ['openFile'],
        ...options,
      });

      return handleIPCSuccess(result);
    } catch (error) {
      return handleIPCError('system:showOpenDialog', error);
    }
  });

  // 显示保存文件对话框
  ipcMain.handle('system:showSaveDialog', async (_, options: any) => {
    try {
      const mainWindow = getMainWindow();
      if (!mainWindow) {
        throw new Error('Main window not available');
      }

      const result = await dialog.showSaveDialog(mainWindow, {
        title: options.title || '保存文件',
        defaultPath: options.defaultPath,
        buttonLabel: options.buttonLabel || '保存',
        filters: options.filters || [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] },
        ],
        ...options,
      });

      return handleIPCSuccess(result);
    } catch (error) {
      return handleIPCError('system:showSaveDialog', error);
    }
  });

  // 获取系统信息
  ipcMain.handle('system:getSystemInfo', async () => {
    try {
      const os = require('os');
      const systemInfo = {
        platform: process.platform,
        arch: process.arch,
        version: process.version,
        osType: os.type(),
        osRelease: os.release(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpus: os.cpus().length,
      };

      return handleIPCSuccess(systemInfo);
    } catch (error) {
      return handleIPCError('system:getSystemInfo', error);
    }
  });

  // 显示项目在文件管理器中
  ipcMain.handle('system:showItemInFolder', async (_, fullPath: string) => {
    try {
      if (!fullPath) {
        throw new Error('File path is required');
      }

      shell.showItemInFolder(fullPath);
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('system:showItemInFolder', error);
    }
  });

  // 文件系统操作 - 读取文件
  ipcMain.handle('fs:readFile', async (_, filePath: string) => {
    try {
      if (!filePath) {
        throw new Error('File path is required');
      }

      const fullPath = path.resolve(filePath);
      const data = await fs.readFile(fullPath, 'utf8');
      return handleIPCSuccess(data);
    } catch (error) {
      return handleIPCError('fs:readFile', error);
    }
  });

  // 文件系统操作 - 写入文件
  ipcMain.handle('fs:writeFile', async (_, filePath: string, data: string) => {
    try {
      if (!filePath) {
        throw new Error('File path is required');
      }

      const fullPath = path.resolve(filePath);
      await fs.writeFile(fullPath, data, 'utf8');
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('fs:writeFile', error);
    }
  });

  // 文件系统操作 - 确保目录存在
  ipcMain.handle('fs:ensureDir', async (_, dirPath: string) => {
    try {
      if (!dirPath) {
        throw new Error('Directory path is required');
      }

      const fullPath = path.resolve(dirPath);
      await fs.ensureDir(fullPath);
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('fs:ensureDir', error);
    }
  });

  // 文件系统操作 - 检查文件是否存在
  ipcMain.handle('fs:pathExists', async (_, filePath: string) => {
    try {
      if (!filePath) {
        throw new Error('File path is required');
      }

      const fullPath = path.resolve(filePath);
      const exists = await fs.pathExists(fullPath);
      return handleIPCSuccess(exists);
    } catch (error) {
      return handleIPCError('fs:pathExists', error);
    }
  });

  console.log('✓ System IPC handlers registered');
}
