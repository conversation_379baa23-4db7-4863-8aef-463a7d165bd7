/**
 * 统计分析页面
 */

import React from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Progress,
  List,
  Avatar,
  Space,
  Tag,
} from 'antd';
import {
  BarChartOutlined,
  FolderOutlined,
  UserOutlined,
  ProjectOutlined,
  StarOutlined,
  EyeOutlined,
  HeartOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import { selectAuthors } from '@/renderer/store/slices/authorSlice';
import { selectProjects } from '@/renderer/store/slices/projectSlice';

const { Title } = Typography;

const Statistics: React.FC = () => {
  const categories = useAppSelector(selectCategories);
  const authors = useAppSelector(selectAuthors);
  const projects = useAppSelector(selectProjects);

  // 计算统计数据
  const stats = {
    totalCategories: categories.length,
    activeCategories: categories.filter(c => c.isActive).length,
    totalAuthors: <AUTHORS>
    activeAuthors: <AUTHORS>
    watchingAuthors: <AUTHORS>
    totalProjects: projects.length,
    activeProjects: projects.filter(p => p.isActive).length,
    watchingProjects: projects.filter(p => p.isWatching).length,
    favoriteProjects: projects.filter(p => p.isFavorite).length,
    totalStars: projects.reduce((sum, p) => sum + (p.metadata?.stars || 0), 0),
    totalForks: projects.reduce((sum, p) => sum + (p.metadata?.forks || 0), 0),
  };

  // 分类统计
  const categoryStats = categories.map(category => ({
    ...category,
    authorCount: authors.filter(a => a.categoryId === category.id).length,
    projectCount: projects.filter(p => p.categoryId === category.id).length,
  })).sort((a, b) => (b.authorCount + b.projectCount) - (a.authorCount + a.projectCount));

  // 热门项目
  const topProjects = projects
    .filter(p => p.metadata?.stars)
    .sort((a, b) => (b.metadata?.stars || 0) - (a.metadata?.stars || 0))
    .slice(0, 5);

  // 活跃作者
  const activeAuthorsList = authors
    .filter(a => a.isWatching)
    .slice(0, 5);

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 页面标题 */}
        <Card>
          <div style={{ textAlign: 'center' }}>
            <BarChartOutlined style={{ fontSize: '48px', color: '#eb2f96', marginBottom: '16px' }} />
            <Title level={2}>统计分析</Title>
          </div>
        </Card>

        {/* 总体统计 */}
        <Card title="总体概览">
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="分类总数"
                value={stats.totalCategories}
                prefix={<FolderOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="作者总数"
                value={stats.totalAuthors}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="项目总数"
                value={stats.totalProjects}
                prefix={<ProjectOutlined />}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="总星标数"
                value={stats.totalStars}
                prefix={<StarOutlined />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="监控作者"
                value={stats.watchingAuthors}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="监控项目"
                value={stats.watchingProjects}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="收藏项目"
                value={stats.favoriteProjects}
                prefix={<HeartOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="总分叉数"
                value={stats.totalForks}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
          </Row>
        </Card>

        <Row gutter={[16, 16]}>
          {/* 分类统计 */}
          <Col xs={24} lg={12}>
            <Card title="分类统计" extra={<FolderOutlined />}>
              <List
                dataSource={categoryStats.slice(0, 5)}
                renderItem={(category) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          icon={<FolderOutlined />}
                          style={{ backgroundColor: category.color }}
                        />
                      }
                      title={category.name}
                      description={`${category.authorCount} 作者, ${category.projectCount} 项目`}
                    />
                    <div>
                      <Progress
                        percent={Math.round(((category.authorCount + category.projectCount) / Math.max(stats.totalAuthors + stats.totalProjects, 1)) * 100)}
                        size="small"
                        showInfo={false}
                      />
                    </div>
                  </List.Item>
                )}
                locale={{ emptyText: '暂无分类数据' }}
              />
            </Card>
          </Col>

          {/* 热门项目 */}
          <Col xs={24} lg={12}>
            <Card title="热门项目" extra={<TrophyOutlined />}>
              <List
                dataSource={topProjects}
                renderItem={(project, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar style={{ backgroundColor: '#1890ff' }}>
                          {index + 1}
                        </Avatar>
                      }
                      title={project.name}
                      description={project.fullName}
                    />
                    <div>
                      <Space direction="vertical" size="small">
                        <Tag icon={<StarOutlined />} color="gold">
                          {project.metadata?.stars || 0}
                        </Tag>
                        {project.isFavorite && (
                          <Tag icon={<HeartOutlined />} color="red">
                            收藏
                          </Tag>
                        )}
                      </Space>
                    </div>
                  </List.Item>
                )}
                locale={{ emptyText: '暂无项目数据' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* 活跃作者 */}
          <Col xs={24} lg={12}>
            <Card title="监控中的作者" extra={<UserOutlined />}>
              <List
                dataSource={activeAuthorsList}
                renderItem={(author) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={author.avatar}
                          icon={<UserOutlined />}
                        />
                      }
                      title={author.displayName || author.username}
                      description={`@${author.username}`}
                    />
                    <div>
                      <Space direction="vertical" size="small">
                        <Tag color="green">监控中</Tag>
                        {author.metadata?.priority && (
                          <Tag color="blue">
                            优先级 {author.metadata.priority}
                          </Tag>
                        )}
                      </Space>
                    </div>
                  </List.Item>
                )}
                locale={{ emptyText: '暂无监控作者' }}
              />
            </Card>
          </Col>

          {/* 监控状态 */}
          <Col xs={24} lg={12}>
            <Card title="监控状态">
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    作者监控率
                  </div>
                  <Progress
                    percent={Math.round((stats.watchingAuthors / Math.max(stats.totalAuthors, 1)) * 100)}
                    strokeColor="#52c41a"
                  />
                </div>
                
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    项目监控率
                  </div>
                  <Progress
                    percent={Math.round((stats.watchingProjects / Math.max(stats.totalProjects, 1)) * 100)}
                    strokeColor="#1890ff"
                  />
                </div>
                
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    项目收藏率
                  </div>
                  <Progress
                    percent={Math.round((stats.favoriteProjects / Math.max(stats.totalProjects, 1)) * 100)}
                    strokeColor="#f5222d"
                  />
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </Space>
    </div>
  );
};

export default Statistics;
