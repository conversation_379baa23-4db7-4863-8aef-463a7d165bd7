/**
 * 统计分析页面
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Progress,
  List,
  Avatar,
  Space,
  Tag,
  Select,
  DatePicker,
  Button,
  Divider,
  Empty,
} from 'antd';
import {
  BarChartOutlined,
  FolderOutlined,
  UserOutlined,
  ProjectOutlined,
  StarOutlined,
  EyeOutlined,
  HeartOutlined,
  TrophyOutlined,
  CalendarOutlined,
  FilterOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useAppSelector } from '@/renderer/store';
import { selectCategories } from '@/renderer/store/slices/categorySlice';
import { selectAuthors } from '@/renderer/store/slices/authorSlice';
import { selectProjects } from '@/renderer/store/slices/projectSlice';
import { CategoryType } from '@/shared/types';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const Statistics: React.FC = () => {
  const categories = useAppSelector(selectCategories);
  const authors = useAppSelector(selectAuthors);
  const projects = useAppSelector(selectProjects);

  // 状态管理
  const [selectedType, setSelectedType] = useState<'all' | 'project' | 'author'>('all');
  const [selectedTarget, setSelectedTarget] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [statisticsPeriod, setStatisticsPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  // 获取可选的目标列表
  const getTargetOptions = () => {
    if (selectedType === 'project') {
      return projects.map(p => ({ label: p.fullName, value: p.id }));
    } else if (selectedType === 'author') {
      return authors.map(a => ({ label: a.displayName || a.username, value: a.id }));
    }
    return [];
  };

  // 过滤数据
  const getFilteredData = () => {
    let filteredProjects = projects;
    let filteredAuthors = authors;

    if (selectedType === 'project' && selectedTarget) {
      filteredProjects = projects.filter(p => p.id === selectedTarget);
      filteredAuthors = authors.filter(a =>
        filteredProjects.some(p => p.owner === a.username)
      );
    } else if (selectedType === 'author' && selectedTarget) {
      filteredAuthors = authors.filter(a => a.id === selectedTarget);
      filteredProjects = projects.filter(p =>
        filteredAuthors.some(a => a.username === p.owner)
      );
    }

    // 日期范围过滤
    if (dateRange) {
      const [start, end] = dateRange;
      filteredProjects = filteredProjects.filter(p => {
        const createdAt = dayjs(p.createdAt);
        return createdAt.isAfter(start) && createdAt.isBefore(end);
      });
      filteredAuthors = filteredAuthors.filter(a => {
        const createdAt = dayjs(a.createdAt);
        return createdAt.isAfter(start) && createdAt.isBefore(end);
      });
    }

    return { filteredProjects, filteredAuthors };
  };

  const { filteredProjects, filteredAuthors } = getFilteredData();

  // 计算统计数据
  const stats = {
    totalCategories: categories.length,
    activeCategories: categories.filter(c => c.isActive).length,
    totalAuthors: <AUTHORS>
    activeAuthors: <AUTHORS>
    watchingAuthors: <AUTHORS>
    totalProjects: filteredProjects.length,
    activeProjects: filteredProjects.filter(p => p.isActive).length,
    watchingProjects: filteredProjects.filter(p => p.isWatching).length,
    favoriteProjects: filteredProjects.filter(p => p.isFavorite).length,
    totalStars: filteredProjects.reduce((sum, p) => sum + (p.metadata?.stars || 0), 0),
    totalForks: filteredProjects.reduce((sum, p) => sum + (p.metadata?.forks || 0), 0),
    updateFrequency: calculateUpdateFrequency(filteredProjects, statisticsPeriod),
  };

  // 计算更新频率
  function calculateUpdateFrequency(projects: any[], period: string) {
    if (projects.length === 0) return 0;

    const now = dayjs();
    const periodStart = now.subtract(1, period as any);

    const updatedProjects = projects.filter(p => {
      const updatedAt = dayjs(p.updatedAt);
      return updatedAt.isAfter(periodStart);
    });

    return Math.round((updatedProjects.length / projects.length) * 100);
  }

  // 重置过滤器
  const resetFilters = () => {
    setSelectedType('all');
    setSelectedTarget('');
    setDateRange(null);
    setStatisticsPeriod('month');
  };

  // 分类统计
  const categoryStats = categories.map(category => ({
    ...category,
    authorCount: filteredAuthors.filter(a => a.categoryId === category.id).length,
    projectCount: filteredProjects.filter(p => p.categoryId === category.id).length,
  })).sort((a, b) => (b.authorCount + b.projectCount) - (a.authorCount + a.projectCount));

  // 热门项目
  const topProjects = filteredProjects
    .filter(p => p.metadata?.stars)
    .sort((a, b) => (b.metadata?.stars || 0) - (a.metadata?.stars || 0))
    .slice(0, 5);

  // 活跃作者
  const activeAuthorsList = filteredAuthors
    .filter(a => a.isWatching)
    .slice(0, 5);

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 页面标题 */}
        <Card>
          <div style={{ textAlign: 'center' }}>
            <BarChartOutlined style={{ fontSize: '48px', color: '#eb2f96', marginBottom: '16px' }} />
            <Title level={2}>统计分析</Title>
          </div>
        </Card>

        {/* 过滤器 */}
        <Card title={<><FilterOutlined /> 统计过滤器</>}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <div style={{ marginBottom: '8px' }}>统计类型</div>
              <Select
                value={selectedType}
                onChange={(value) => {
                  setSelectedType(value);
                  setSelectedTarget('');
                }}
                style={{ width: '100%' }}
              >
                <Select.Option value="all">全部</Select.Option>
                <Select.Option value="project">指定项目</Select.Option>
                <Select.Option value="author">指定作者</Select.Option>
              </Select>
            </Col>

            {selectedType !== 'all' && (
              <Col xs={24} sm={12} md={6}>
                <div style={{ marginBottom: '8px' }}>
                  {selectedType === 'project' ? '选择项目' : '选择作者'}
                </div>
                <Select
                  value={selectedTarget}
                  onChange={setSelectedTarget}
                  style={{ width: '100%' }}
                  placeholder={`请选择${selectedType === 'project' ? '项目' : '作者'}`}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  options={getTargetOptions()}
                />
              </Col>
            )}

            <Col xs={24} sm={12} md={6}>
              <div style={{ marginBottom: '8px' }}>统计周期</div>
              <Select
                value={statisticsPeriod}
                onChange={setStatisticsPeriod}
                style={{ width: '100%' }}
              >
                <Select.Option value="week">最近一周</Select.Option>
                <Select.Option value="month">最近一月</Select.Option>
                <Select.Option value="quarter">最近三月</Select.Option>
                <Select.Option value="year">最近一年</Select.Option>
              </Select>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <div style={{ marginBottom: '8px' }}>日期范围</div>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Col>

            <Col xs={24}>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={resetFilters}
                >
                  重置过滤器
                </Button>
                {(selectedType !== 'all' || selectedTarget || dateRange) && (
                  <Tag color="blue">
                    已应用过滤条件
                  </Tag>
                )}
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 总体统计 */}
        <Card title="总体概览">
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="分类总数"
                value={stats.totalCategories}
                prefix={<FolderOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="作者总数"
                value={stats.totalAuthors}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="项目总数"
                value={stats.totalProjects}
                prefix={<ProjectOutlined />}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="总星标数"
                value={stats.totalStars}
                prefix={<StarOutlined />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="监控作者"
                value={stats.watchingAuthors}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="监控项目"
                value={stats.watchingProjects}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="收藏项目"
                value={stats.favoriteProjects}
                prefix={<HeartOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="总分叉数"
                value={stats.totalForks}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col xs={12} sm={8} md={6}>
              <Statistic
                title="更新频率"
                value={stats.updateFrequency}
                suffix="%"
                prefix={<CalendarOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 如果有过滤条件但没有数据，显示空状态 */}
        {(selectedType !== 'all' || dateRange) && stats.totalProjects === 0 && stats.totalAuthors === 0 && (
          <Card>
            <Empty
              description="没有符合条件的数据"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" onClick={resetFilters}>
                重置过滤器
              </Button>
            </Empty>
          </Card>
        )}

        <Row gutter={[16, 16]}>
          {/* 分类统计 */}
          <Col xs={24} lg={12}>
            <Card title="分类统计" extra={<FolderOutlined />}>
              <List
                dataSource={categoryStats.slice(0, 5)}
                renderItem={(category) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          icon={<FolderOutlined />}
                          style={{ backgroundColor: category.color }}
                        />
                      }
                      title={category.name}
                      description={`${category.authorCount} 作者, ${category.projectCount} 项目`}
                    />
                    <div>
                      <Progress
                        percent={Math.round(((category.authorCount + category.projectCount) / Math.max(stats.totalAuthors + stats.totalProjects, 1)) * 100)}
                        size="small"
                        showInfo={false}
                      />
                    </div>
                  </List.Item>
                )}
                locale={{ emptyText: '暂无分类数据' }}
              />
            </Card>
          </Col>

          {/* 热门项目 */}
          <Col xs={24} lg={12}>
            <Card title="热门项目" extra={<TrophyOutlined />}>
              <List
                dataSource={topProjects}
                renderItem={(project, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar style={{ backgroundColor: '#1890ff' }}>
                          {index + 1}
                        </Avatar>
                      }
                      title={project.name}
                      description={project.fullName}
                    />
                    <div>
                      <Space direction="vertical" size="small">
                        <Tag icon={<StarOutlined />} color="gold">
                          {project.metadata?.stars || 0}
                        </Tag>
                        {project.isFavorite && (
                          <Tag icon={<HeartOutlined />} color="red">
                            收藏
                          </Tag>
                        )}
                      </Space>
                    </div>
                  </List.Item>
                )}
                locale={{ emptyText: '暂无项目数据' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* 活跃作者 */}
          <Col xs={24} lg={12}>
            <Card title="监控中的作者" extra={<UserOutlined />}>
              <List
                dataSource={activeAuthorsList}
                renderItem={(author) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={author.avatar}
                          icon={<UserOutlined />}
                        />
                      }
                      title={author.displayName || author.username}
                      description={`@${author.username}`}
                    />
                    <div>
                      <Space direction="vertical" size="small">
                        <Tag color="green">监控中</Tag>
                        {author.metadata?.priority && (
                          <Tag color="blue">
                            优先级 {author.metadata.priority}
                          </Tag>
                        )}
                      </Space>
                    </div>
                  </List.Item>
                )}
                locale={{ emptyText: '暂无监控作者' }}
              />
            </Card>
          </Col>

          {/* 监控状态 */}
          <Col xs={24} lg={12}>
            <Card title="监控状态">
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    作者监控率
                  </div>
                  <Progress
                    percent={Math.round((stats.watchingAuthors / Math.max(stats.totalAuthors, 1)) * 100)}
                    strokeColor="#52c41a"
                  />
                </div>
                
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    项目监控率
                  </div>
                  <Progress
                    percent={Math.round((stats.watchingProjects / Math.max(stats.totalProjects, 1)) * 100)}
                    strokeColor="#1890ff"
                  />
                </div>
                
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    项目收藏率
                  </div>
                  <Progress
                    percent={Math.round((stats.favoriteProjects / Math.max(stats.totalProjects, 1)) * 100)}
                    strokeColor="#f5222d"
                  />
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </Space>
    </div>
  );
};

export default Statistics;
