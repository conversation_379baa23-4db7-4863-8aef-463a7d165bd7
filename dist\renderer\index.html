<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GitHub Monitor</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background-color: #1f1f1f;
      color: #ffffff;
    }
    
    #root {
      width: 100vw;
      height: 100vh;
    }
    
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      font-size: 18px;
    }
  </style>
  <script type="module" crossorigin src="./assets/index-CtxvtNsy.js"></script>
  <link rel="stylesheet" crossorigin href="./assets/index-MnlJxZP2.css">
</head>
<body>
  <div id="root">
    <div class="loading">Loading GitHub Monitor...</div>
  </div>
</body>
</html>
