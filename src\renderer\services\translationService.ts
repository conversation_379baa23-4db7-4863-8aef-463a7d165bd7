/**
 * 翻译服务
 * 使用内置的i18n国际化翻译功能
 */

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'ru', name: 'Русский', flag: '🇷🇺' },
];

// 翻译缓存
const translationCache = new Map<string, string>();

/**
 * 翻译服务类
 */
export class TranslationService {
  private static instance: TranslationService;
  private currentLanguage: string = 'zh';

  private constructor() {}

  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  /**
   * 设置当前语言
   */
  public setLanguage(language: string): void {
    this.currentLanguage = language;
  }

  /**
   * 获取当前语言
   */
  public getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  /**
   * 翻译文本
   */
  public async translateText(text: string, targetLanguage: string = this.currentLanguage): Promise<string> {
    if (!text || !text.trim()) {
      return text;
    }

    // 如果目标语言是中文，直接返回
    if (targetLanguage === 'zh') {
      return text;
    }

    // 检查缓存
    const cacheKey = `${text}_${targetLanguage}`;
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    try {
      // 使用简单的翻译映射（实际项目中可以接入真实的翻译API）
      const translatedText = await this.performTranslation(text, targetLanguage);
      
      // 缓存翻译结果
      translationCache.set(cacheKey, translatedText);
      
      return translatedText;
    } catch (error) {
      console.error('Translation failed:', error);
      return text; // 翻译失败时返回原文
    }
  }

  /**
   * 批量翻译
   */
  public async translateBatch(texts: string[], targetLanguage: string = this.currentLanguage): Promise<string[]> {
    const promises = texts.map(text => this.translateText(text, targetLanguage));
    return Promise.all(promises);
  }

  /**
   * 执行翻译（模拟翻译API）
   */
  private async performTranslation(text: string, targetLanguage: string): Promise<string> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 简单的翻译映射（实际项目中应该接入真实的翻译服务）
    const translations: Record<string, Record<string, string>> = {
      'en': {
        // 基础词汇
        '一个简单的项目': 'A simple project',
        '用于学习的代码库': 'Code repository for learning',
        '开源工具': 'Open source tool',
        '网站项目': 'Website project',
        '移动应用': 'Mobile application',
        '桌面应用': 'Desktop application',
        '库和框架': 'Library and framework',
        '文档和教程': 'Documentation and tutorial',
        '游戏项目': 'Game project',
        '数据科学': 'Data science',
        '机器学习': 'Machine learning',
        '人工智能': 'Artificial intelligence',
        '区块链': 'Blockchain',
        '物联网': 'Internet of Things',
        '云计算': 'Cloud computing',
        '微服务': 'Microservices',
        '前端开发': 'Frontend development',
        '后端开发': 'Backend development',
        '全栈开发': 'Full-stack development',
        '移动开发': 'Mobile development',

        // 常见技术词汇
        'JavaScript': 'JavaScript',
        'TypeScript': 'TypeScript',
        'Python': 'Python',
        'Java': 'Java',
        'C++': 'C++',
        'Go': 'Go',
        'Rust': 'Rust',
        'PHP': 'PHP',
        'Ruby': 'Ruby',
        'Swift': 'Swift',
        'Kotlin': 'Kotlin',
        'React': 'React',
        'Vue': 'Vue',
        'Angular': 'Angular',
        'Node.js': 'Node.js',
        'Express': 'Express',
        'Django': 'Django',
        'Flask': 'Flask',
        'Spring': 'Spring',
        'Laravel': 'Laravel',
        'Rails': 'Rails',
        'Docker': 'Docker',
        'Kubernetes': 'Kubernetes',
        'MongoDB': 'MongoDB',
        'MySQL': 'MySQL',
        'PostgreSQL': 'PostgreSQL',
        'Redis': 'Redis',
        'Elasticsearch': 'Elasticsearch',
        'GraphQL': 'GraphQL',
        'REST API': 'REST API',
        'WebSocket': 'WebSocket',
        'OAuth': 'OAuth',
        'JWT': 'JWT',
        'SSL': 'SSL',
        'HTTPS': 'HTTPS',
        'CI/CD': 'CI/CD',
        'DevOps': 'DevOps',
        'AWS': 'AWS',
        'Azure': 'Azure',
        'Google Cloud': 'Google Cloud',
        'Firebase': 'Firebase',
        'Vercel': 'Vercel',
        'Netlify': 'Netlify',
        'GitHub': 'GitHub',
        'GitLab': 'GitLab',
        'Bitbucket': 'Bitbucket',
        'npm': 'npm',
        'yarn': 'yarn',
        'webpack': 'webpack',
        'Vite': 'Vite',
        'Babel': 'Babel',
        'ESLint': 'ESLint',
        'Prettier': 'Prettier',
        'Jest': 'Jest',
        'Cypress': 'Cypress',
        'Selenium': 'Selenium',
        'TensorFlow': 'TensorFlow',
        'PyTorch': 'PyTorch',
        'Scikit-learn': 'Scikit-learn',
        'Pandas': 'Pandas',
        'NumPy': 'NumPy',
        'Matplotlib': 'Matplotlib',
        'Jupyter': 'Jupyter',
        'Anaconda': 'Anaconda',

        // 常见描述词汇
        '简单': 'simple',
        '复杂': 'complex',
        '高效': 'efficient',
        '快速': 'fast',
        '安全': 'secure',
        '稳定': 'stable',
        '可扩展': 'scalable',
        '轻量级': 'lightweight',
        '现代': 'modern',
        '强大': 'powerful',
        '灵活': 'flexible',
        '易用': 'easy to use',
        '开源': 'open source',
        '免费': 'free',
        '商业': 'commercial',
        '企业级': 'enterprise',
        '跨平台': 'cross-platform',
        '响应式': 'responsive',
        '实时': 'real-time',
        '异步': 'asynchronous',
        '同步': 'synchronous',
        '分布式': 'distributed',
        '集中式': 'centralized',
        '模块化': 'modular',
        '组件化': 'component-based',
        '面向对象': 'object-oriented',
        '函数式': 'functional',
        '声明式': 'declarative',
        '命令式': 'imperative',
      },
      'ja': {
        '一个简单的项目': 'シンプルなプロジェクト',
        '用于学习的代码库': '学習用のコードリポジトリ',
        '开源工具': 'オープンソースツール',
        '网站项目': 'ウェブサイトプロジェクト',
        '移动应用': 'モバイルアプリケーション',
        '桌面应用': 'デスクトップアプリケーション',
        '库和框架': 'ライブラリとフレームワーク',
        '文档和教程': 'ドキュメントとチュートリアル',
        '游戏项目': 'ゲームプロジェクト',
        '数据科学': 'データサイエンス',
        '机器学习': '機械学習',
        '人工智能': '人工知能',
        '区块链': 'ブロックチェーン',
        '物联网': 'モノのインターネット',
        '云计算': 'クラウドコンピューティング',
        '微服务': 'マイクロサービス',
        '前端开发': 'フロントエンド開発',
        '后端开发': 'バックエンド開発',
        '全栈开发': 'フルスタック開発',
        '移动开发': 'モバイル開発',
      }
    };

    // 尝试精确匹配
    if (translations[targetLanguage] && translations[targetLanguage][text]) {
      return translations[targetLanguage][text];
    }

    // 智能翻译：处理多个词汇的组合
    if (translations[targetLanguage]) {
      let translatedText = text;
      let hasTranslation = false;

      // 按词汇长度排序，优先匹配长词汇
      const sortedEntries = Object.entries(translations[targetLanguage])
        .sort(([a], [b]) => b.length - a.length);

      for (const [key, value] of sortedEntries) {
        if (translatedText.includes(key)) {
          translatedText = translatedText.replace(new RegExp(key, 'g'), value);
          hasTranslation = true;
        }
      }

      if (hasTranslation) {
        return translatedText;
      }
    }

    // 如果是英文文本，直接返回（可能已经是英文）
    if (targetLanguage === 'en' && /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/.test(text)) {
      return text;
    }

    // 如果没有找到翻译，返回原文
    return text;
  }

  /**
   * 清除翻译缓存
   */
  public clearCache(): void {
    translationCache.clear();
  }

  /**
   * 获取缓存大小
   */
  public getCacheSize(): number {
    return translationCache.size;
  }
}

// 导出单例实例
export const translationService = TranslationService.getInstance();
