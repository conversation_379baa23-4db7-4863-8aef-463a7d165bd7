/**
 * 翻译服务
 * 支持多种翻译提供商：Google Translate API、免费翻译服务、本地词汇翻译
 */

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'ru', name: 'Русский', flag: '🇷🇺' },
];

// 翻译提供商类型
export enum TranslationProvider {
  GOOGLE = 'google',
  GOOGLE_FREE = 'google_free',
  SILICONFLOW = 'siliconflow',
  GLM = 'glm',
  BAIDU = 'baidu',
  TENCENT = 'tencent',
  ALIBABA = 'alibaba',
  CUSTOM = 'custom',
  MYMEMORY = 'mymemory',
  LOCAL = 'local'
}

// 自定义翻译API配置
interface CustomApiConfig {
  name: string;
  endpoint: string;
  apiKey: string;
  headers?: Record<string, string>;
  requestFormat: 'openai' | 'custom';
  model?: string;
}

// 翻译配置
interface TranslationConfig {
  googleApiKey?: string;
  siliconflowApiKey?: string;
  glmApiKey?: string;
  baiduApiKey?: string;
  baiduSecretKey?: string;
  tencentSecretId?: string;
  tencentSecretKey?: string;
  alibabaAccessKeyId?: string;
  alibabaAccessKeySecret?: string;
  customApis: CustomApiConfig[];
  preferredProvider: TranslationProvider;
  enableFallback: boolean;
}

// 翻译缓存
const translationCache = new Map<string, string>();

// 默认配置
const defaultConfig: TranslationConfig = {
  customApis: [],
  preferredProvider: TranslationProvider.LOCAL,
  enableFallback: true,
};

/**
 * 翻译服务类
 */
export class TranslationService {
  private static instance: TranslationService;
  private currentLanguage: string = 'zh';
  private config: TranslationConfig = defaultConfig;

  private constructor() {
    // 从localStorage加载配置
    this.loadConfig();
  }

  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      const savedConfig = localStorage.getItem('translation-config');
      if (savedConfig) {
        this.config = { ...defaultConfig, ...JSON.parse(savedConfig) };
      }
    } catch (error) {
      console.warn('Failed to load translation config:', error);
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem('translation-config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save translation config:', error);
    }
  }

  /**
   * 设置翻译配置
   */
  public setConfig(config: Partial<TranslationConfig>): void {
    this.config = { ...this.config, ...config };
    this.saveConfig();
  }

  /**
   * 获取翻译配置
   */
  public getConfig(): TranslationConfig {
    return { ...this.config };
  }

  /**
   * 设置当前语言
   */
  public setLanguage(language: string): void {
    this.currentLanguage = language;
  }

  /**
   * 获取当前语言
   */
  public getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  /**
   * 翻译文本
   */
  public async translateText(text: string, targetLanguage: string = this.currentLanguage): Promise<string> {
    if (!text || !text.trim()) {
      return text;
    }

    console.log(`翻译请求: "${text.substring(0, 50)}..." -> ${targetLanguage}`);

    // 检查缓存
    const cacheKey = `${text}_${targetLanguage}`;
    if (translationCache.has(cacheKey)) {
      console.log('使用缓存翻译');
      return translationCache.get(cacheKey)!;
    }

    try {
      let translatedText = text;

      // 尝试多种翻译方式
      console.log('使用翻译提供商:', this.config.preferredProvider);

      try {
        if (this.config.preferredProvider === TranslationProvider.GOOGLE && this.config.googleApiKey) {
          translatedText = await this.translateWithGoogle(text, targetLanguage);
        } else if (this.config.preferredProvider === TranslationProvider.SILICONFLOW && this.config.siliconflowApiKey) {
          translatedText = await this.translateWithSiliconFlow(text, targetLanguage);
        } else if (this.config.preferredProvider === TranslationProvider.GLM && this.config.glmApiKey) {
          translatedText = await this.translateWithGLM(text, targetLanguage);
        } else if (this.config.preferredProvider === TranslationProvider.CUSTOM && this.config.customApis.length > 0) {
          translatedText = await this.translateWithCustomApi(text, targetLanguage);
        } else if (this.config.preferredProvider === TranslationProvider.MYMEMORY) {
          translatedText = await this.translateWithMyMemory(text, targetLanguage);
        } else {
          translatedText = await this.translateWithLocal(text, targetLanguage);
        }
      } catch (error) {
        console.warn(`主要翻译提供商失败: ${error.message}`);
        translatedText = text; // 确保降级逻辑能够执行
      }

      // 如果翻译失败且启用了降级，尝试其他方式
      if (translatedText === text && this.config.enableFallback) {
        console.log('主要翻译失败，尝试降级翻译');

        // 尝试本地翻译（最可靠的备用方案）
        try {
          translatedText = await this.translateWithLocal(text, targetLanguage);
          console.log('本地翻译成功');
        } catch (error) {
          console.warn('本地翻译失败:', error);
        }
      }

      // 缓存翻译结果
      translationCache.set(cacheKey, translatedText);

      return translatedText;
    } catch (error) {
      console.error('Translation failed:', error);
      return text; // 翻译失败时返回原文
    }
  }

  /**
   * 批量翻译
   */
  public async translateBatch(texts: string[], targetLanguage: string = this.currentLanguage): Promise<string[]> {
    const promises = texts.map(text => this.translateText(text, targetLanguage));
    return Promise.all(promises);
  }

  /**
   * 使用Google翻译API
   */
  private async translateWithGoogle(text: string, targetLanguage: string): Promise<string> {
    if (!this.config.googleApiKey) {
      throw new Error('Google API key not configured');
    }

    const url = `https://translation.googleapis.com/language/translate/v2?key=${this.config.googleApiKey}`;

    // Google翻译的语言代码映射
    const googleLangMap: Record<string, string> = {
      'zh': 'zh-cn',
      'ja': 'ja',
      'ko': 'ko',
      'en': 'en',
      'fr': 'fr',
      'de': 'de',
      'es': 'es',
      'ru': 'ru'
    };

    const targetLang = googleLangMap[targetLanguage] || targetLanguage;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: text,
        target: targetLang,
        source: 'auto', // 让Google自动检测源语言
        format: 'text'
      })
    });

    if (!response.ok) {
      throw new Error(`Google Translate API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.data && data.data.translations && data.data.translations.length > 0) {
      return data.data.translations[0].translatedText;
    }

    throw new Error('Invalid response from Google Translate API');
  }

  /**
   * 使用硅基流动翻译
   */
  private async translateWithSiliconFlow(text: string, targetLanguage: string): Promise<string> {
    if (!this.config.siliconflowApiKey) {
      throw new Error('SiliconFlow API key not configured');
    }

    const langMap: Record<string, string> = {
      'zh': '中文',
      'en': 'English',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语'
    };

    const targetLang = langMap[targetLanguage] || targetLanguage;

    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.siliconflowApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'Qwen/Qwen2.5-7B-Instruct',
        messages: [
          {
            role: 'user',
            content: `请将以下文本翻译成${targetLang}，只返回翻译结果，不要添加任何解释：\n\n${text}`
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`SiliconFlow API error: ${response.status}`);
    }

    const data = await response.json();
    if (data.choices && data.choices[0] && data.choices[0].message) {
      return data.choices[0].message.content.trim();
    }

    throw new Error('Invalid response from SiliconFlow API');
  }

  /**
   * 使用智谱清言翻译
   */
  private async translateWithGLM(text: string, targetLanguage: string): Promise<string> {
    if (!this.config.glmApiKey) {
      throw new Error('GLM API key not configured');
    }

    const langMap: Record<string, string> = {
      'zh': '中文',
      'en': 'English',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语'
    };

    const targetLang = langMap[targetLanguage] || targetLanguage;

    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.glmApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'glm-4-flash',
        messages: [
          {
            role: 'user',
            content: `请将以下文本翻译成${targetLang}，只返回翻译结果，不要添加任何解释：\n\n${text}`
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`GLM API error: ${response.status}`);
    }

    const data = await response.json();
    if (data.choices && data.choices[0] && data.choices[0].message) {
      return data.choices[0].message.content.trim();
    }

    throw new Error('Invalid response from GLM API');
  }

  /**
   * 使用自定义API翻译
   */
  private async translateWithCustomApi(text: string, targetLanguage: string): Promise<string> {
    const customApi = this.config.customApis[0]; // 使用第一个配置的自定义API
    if (!customApi) {
      throw new Error('No custom API configured');
    }

    const langMap: Record<string, string> = {
      'zh': '中文',
      'en': 'English',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语'
    };

    const targetLang = langMap[targetLanguage] || targetLanguage;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...customApi.headers
    };

    if (customApi.apiKey) {
      headers['Authorization'] = `Bearer ${customApi.apiKey}`;
    }

    let requestBody: any;

    if (customApi.requestFormat === 'openai') {
      requestBody = {
        model: customApi.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: `请将以下文本翻译成${targetLang}，只返回翻译结果，不要添加任何解释：\n\n${text}`
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      };
    } else {
      // 自定义格式，用户需要自己配置
      requestBody = {
        text: text,
        target_language: targetLang,
        source_language: 'auto'
      };
    }

    const response = await fetch(customApi.endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Custom API error: ${response.status}`);
    }

    const data = await response.json();

    if (customApi.requestFormat === 'openai') {
      if (data.choices && data.choices[0] && data.choices[0].message) {
        return data.choices[0].message.content.trim();
      }
    } else {
      // 假设自定义API返回 { translated_text: "..." }
      if (data.translated_text) {
        return data.translated_text;
      }
    }

    throw new Error('Invalid response from custom API');
  }

  /**
   * 使用MyMemory免费翻译API
   */
  private async translateWithMyMemory(text: string, targetLanguage: string): Promise<string> {
    // MyMemory API支持的语言代码映射
    const langMap: Record<string, string> = {
      'zh': 'zh-CN',
      'ja': 'ja',
      'ko': 'ko',
      'en': 'en',
      'fr': 'fr',
      'de': 'de',
      'es': 'es',
      'ru': 'ru'
    };

    // 智能检测源语言
    const sourceLang = this.detectSourceLanguage(text);
    const targetLang = langMap[targetLanguage] || targetLanguage;

    const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${sourceLang}|${targetLang}`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`MyMemory API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.responseStatus === 200 && data.responseData && data.responseData.translatedText) {
      const translatedText = data.responseData.translatedText;
      // 检查是否是有效的翻译（不是"PLEASE SELECT A VALID LANGUAGE PAIR"等错误信息）
      if (!translatedText.includes('PLEASE SELECT') && !translatedText.includes('INVALID')) {
        return translatedText;
      }
    }

    throw new Error('Invalid response from MyMemory API');
  }

  /**
   * 智能检测源语言
   */
  private detectSourceLanguage(text: string): string {
    // 检测中文字符
    const chineseRegex = /[\u4e00-\u9fff]/;
    // 检测日文字符
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
    // 检测韩文字符
    const koreanRegex = /[\uac00-\ud7af]/;
    // 检测英文字符
    const englishRegex = /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/;

    if (chineseRegex.test(text)) {
      return 'zh-CN';
    } else if (japaneseRegex.test(text)) {
      return 'ja';
    } else if (koreanRegex.test(text)) {
      return 'ko';
    } else if (englishRegex.test(text)) {
      return 'en';
    } else {
      // 默认使用自动检测
      return 'auto';
    }
  }

  /**
   * 使用本地词汇翻译
   */
  private async translateWithLocal(text: string, targetLanguage: string): Promise<string> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 简单的翻译映射（实际项目中应该接入真实的翻译服务）
    const translations: Record<string, Record<string, string>> = {
      'zh': {
        // 英文到中文的翻译
        'Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.': 'Claude-Flow v2.0.0 Alpha代表了AI驱动开发编排的革命性飞跃。采用企业级架构、先进的群体智能和无缝的Claude Code集成，从头开始构建。',
        'Code Mesh': '代码网格',
        'Synaptic Neural Mesh: a self-evolving, peer to peer neural fabric where every element is an agent, learning and communicating across a globally coordinated DAG substrate.': '突触神经网格：一个自我进化的点对点神经结构，其中每个元素都是一个代理，在全球协调的DAG基底上学习和通信。',
        'sparc': '稀疏编码',
        'dsp.ts': 'DSP TypeScript项目',
        'claude-flow': 'Claude流程',
        'code-mesh': '代码网格',
        'Synaptic-Mesh': '突触网格',
        'A simple project': '一个简单的项目',
        'Code repository for learning': '用于学习的代码库',
        'Open source tool': '开源工具',
        'Website project': '网站项目',
        'Mobile application': '移动应用',
        'Desktop application': '桌面应用',
        'Library and framework': '库和框架',
        'Documentation and tutorial': '文档和教程',
        'Game project': '游戏项目',
        'Data science': '数据科学',
        'Machine learning': '机器学习',
        'Artificial intelligence': '人工智能',
        'Blockchain': '区块链',
        'Internet of Things': '物联网',
        'Cloud computing': '云计算',
        'Microservices': '微服务',
        'Frontend development': '前端开发',
        'Backend development': '后端开发',
        'Full-stack development': '全栈开发',
        'Mobile development': '移动开发',
        'represents': '代表',
        'revolutionary': '革命性的',
        'leap': '飞跃',
        'AI-powered': 'AI驱动的',
        'development': '开发',
        'orchestration': '编排',
        'Built from the ground up': '从头开始构建',
        'enterprise-grade': '企业级',
        'architecture': '架构',
        'advanced': '先进的',
        'swarm intelligence': '群体智能',
        'seamless': '无缝的',
        'integration': '集成',
        'self-evolving': '自我进化的',
        'peer to peer': '点对点',
        'neural fabric': '神经结构',
        'element': '元素',
        'agent': '代理',
        'learning': '学习',
        'communicating': '通信',
        'globally coordinated': '全球协调的',
        'substrate': '基底',
      },
      'en': {
        // 基础词汇
        '一个简单的项目': 'A simple project',
        '用于学习的代码库': 'Code repository for learning',
        '开源工具': 'Open source tool',
        '网站项目': 'Website project',
        '移动应用': 'Mobile application',
        '桌面应用': 'Desktop application',
        '库和框架': 'Library and framework',
        '文档和教程': 'Documentation and tutorial',
        '游戏项目': 'Game project',
        '数据科学': 'Data science',
        '机器学习': 'Machine learning',
        '人工智能': 'Artificial intelligence',
        '区块链': 'Blockchain',
        '物联网': 'Internet of Things',
        '云计算': 'Cloud computing',
        '微服务': 'Microservices',
        '前端开发': 'Frontend development',
        '后端开发': 'Backend development',
        '全栈开发': 'Full-stack development',
        '移动开发': 'Mobile development',

        // 常见技术词汇
        'JavaScript': 'JavaScript',
        'TypeScript': 'TypeScript',
        'Python': 'Python',
        'Java': 'Java',
        'C++': 'C++',
        'Go': 'Go',
        'Rust': 'Rust',
        'PHP': 'PHP',
        'Ruby': 'Ruby',
        'Swift': 'Swift',
        'Kotlin': 'Kotlin',
        'React': 'React',
        'Vue': 'Vue',
        'Angular': 'Angular',
        'Node.js': 'Node.js',
        'Express': 'Express',
        'Django': 'Django',
        'Flask': 'Flask',
        'Spring': 'Spring',
        'Laravel': 'Laravel',
        'Rails': 'Rails',
        'Docker': 'Docker',
        'Kubernetes': 'Kubernetes',
        'MongoDB': 'MongoDB',
        'MySQL': 'MySQL',
        'PostgreSQL': 'PostgreSQL',
        'Redis': 'Redis',
        'Elasticsearch': 'Elasticsearch',
        'GraphQL': 'GraphQL',
        'REST API': 'REST API',
        'WebSocket': 'WebSocket',
        'OAuth': 'OAuth',
        'JWT': 'JWT',
        'SSL': 'SSL',
        'HTTPS': 'HTTPS',
        'CI/CD': 'CI/CD',
        'DevOps': 'DevOps',
        'AWS': 'AWS',
        'Azure': 'Azure',
        'Google Cloud': 'Google Cloud',
        'Firebase': 'Firebase',
        'Vercel': 'Vercel',
        'Netlify': 'Netlify',
        'GitHub': 'GitHub',
        'GitLab': 'GitLab',
        'Bitbucket': 'Bitbucket',
        'npm': 'npm',
        'yarn': 'yarn',
        'webpack': 'webpack',
        'Vite': 'Vite',
        'Babel': 'Babel',
        'ESLint': 'ESLint',
        'Prettier': 'Prettier',
        'Jest': 'Jest',
        'Cypress': 'Cypress',
        'Selenium': 'Selenium',
        'TensorFlow': 'TensorFlow',
        'PyTorch': 'PyTorch',
        'Scikit-learn': 'Scikit-learn',
        'Pandas': 'Pandas',
        'NumPy': 'NumPy',
        'Matplotlib': 'Matplotlib',
        'Jupyter': 'Jupyter',
        'Anaconda': 'Anaconda',

        // 常见描述词汇
        '简单': 'simple',
        '复杂': 'complex',
        '高效': 'efficient',
        '快速': 'fast',
        '安全': 'secure',
        '稳定': 'stable',
        '可扩展': 'scalable',
        '轻量级': 'lightweight',
        '现代': 'modern',
        '强大': 'powerful',
        '灵活': 'flexible',
        '易用': 'easy to use',
        '开源': 'open source',
        '免费': 'free',
        '商业': 'commercial',
        '企业级': 'enterprise',
        '跨平台': 'cross-platform',
        '响应式': 'responsive',
        '实时': 'real-time',
        '异步': 'asynchronous',
        '同步': 'synchronous',
        '分布式': 'distributed',
        '集中式': 'centralized',
        '模块化': 'modular',
        '组件化': 'component-based',
        '面向对象': 'object-oriented',
        '函数式': 'functional',
        '声明式': 'declarative',
        '命令式': 'imperative',
      },
      'ja': {
        // 基础词汇
        '一个简单的项目': 'シンプルなプロジェクト',
        '用于学习的代码库': '学習用のコードリポジトリ',
        '开源工具': 'オープンソースツール',
        '网站项目': 'ウェブサイトプロジェクト',
        '移动应用': 'モバイルアプリケーション',
        '桌面应用': 'デスクトップアプリケーション',
        '库和框架': 'ライブラリとフレームワーク',
        '文档和教程': 'ドキュメントとチュートリアル',
        '游戏项目': 'ゲームプロジェクト',
        '数据科学': 'データサイエンス',
        '机器学习': '機械学習',
        '人工智能': '人工知能',
        '区块链': 'ブロックチェーン',
        '物联网': 'モノのインターネット',
        '云计算': 'クラウドコンピューティング',
        '微服务': 'マイクロサービス',
        '前端开发': 'フロントエンド開発',
        '后端开发': 'バックエンド開発',
        '全栈开发': 'フルスタック開発',
        '移动开发': 'モバイル開発',

        // 英文项目描述翻译
        'Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.': 'Claude-Flow v2.0.0 Alphaは、AI駆動開発オーケストレーションにおける革命的な飛躍を表しています。エンタープライズグレードのアーキテクチャ、高度なスウォームインテリジェンス、シームレスなClaude Code統合により、ゼロから構築されています。',
        'Code Mesh': 'コードメッシュ',
        'Synaptic Neural Mesh: a self-evolving, peer to peer neural fabric where every element is an agent, learning and communicating across a globally coordinated DAG substrate.': 'シナプティックニューラルメッシュ：すべての要素がエージェントである自己進化型のピアツーピアニューラルファブリック。グローバルに調整されたDAG基盤全体で学習と通信を行います。',
        'represents': '表す',
        'revolutionary': '革命的な',
        'leap': '飛躍',
        'AI-powered': 'AI駆動の',
        'development': '開発',
        'orchestration': 'オーケストレーション',
        'Built from the ground up': 'ゼロから構築',
        'enterprise-grade': 'エンタープライズグレード',
        'architecture': 'アーキテクチャ',
        'advanced': '高度な',
        'swarm intelligence': 'スウォームインテリジェンス',
        'seamless': 'シームレス',
        'integration': '統合',
        'self-evolving': '自己進化型',
        'peer to peer': 'ピアツーピア',
        'neural fabric': 'ニューラルファブリック',
        'element': '要素',
        'agent': 'エージェント',
        'learning': '学習',
        'communicating': '通信',
        'globally coordinated': 'グローバルに調整された',
        'substrate': '基盤',
      }
    };

    // 尝试精确匹配
    if (translations[targetLanguage] && translations[targetLanguage][text]) {
      return translations[targetLanguage][text];
    }

    // 智能翻译：处理多个词汇的组合
    if (translations[targetLanguage]) {
      let translatedText = text;
      let hasTranslation = false;

      // 按词汇长度排序，优先匹配长词汇
      const sortedEntries = Object.entries(translations[targetLanguage])
        .sort(([a], [b]) => b.length - a.length);

      for (const [key, value] of sortedEntries) {
        if (translatedText.toLowerCase().includes(key.toLowerCase())) {
          // 使用正则表达式进行大小写不敏感的替换
          const regex = new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
          translatedText = translatedText.replace(regex, value);
          hasTranslation = true;
        }
      }

      if (hasTranslation) {
        return translatedText;
      }
    }

    // 如果是编程语言或技术名词，保持原样
    const techTerms = ['TypeScript', 'JavaScript', 'Python', 'Java', 'C++', 'Go', 'Rust', 'PHP', 'Ruby', 'Swift', 'Kotlin', 'React', 'Vue', 'Angular', 'Node.js', 'Docker', 'Kubernetes', 'MongoDB', 'MySQL', 'PostgreSQL', 'Redis', 'GraphQL', 'REST', 'API', 'JSON', 'XML', 'HTML', 'CSS', 'SCSS', 'SASS', 'Webpack', 'Vite', 'Babel', 'ESLint', 'Prettier', 'Jest', 'Cypress', 'GitHub', 'GitLab', 'npm', 'yarn', 'pnpm'];

    if (techTerms.some(term => text.toLowerCase().includes(term.toLowerCase()))) {
      // 对于包含技术术语的文本，提供基本翻译
      if (targetLanguage === 'zh') {
        if (text.toLowerCase().includes('typescript')) return text + ' (TypeScript项目)';
        if (text.toLowerCase().includes('javascript')) return text + ' (JavaScript项目)';
        if (text.toLowerCase().includes('python')) return text + ' (Python项目)';
        if (text.toLowerCase().includes('rust')) return text + ' (Rust项目)';
        if (text.toLowerCase().includes('learning')) return text.replace(/learning/gi, '学习');
        if (text.toLowerCase().includes('project')) return text.replace(/project/gi, '项目');
        if (text.toLowerCase().includes('code')) return text.replace(/code/gi, '代码');
        if (text.toLowerCase().includes('repository')) return text.replace(/repository/gi, '仓库');
        if (text.toLowerCase().includes('library')) return text.replace(/library/gi, '库');
        if (text.toLowerCase().includes('framework')) return text.replace(/framework/gi, '框架');
        if (text.toLowerCase().includes('tool')) return text.replace(/tool/gi, '工具');
        if (text.toLowerCase().includes('application')) return text.replace(/application/gi, '应用');
        if (text.toLowerCase().includes('website')) return text.replace(/website/gi, '网站');
        if (text.toLowerCase().includes('mobile')) return text.replace(/mobile/gi, '移动');
        if (text.toLowerCase().includes('desktop')) return text.replace(/desktop/gi, '桌面');
        if (text.toLowerCase().includes('web')) return text.replace(/web/gi, '网页');
        if (text.toLowerCase().includes('api')) return text.replace(/api/gi, 'API接口');
        if (text.toLowerCase().includes('database')) return text.replace(/database/gi, '数据库');
        if (text.toLowerCase().includes('server')) return text.replace(/server/gi, '服务器');
        if (text.toLowerCase().includes('client')) return text.replace(/client/gi, '客户端');
      }
    }

    // 如果是英文文本，直接返回（可能已经是英文）
    if (targetLanguage === 'en' && /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/.test(text)) {
      return text;
    }

    // 如果没有找到翻译，返回原文
    return text;
  }

  /**
   * 清除翻译缓存
   */
  public clearCache(): void {
    translationCache.clear();
  }

  /**
   * 获取缓存大小
   */
  public getCacheSize(): number {
    return translationCache.size;
  }
}

// 导出单例实例
export const translationService = TranslationService.getInstance();
