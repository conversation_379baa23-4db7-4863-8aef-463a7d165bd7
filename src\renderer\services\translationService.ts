/**
 * 翻译服务
 * 支持多种翻译提供商：Google Translate API、免费翻译服务、本地词汇翻译
 */

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'ru', name: 'Русский', flag: '🇷🇺' },
];

// 翻译提供商类型
export enum TranslationProvider {
  GOOGLE = 'google',
  GLM = 'glm'
}

// 翻译配置
interface TranslationConfig {
  glmApiKey?: string;
  preferredProvider: TranslationProvider;
  enableFallback: boolean;
}

// 默认配置
const defaultConfig: TranslationConfig = {
  preferredProvider: TranslationProvider.GOOGLE,
  enableFallback: true,
};

/**
 * 翻译服务类
 */
export class TranslationService {
  private static instance: TranslationService;
  private currentLanguage: string = 'zh';
  private config: TranslationConfig = defaultConfig;

  private constructor() {
    // 从localStorage加载配置
    this.loadConfig();
  }

  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      const savedConfig = localStorage.getItem('translation-config');
      if (savedConfig) {
        this.config = { ...defaultConfig, ...JSON.parse(savedConfig) };
      }
    } catch (error) {
      console.warn('Failed to load translation config:', error);
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem('translation-config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save translation config:', error);
    }
  }

  /**
   * 设置翻译配置
   */
  public setConfig(config: Partial<TranslationConfig>): void {
    this.config = { ...this.config, ...config };
    this.saveConfig();
  }

  /**
   * 获取翻译配置
   */
  public getConfig(): TranslationConfig {
    return { ...this.config };
  }

  /**
   * 设置当前语言
   */
  public setLanguage(language: string): void {
    this.currentLanguage = language;
  }

  /**
   * 获取当前语言
   */
  public getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  /**
   * 翻译文本
   */
  public async translateText(text: string, targetLanguage: string = this.currentLanguage): Promise<string> {
    if (!text || !text.trim()) {
      return text;
    }

    console.log(`实时翻译请求: "${text.substring(0, 50)}..." -> ${targetLanguage}`);

    try {
      let translatedText = text;

      console.log('使用翻译提供商:', this.config.preferredProvider);

      try {
        if (this.config.preferredProvider === TranslationProvider.GOOGLE) {
          translatedText = await this.translateWithGoogleFree(text, targetLanguage);
          console.log('Google免费翻译成功');
        } else if (this.config.preferredProvider === TranslationProvider.GLM && this.config.glmApiKey) {
          translatedText = await this.translateWithGLM(text, targetLanguage);
          console.log('智谱清言翻译成功');
        } else {
          throw new Error('未配置有效的翻译服务');
        }
      } catch (error) {
        console.warn(`主要翻译提供商失败: ${error.message}`);

        // 如果启用了降级，尝试另一个服务
        if (this.config.enableFallback) {
          console.log('主要翻译失败，尝试降级翻译');

          try {
            if (this.config.preferredProvider === TranslationProvider.GLM) {
              // GLM失败，降级到Google免费翻译
              translatedText = await this.translateWithGoogleFree(text, targetLanguage);
              console.log('降级到Google免费翻译成功');
            } else {
              // Google失败，如果有GLM配置则降级到GLM
              if (this.config.glmApiKey) {
                translatedText = await this.translateWithGLM(text, targetLanguage);
                console.log('降级到智谱清言翻译成功');
              } else {
                throw new Error('没有可用的降级翻译服务');
              }
            }
          } catch (fallbackError) {
            console.warn('降级翻译也失败:', fallbackError);
            translatedText = text; // 返回原文
          }
        } else {
          translatedText = text; // 不启用降级时返回原文
        }
      }

      return translatedText;
    } catch (error) {
      console.error('Translation failed:', error);
      return text; // 翻译失败时返回原文
    }
  }

  /**
   * 批量翻译
   */
  public async translateBatch(texts: string[], targetLanguage: string = this.currentLanguage): Promise<string[]> {
    const promises = texts.map(text => this.translateText(text, targetLanguage));
    return Promise.all(promises);
  }

  /**
   * 使用Google免费翻译服务
   */
  private async translateWithGoogleFree(text: string, targetLanguage: string): Promise<string> {
    console.log(`Google免费翻译: "${text.substring(0, 50)}..." -> ${targetLanguage}`);

    // Google翻译的语言代码映射
    const googleLangMap: Record<string, string> = {
      'zh': 'zh-cn',
      'ja': 'ja',
      'ko': 'ko',
      'en': 'en',
      'fr': 'fr',
      'de': 'de',
      'es': 'es',
      'ru': 'ru'
    };

    const targetLang = googleLangMap[targetLanguage] || targetLanguage;
    const sourceLang = this.detectSourceLanguage(text);

    // 使用Google Translate的免费接口
    const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=${sourceLang}&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`Google Translate error: ${response.status}`);
      }

      const data = await response.json();

      if (data && data[0] && data[0][0] && data[0][0][0]) {
        const translatedText = data[0].map((item: any) => item[0]).join('');
        console.log(`Google免费翻译成功: "${text}" -> "${translatedText}"`);
        return translatedText;
      }

      throw new Error('Invalid response from Google Translate');
    } catch (error) {
      console.error('Google免费翻译失败:', error);
      throw error;
    }
  }



  /**
   * 使用智谱清言GLM-4-FlashX翻译
   */
  private async translateWithGLM(text: string, targetLanguage: string): Promise<string> {
    if (!this.config.glmApiKey) {
      throw new Error('智谱清言 API key 未配置');
    }

    console.log(`智谱清言翻译: "${text.substring(0, 50)}..." -> ${targetLanguage}`);

    const langMap: Record<string, string> = {
      'zh': '中文',
      'en': '英文',
      'ja': '日文',
      'ko': '韩文',
      'fr': '法文',
      'de': '德文',
      'es': '西班牙文',
      'ru': '俄文'
    };

    const targetLang = langMap[targetLanguage] || targetLanguage;

    // 使用专业的翻译提示词
    const translationPrompt = `你是一个专业的翻译专家，请将以下文本准确翻译成${targetLang}。

要求：
1. 保持原文的语义和语调
2. 确保翻译自然流畅
3. 对于技术术语，请使用准确的专业词汇
4. 只返回翻译结果，不要添加任何解释或说明

原文：
${text}

翻译：`;

    try {
      const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.glmApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'GLM-4-FlashX',
          messages: [
            {
              role: 'user',
              content: translationPrompt
            }
          ],
          temperature: 0.1,
          max_tokens: 2000,
          top_p: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`智谱清言 API 错误: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (data.choices && data.choices[0] && data.choices[0].message) {
        const translatedText = data.choices[0].message.content.trim();
        console.log(`智谱清言翻译成功: "${text}" -> "${translatedText}"`);
        return translatedText;
      }

      throw new Error('智谱清言 API 返回无效响应');
    } catch (error) {
      console.error('智谱清言翻译失败:', error);
      throw error;
    }
  }





  /**
   * 智能检测源语言
   */
  private detectSourceLanguage(text: string): string {
    // 检测中文字符
    const chineseRegex = /[\u4e00-\u9fff]/;
    // 检测日文字符
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
    // 检测韩文字符
    const koreanRegex = /[\uac00-\ud7af]/;
    // 检测英文字符
    const englishRegex = /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/;

    if (chineseRegex.test(text)) {
      return 'zh-CN';
    } else if (japaneseRegex.test(text)) {
      return 'ja';
    } else if (koreanRegex.test(text)) {
      return 'ko';
    } else if (englishRegex.test(text)) {
      return 'en';
    } else {
      // 默认使用自动检测
      return 'auto';
    }
  }

  /**
   * 清除翻译缓存（实时翻译，无缓存）
   */
  public clearCache(): void {
    console.log('实时翻译模式，无缓存机制');
  }

  /**
   * 获取缓存大小（实时翻译，无缓存）
   */
  public getCacheSize(): number {
    return 0;
  }
}

// 导出单例实例
export const translationService = TranslationService.getInstance();
