/**
 * 翻译服务
 * 支持多种翻译提供商：Google Translate API、免费翻译服务、本地词汇翻译
 */

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'ru', name: 'Русский', flag: '🇷🇺' },
];

// 翻译提供商类型
export enum TranslationProvider {
  GOOGLE = 'google',
  GOOGLE_FREE = 'google_free',
  SILICONFLOW = 'siliconflow',
  GLM = 'glm',
  BAIDU = 'baidu',
  TENCENT = 'tencent',
  ALIBABA = 'alibaba',
  CUSTOM = 'custom',
  MYMEMORY = 'mymemory',
  LOCAL = 'local'
}

// 自定义翻译API配置
interface CustomApiConfig {
  name: string;
  endpoint: string;
  apiKey: string;
  headers?: Record<string, string>;
  requestFormat: 'openai' | 'custom';
  model?: string;
}

// 翻译配置
interface TranslationConfig {
  googleApiKey?: string;
  siliconflowApiKey?: string;
  glmApiKey?: string;
  baiduApiKey?: string;
  baiduSecretKey?: string;
  tencentSecretId?: string;
  tencentSecretKey?: string;
  alibabaAccessKeyId?: string;
  alibabaAccessKeySecret?: string;
  customApis: CustomApiConfig[];
  preferredProvider: TranslationProvider;
  enableFallback: boolean;
}

// 移除翻译缓存，使用在线翻译
// const translationCache = new Map<string, string>();

// 默认配置
const defaultConfig: TranslationConfig = {
  customApis: [],
  preferredProvider: TranslationProvider.LOCAL,
  enableFallback: true,
};

/**
 * 翻译服务类
 */
export class TranslationService {
  private static instance: TranslationService;
  private currentLanguage: string = 'zh';
  private config: TranslationConfig = defaultConfig;

  private constructor() {
    // 从localStorage加载配置
    this.loadConfig();
  }

  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      const savedConfig = localStorage.getItem('translation-config');
      if (savedConfig) {
        this.config = { ...defaultConfig, ...JSON.parse(savedConfig) };
      }
    } catch (error) {
      console.warn('Failed to load translation config:', error);
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem('translation-config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save translation config:', error);
    }
  }

  /**
   * 设置翻译配置
   */
  public setConfig(config: Partial<TranslationConfig>): void {
    this.config = { ...this.config, ...config };
    this.saveConfig();
  }

  /**
   * 获取翻译配置
   */
  public getConfig(): TranslationConfig {
    return { ...this.config };
  }

  /**
   * 设置当前语言
   */
  public setLanguage(language: string): void {
    this.currentLanguage = language;
  }

  /**
   * 获取当前语言
   */
  public getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  /**
   * 翻译文本
   */
  public async translateText(text: string, targetLanguage: string = this.currentLanguage): Promise<string> {
    if (!text || !text.trim()) {
      return text;
    }

    console.log(`在线翻译请求: "${text.substring(0, 50)}..." -> ${targetLanguage}`);

    // 移除缓存机制，直接进行在线翻译

    try {
      let translatedText = text;

      // 优先使用在线翻译服务
      console.log('使用翻译提供商:', this.config.preferredProvider);

      try {
        // 优先尝试配置的在线翻译服务
        if (this.config.preferredProvider === TranslationProvider.GOOGLE && this.config.googleApiKey) {
          translatedText = await this.translateWithGoogle(text, targetLanguage);
          console.log('Google翻译成功');
        } else if (this.config.preferredProvider === TranslationProvider.SILICONFLOW && this.config.siliconflowApiKey) {
          translatedText = await this.translateWithSiliconFlow(text, targetLanguage);
          console.log('硅基流动翻译成功');
        } else if (this.config.preferredProvider === TranslationProvider.GLM && this.config.glmApiKey) {
          translatedText = await this.translateWithGLM(text, targetLanguage);
          console.log('智谱清言翻译成功');
        } else if (this.config.preferredProvider === TranslationProvider.CUSTOM && this.config.customApis.length > 0) {
          translatedText = await this.translateWithCustomApi(text, targetLanguage);
          console.log('自定义API翻译成功');
        } else if (this.config.preferredProvider === TranslationProvider.MYMEMORY) {
          translatedText = await this.translateWithMyMemory(text, targetLanguage);
          console.log('MyMemory翻译成功');
        } else {
          // 如果没有配置在线服务，尝试免费的MyMemory
          try {
            translatedText = await this.translateWithMyMemory(text, targetLanguage);
            console.log('降级到MyMemory翻译成功');
          } catch (error) {
            console.warn('MyMemory翻译失败，使用本地翻译:', error);
            translatedText = await this.translateWithLocal(text, targetLanguage);
          }
        }
      } catch (error) {
        console.warn(`主要翻译提供商失败: ${error.message}`);

        // 如果启用了降级，尝试其他在线服务
        if (this.config.enableFallback) {
          console.log('主要翻译失败，尝试降级翻译');

          try {
            // 尝试免费的MyMemory服务
            translatedText = await this.translateWithMyMemory(text, targetLanguage);
            console.log('降级到MyMemory翻译成功');
          } catch (fallbackError) {
            console.warn('MyMemory降级翻译失败:', fallbackError);
            // 最后使用本地翻译作为兜底
            translatedText = await this.translateWithLocal(text, targetLanguage);
            console.log('使用本地翻译作为兜底');
          }
        } else {
          translatedText = text; // 不启用降级时返回原文
        }
      }

      // 移除缓存机制，直接返回翻译结果
      return translatedText;
    } catch (error) {
      console.error('Translation failed:', error);
      return text; // 翻译失败时返回原文
    }
  }

  /**
   * 批量翻译
   */
  public async translateBatch(texts: string[], targetLanguage: string = this.currentLanguage): Promise<string[]> {
    const promises = texts.map(text => this.translateText(text, targetLanguage));
    return Promise.all(promises);
  }

  /**
   * 使用Google翻译API
   */
  private async translateWithGoogle(text: string, targetLanguage: string): Promise<string> {
    if (!this.config.googleApiKey) {
      throw new Error('Google API key not configured');
    }

    const url = `https://translation.googleapis.com/language/translate/v2?key=${this.config.googleApiKey}`;

    // Google翻译的语言代码映射
    const googleLangMap: Record<string, string> = {
      'zh': 'zh-cn',
      'ja': 'ja',
      'ko': 'ko',
      'en': 'en',
      'fr': 'fr',
      'de': 'de',
      'es': 'es',
      'ru': 'ru'
    };

    const targetLang = googleLangMap[targetLanguage] || targetLanguage;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: text,
        target: targetLang,
        source: 'auto', // 让Google自动检测源语言
        format: 'text'
      })
    });

    if (!response.ok) {
      throw new Error(`Google Translate API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.data && data.data.translations && data.data.translations.length > 0) {
      return data.data.translations[0].translatedText;
    }

    throw new Error('Invalid response from Google Translate API');
  }

  /**
   * 使用硅基流动翻译
   */
  private async translateWithSiliconFlow(text: string, targetLanguage: string): Promise<string> {
    if (!this.config.siliconflowApiKey) {
      throw new Error('SiliconFlow API key not configured');
    }

    const langMap: Record<string, string> = {
      'zh': '中文',
      'en': 'English',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语'
    };

    const targetLang = langMap[targetLanguage] || targetLanguage;

    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.siliconflowApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'Qwen/Qwen2.5-7B-Instruct',
        messages: [
          {
            role: 'user',
            content: `请将以下文本翻译成${targetLang}，只返回翻译结果，不要添加任何解释：\n\n${text}`
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`SiliconFlow API error: ${response.status}`);
    }

    const data = await response.json();
    if (data.choices && data.choices[0] && data.choices[0].message) {
      return data.choices[0].message.content.trim();
    }

    throw new Error('Invalid response from SiliconFlow API');
  }

  /**
   * 使用智谱清言翻译
   */
  private async translateWithGLM(text: string, targetLanguage: string): Promise<string> {
    if (!this.config.glmApiKey) {
      throw new Error('GLM API key not configured');
    }

    const langMap: Record<string, string> = {
      'zh': '中文',
      'en': 'English',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语'
    };

    const targetLang = langMap[targetLanguage] || targetLanguage;

    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.glmApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'glm-4-flash',
        messages: [
          {
            role: 'user',
            content: `请将以下文本翻译成${targetLang}，只返回翻译结果，不要添加任何解释：\n\n${text}`
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`GLM API error: ${response.status}`);
    }

    const data = await response.json();
    if (data.choices && data.choices[0] && data.choices[0].message) {
      return data.choices[0].message.content.trim();
    }

    throw new Error('Invalid response from GLM API');
  }

  /**
   * 使用自定义API翻译
   */
  private async translateWithCustomApi(text: string, targetLanguage: string): Promise<string> {
    const customApi = this.config.customApis[0]; // 使用第一个配置的自定义API
    if (!customApi) {
      throw new Error('No custom API configured');
    }

    const langMap: Record<string, string> = {
      'zh': '中文',
      'en': 'English',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语'
    };

    const targetLang = langMap[targetLanguage] || targetLanguage;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...customApi.headers
    };

    if (customApi.apiKey) {
      headers['Authorization'] = `Bearer ${customApi.apiKey}`;
    }

    let requestBody: any;

    if (customApi.requestFormat === 'openai') {
      requestBody = {
        model: customApi.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: `请将以下文本翻译成${targetLang}，只返回翻译结果，不要添加任何解释：\n\n${text}`
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      };
    } else {
      // 自定义格式，用户需要自己配置
      requestBody = {
        text: text,
        target_language: targetLang,
        source_language: 'auto'
      };
    }

    const response = await fetch(customApi.endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Custom API error: ${response.status}`);
    }

    const data = await response.json();

    if (customApi.requestFormat === 'openai') {
      if (data.choices && data.choices[0] && data.choices[0].message) {
        return data.choices[0].message.content.trim();
      }
    } else {
      // 假设自定义API返回 { translated_text: "..." }
      if (data.translated_text) {
        return data.translated_text;
      }
    }

    throw new Error('Invalid response from custom API');
  }

  /**
   * 使用MyMemory免费翻译API
   */
  private async translateWithMyMemory(text: string, targetLanguage: string): Promise<string> {
    console.log(`MyMemory翻译: "${text.substring(0, 50)}..." -> ${targetLanguage}`);

    // MyMemory API支持的语言代码映射
    const langMap: Record<string, string> = {
      'zh': 'zh-CN',
      'ja': 'ja',
      'ko': 'ko',
      'en': 'en',
      'fr': 'fr',
      'de': 'de',
      'es': 'es',
      'ru': 'ru'
    };

    // 智能检测源语言
    const sourceLang = this.detectSourceLanguage(text);
    const targetLang = langMap[targetLanguage] || targetLanguage;

    console.log(`语言对: ${sourceLang} -> ${targetLang}`);

    const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${sourceLang}|${targetLang}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`MyMemory API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('MyMemory响应:', data);

      if (data.responseStatus === 200 && data.responseData && data.responseData.translatedText) {
        const translatedText = data.responseData.translatedText;
        // 检查是否是有效的翻译
        if (!translatedText.includes('PLEASE SELECT') &&
            !translatedText.includes('INVALID') &&
            !translatedText.includes('NO QUERY SPECIFIED') &&
            translatedText.trim() !== text.trim()) {
          console.log(`MyMemory翻译成功: "${text}" -> "${translatedText}"`);
          return translatedText;
        }
      }

      throw new Error(`MyMemory返回无效翻译: ${JSON.stringify(data)}`);
    } catch (error) {
      console.error('MyMemory翻译失败:', error);
      throw error;
    }
  }

  /**
   * 智能检测源语言
   */
  private detectSourceLanguage(text: string): string {
    // 检测中文字符
    const chineseRegex = /[\u4e00-\u9fff]/;
    // 检测日文字符
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
    // 检测韩文字符
    const koreanRegex = /[\uac00-\ud7af]/;
    // 检测英文字符
    const englishRegex = /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/;

    if (chineseRegex.test(text)) {
      return 'zh-CN';
    } else if (japaneseRegex.test(text)) {
      return 'ja';
    } else if (koreanRegex.test(text)) {
      return 'ko';
    } else if (englishRegex.test(text)) {
      return 'en';
    } else {
      // 默认使用自动检测
      return 'auto';
    }
  }

  /**
   * 使用本地词汇翻译
   */
  private async translateWithLocal(text: string, targetLanguage: string): Promise<string> {
    console.log(`本地翻译: "${text.substring(0, 50)}..." -> ${targetLanguage}`);

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 扩展的翻译映射
    const translations: Record<string, Record<string, string>> = {
      'zh': {
        // 完整项目描述翻译
        'Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.': 'Claude-Flow v2.0.0 Alpha代表了AI驱动开发编排的革命性飞跃。采用企业级架构、先进的群体智能和无缝的Claude Code集成，从头开始构建。',
        'Synaptic Neural Mesh: a self-evolving, peer to peer neural fabric where every element is an agent, learning and communicating across a globally coordinated DAG substrate.': '突触神经网格：一个自我进化的点对点神经结构，其中每个元素都是一个代理，在全球协调的DAG基底上学习和通信。',
        'AI power Dev using the rUv approach': '使用rUv方法的AI驱动开发',
        'A blazing-fast, memory-safe neural network library for Rust that brings the power of FANN to the modern world. (Rust项目)': '一个极速、内存安全的Rust神经网络库，将FANN的强大功能带入现代世界。(Rust项目)',
        'The Screenplay Generator is a web application that allows users to generate a TV or movie screenplay based on a scenic template.': '剧本生成器是一个网络应用程序，允许用户基于场景模板生成电视或电影剧本。',
        'Everything you need to translate your app using Supabase + AI': '使用Supabase + AI翻译应用程序所需的一切',
        'rUv-Engineer - let\'s you describe UI using your imagination, then see it rendered live.': 'rUv-Engineer - 让你用想象力描述UI，然后实时看到渲染效果。',
        'A trading script for KuCoin that continuously places buy and sell orders based on market data and a predefined strategy using OpenAI\'s GPT 3.5': '一个KuCoin交易脚本，基于市场数据和使用OpenAI GPT 3.5的预定义策略持续下买卖单。',
        'AWS Dev environment': 'AWS开发环境',
        'ChatGPT Web Surfer Plugin': 'ChatGPT网页浏览器插件',
        'a comprehensive diffusion-based code refinement model': '一个全面的基于扩散的代码优化模型',
        'AI-Driven Decentralized Organization (AIDO) using Supabase and Langchain.js. It includes all necessary files, configurations, and deployment components to get started. Feel free to extend and customize the system based on your requirements.': 'AI驱动的去中心化组织(AIDO)，使用Supabase和Langchain.js。包含所有必要的文件、配置和部署组件以开始使用。可以根据需求自由扩展和定制系统。',
        'Sample Starting': '示例启动',

        // 项目名称翻译
        'Code Mesh': '代码网格',
        'claude-flow': 'Claude流程',
        'code-mesh': '代码网格',
        'Synaptic-Mesh': '突触网格',
        'sparc': '稀疏编码',
        'dsp.ts': 'DSP TypeScript项目',
        'rUv-dev': 'rUv开发',
        'rUv-FANN': 'rUv-FANN',

        // 基础项目类型
        'A simple project': '一个简单的项目',
        'Code repository for learning': '用于学习的代码库',
        'Open source tool': '开源工具',
        'Website project': '网站项目',
        'Mobile application': '移动应用',
        'Desktop application': '桌面应用',
        'Library and framework': '库和框架',
        'Documentation and tutorial': '文档和教程',
        'Game project': '游戏项目',
        'Data science': '数据科学',
        'Machine learning': '机器学习',
        'Artificial intelligence': '人工智能',
        'Blockchain': '区块链',
        'Internet of Things': '物联网',
        'Cloud computing': '云计算',
        'Microservices': '微服务',
        'Frontend development': '前端开发',
        'Backend development': '后端开发',
        'Full-stack development': '全栈开发',
        'Mobile development': '移动开发',

        // 技术词汇
        'represents': '代表',
        'revolutionary': '革命性的',
        'leap': '飞跃',
        'AI-powered': 'AI驱动的',
        'development': '开发',
        'orchestration': '编排',
        'Built from the ground up': '从头开始构建',
        'enterprise-grade': '企业级',
        'architecture': '架构',
        'advanced': '先进的',
        'swarm intelligence': '群体智能',
        'seamless': '无缝的',
        'integration': '集成',
        'self-evolving': '自我进化的',
        'peer to peer': '点对点',
        'neural fabric': '神经结构',
        'neural network': '神经网络',
        'element': '元素',
        'agent': '代理',
        'learning': '学习',
        'communicating': '通信',
        'globally coordinated': '全球协调的',
        'substrate': '基底',
        'blazing-fast': '极速',
        'memory-safe': '内存安全',
        'library': '库',
        'framework': '框架',
        'application': '应用程序',
        'web application': '网络应用程序',
        'allows users': '允许用户',
        'generate': '生成',
        'based on': '基于',
        'template': '模板',
        'translate': '翻译',
        'using': '使用',
        'describe': '描述',
        'imagination': '想象力',
        'rendered': '渲染',
        'live': '实时',
        'trading': '交易',
        'script': '脚本',
        'continuously': '持续',
        'places': '下单',
        'buy and sell': '买卖',
        'orders': '订单',
        'market data': '市场数据',
        'predefined': '预定义',
        'strategy': '策略',
        'environment': '环境',
        'plugin': '插件',
        'comprehensive': '全面的',
        'diffusion-based': '基于扩散的',
        'refinement': '优化',
        'model': '模型',
        'decentralized': '去中心化',
        'organization': '组织',
        'includes': '包含',
        'necessary': '必要的',
        'files': '文件',
        'configurations': '配置',
        'deployment': '部署',
        'components': '组件',
        'get started': '开始使用',
        'feel free': '可以自由',
        'extend': '扩展',
        'customize': '定制',
        'system': '系统',
        'requirements': '需求',
        'sample': '示例',
        'starting': '启动',

        // 常见动词
        'build': '构建',
        'create': '创建',
        'develop': '开发',
        'design': '设计',
        'implement': '实现',
        'deploy': '部署',
        'test': '测试',
        'manage': '管理',
        'monitor': '监控',
        'analyze': '分析',
        'optimize': '优化',
        'configure': '配置',
        'integrate': '集成',
        'automate': '自动化',
        'scale': '扩展',
        'maintain': '维护',
        'update': '更新',
        'upgrade': '升级',

        // 常见形容词
        'simple': '简单的',
        'complex': '复杂的',
        'powerful': '强大的',
        'efficient': '高效的',
        'fast': '快速的',
        'reliable': '可靠的',
        'scalable': '可扩展的',
        'flexible': '灵活的',
        'modern': '现代的',
        'innovative': '创新的',
        'intelligent': '智能的',
        'automated': '自动化的',
        'real-time': '实时的',
        'cross-platform': '跨平台的',
        'open-source': '开源的',
        'lightweight': '轻量级的',
        'responsive': '响应式的',
        'interactive': '交互式的',
        'user-friendly': '用户友好的',
      },
      'en': {
        // 基础词汇
        '一个简单的项目': 'A simple project',
        '用于学习的代码库': 'Code repository for learning',
        '开源工具': 'Open source tool',
        '网站项目': 'Website project',
        '移动应用': 'Mobile application',
        '桌面应用': 'Desktop application',
        '库和框架': 'Library and framework',
        '文档和教程': 'Documentation and tutorial',
        '游戏项目': 'Game project',
        '数据科学': 'Data science',
        '机器学习': 'Machine learning',
        '人工智能': 'Artificial intelligence',
        '区块链': 'Blockchain',
        '物联网': 'Internet of Things',
        '云计算': 'Cloud computing',
        '微服务': 'Microservices',
        '前端开发': 'Frontend development',
        '后端开发': 'Backend development',
        '全栈开发': 'Full-stack development',
        '移动开发': 'Mobile development',

        // 常见技术词汇
        'JavaScript': 'JavaScript',
        'TypeScript': 'TypeScript',
        'Python': 'Python',
        'Java': 'Java',
        'C++': 'C++',
        'Go': 'Go',
        'Rust': 'Rust',
        'PHP': 'PHP',
        'Ruby': 'Ruby',
        'Swift': 'Swift',
        'Kotlin': 'Kotlin',
        'React': 'React',
        'Vue': 'Vue',
        'Angular': 'Angular',
        'Node.js': 'Node.js',
        'Express': 'Express',
        'Django': 'Django',
        'Flask': 'Flask',
        'Spring': 'Spring',
        'Laravel': 'Laravel',
        'Rails': 'Rails',
        'Docker': 'Docker',
        'Kubernetes': 'Kubernetes',
        'MongoDB': 'MongoDB',
        'MySQL': 'MySQL',
        'PostgreSQL': 'PostgreSQL',
        'Redis': 'Redis',
        'Elasticsearch': 'Elasticsearch',
        'GraphQL': 'GraphQL',
        'REST API': 'REST API',
        'WebSocket': 'WebSocket',
        'OAuth': 'OAuth',
        'JWT': 'JWT',
        'SSL': 'SSL',
        'HTTPS': 'HTTPS',
        'CI/CD': 'CI/CD',
        'DevOps': 'DevOps',
        'AWS': 'AWS',
        'Azure': 'Azure',
        'Google Cloud': 'Google Cloud',
        'Firebase': 'Firebase',
        'Vercel': 'Vercel',
        'Netlify': 'Netlify',
        'GitHub': 'GitHub',
        'GitLab': 'GitLab',
        'Bitbucket': 'Bitbucket',
        'npm': 'npm',
        'yarn': 'yarn',
        'webpack': 'webpack',
        'Vite': 'Vite',
        'Babel': 'Babel',
        'ESLint': 'ESLint',
        'Prettier': 'Prettier',
        'Jest': 'Jest',
        'Cypress': 'Cypress',
        'Selenium': 'Selenium',
        'TensorFlow': 'TensorFlow',
        'PyTorch': 'PyTorch',
        'Scikit-learn': 'Scikit-learn',
        'Pandas': 'Pandas',
        'NumPy': 'NumPy',
        'Matplotlib': 'Matplotlib',
        'Jupyter': 'Jupyter',
        'Anaconda': 'Anaconda',

        // 常见描述词汇
        '简单': 'simple',
        '复杂': 'complex',
        '高效': 'efficient',
        '快速': 'fast',
        '安全': 'secure',
        '稳定': 'stable',
        '可扩展': 'scalable',
        '轻量级': 'lightweight',
        '现代': 'modern',
        '强大': 'powerful',
        '灵活': 'flexible',
        '易用': 'easy to use',
        '开源': 'open source',
        '免费': 'free',
        '商业': 'commercial',
        '企业级': 'enterprise',
        '跨平台': 'cross-platform',
        '响应式': 'responsive',
        '实时': 'real-time',
        '异步': 'asynchronous',
        '同步': 'synchronous',
        '分布式': 'distributed',
        '集中式': 'centralized',
        '模块化': 'modular',
        '组件化': 'component-based',
        '面向对象': 'object-oriented',
        '函数式': 'functional',
        '声明式': 'declarative',
        '命令式': 'imperative',
      },
      'ja': {
        // 基础词汇
        '一个简单的项目': 'シンプルなプロジェクト',
        '用于学习的代码库': '学習用のコードリポジトリ',
        '开源工具': 'オープンソースツール',
        '网站项目': 'ウェブサイトプロジェクト',
        '移动应用': 'モバイルアプリケーション',
        '桌面应用': 'デスクトップアプリケーション',
        '库和框架': 'ライブラリとフレームワーク',
        '文档和教程': 'ドキュメントとチュートリアル',
        '游戏项目': 'ゲームプロジェクト',
        '数据科学': 'データサイエンス',
        '机器学习': '機械学習',
        '人工智能': '人工知能',
        '区块链': 'ブロックチェーン',
        '物联网': 'モノのインターネット',
        '云计算': 'クラウドコンピューティング',
        '微服务': 'マイクロサービス',
        '前端开发': 'フロントエンド開発',
        '后端开发': 'バックエンド開発',
        '全栈开发': 'フルスタック開発',
        '移动开发': 'モバイル開発',

        // 英文项目描述翻译
        'Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.': 'Claude-Flow v2.0.0 Alphaは、AI駆動開発オーケストレーションにおける革命的な飛躍を表しています。エンタープライズグレードのアーキテクチャ、高度なスウォームインテリジェンス、シームレスなClaude Code統合により、ゼロから構築されています。',
        'Code Mesh': 'コードメッシュ',
        'Synaptic Neural Mesh: a self-evolving, peer to peer neural fabric where every element is an agent, learning and communicating across a globally coordinated DAG substrate.': 'シナプティックニューラルメッシュ：すべての要素がエージェントである自己進化型のピアツーピアニューラルファブリック。グローバルに調整されたDAG基盤全体で学習と通信を行います。',
        'represents': '表す',
        'revolutionary': '革命的な',
        'leap': '飛躍',
        'AI-powered': 'AI駆動の',
        'development': '開発',
        'orchestration': 'オーケストレーション',
        'Built from the ground up': 'ゼロから構築',
        'enterprise-grade': 'エンタープライズグレード',
        'architecture': 'アーキテクチャ',
        'advanced': '高度な',
        'swarm intelligence': 'スウォームインテリジェンス',
        'seamless': 'シームレス',
        'integration': '統合',
        'self-evolving': '自己進化型',
        'peer to peer': 'ピアツーピア',
        'neural fabric': 'ニューラルファブリック',
        'element': '要素',
        'agent': 'エージェント',
        'learning': '学習',
        'communicating': '通信',
        'globally coordinated': 'グローバルに調整された',
        'substrate': '基盤',
      }
    };

    // 尝试精确匹配
    if (translations[targetLanguage] && translations[targetLanguage][text]) {
      console.log(`精确匹配翻译: "${text}" -> "${translations[targetLanguage][text]}"`);
      return translations[targetLanguage][text];
    }

    // 智能翻译：处理多个词汇的组合
    if (translations[targetLanguage]) {
      let translatedText = text;
      let hasTranslation = false;

      // 按词汇长度排序，优先匹配长词汇
      const sortedEntries = Object.entries(translations[targetLanguage])
        .sort(([a], [b]) => b.length - a.length);

      // 第一轮：精确匹配完整短语
      for (const [key, value] of sortedEntries) {
        if (translatedText.toLowerCase() === key.toLowerCase()) {
          console.log(`精确匹配翻译: "${text}" -> "${value}"`);
          return value;
        }
      }

      // 第二轮：部分匹配和替换
      for (const [key, value] of sortedEntries) {
        if (translatedText.toLowerCase().includes(key.toLowerCase())) {
          // 使用正则表达式进行大小写不敏感的替换
          const regex = new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
          const newText = translatedText.replace(regex, value);
          if (newText !== translatedText) {
            translatedText = newText;
            hasTranslation = true;
          }
        }
      }

      // 第三轮：智能分词翻译（处理复合词）
      if (!hasTranslation) {
        translatedText = this.smartWordTranslation(text, translations[targetLanguage]);
        hasTranslation = translatedText !== text;
      }

      if (hasTranslation) {
        console.log(`智能翻译成功: "${text}" -> "${translatedText}"`);
        return translatedText;
      }
    }

    // 智能处理技术术语
    if (targetLanguage === 'zh') {
      const result = this.translateTechTerms(text);
      if (result !== text) {
        console.log(`技术术语翻译: "${text}" -> "${result}"`);
        return result;
      }
    }

    // 如果是英文文本，直接返回（可能已经是英文）
    if (targetLanguage === 'en' && /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/.test(text)) {
      return text;
    }

    // 如果没有找到翻译，返回原文
    console.log(`本地翻译未找到匹配: "${text}" (目标语言: ${targetLanguage})`);
    return text;
  }

  /**
   * 翻译技术术语
   */
  private translateTechTerms(text: string): string {
    // 技术术语映射
    const techTranslations: Record<string, string> = {
      'typescript': 'TypeScript',
      'javascript': 'JavaScript',
      'python': 'Python',
      'rust': 'Rust',
      'learning': '学习',
      'project': '项目',
      'projects': '项目',
      'code': '代码',
      'repository': '仓库',
      'repositories': '仓库',
      'library': '库',
      'libraries': '库',
      'framework': '框架',
      'frameworks': '框架',
      'tool': '工具',
      'tools': '工具',
      'application': '应用程序',
      'applications': '应用程序',
      'app': '应用',
      'apps': '应用',
      'website': '网站',
      'websites': '网站',
      'mobile': '移动',
      'desktop': '桌面',
      'web': '网页',
      'api': 'API',
      'apis': 'API',
      'database': '数据库',
      'databases': '数据库',
      'server': '服务器',
      'servers': '服务器',
      'client': '客户端',
      'clients': '客户端',
      'service': '服务',
      'services': '服务',
      'component': '组件',
      'components': '组件',
      'module': '模块',
      'modules': '模块',
      'package': '包',
      'packages': '包',
      'plugin': '插件',
      'plugins': '插件',
      'extension': '扩展',
      'extensions': '扩展',
      'template': '模板',
      'templates': '模板',
      'example': '示例',
      'examples': '示例',
      'demo': '演示',
      'demos': '演示',
      'tutorial': '教程',
      'tutorials': '教程',
      'documentation': '文档',
      'docs': '文档',
      'guide': '指南',
      'guides': '指南',
      'starter': '启动器',
      'boilerplate': '样板',
      'scaffold': '脚手架',
      'generator': '生成器',
      'builder': '构建器',
      'compiler': '编译器',
      'transpiler': '转译器',
      'bundler': '打包器',
      'linter': '代码检查器',
      'formatter': '格式化器',
      'testing': '测试',
      'test': '测试',
      'tests': '测试',
      'spec': '规范',
      'specs': '规范',
      'benchmark': '基准测试',
      'performance': '性能',
      'optimization': '优化',
      'security': '安全',
      'authentication': '认证',
      'authorization': '授权',
      'middleware': '中间件',
      'router': '路由器',
      'routing': '路由',
      'controller': '控制器',
      'model': '模型',
      'view': '视图',
      'helper': '助手',
      'utility': '工具',
      'utilities': '工具',
      'util': '工具',
      'utils': '工具',
      'config': '配置',
      'configuration': '配置',
      'settings': '设置',
      'environment': '环境',
      'development': '开发',
      'production': '生产',
      'staging': '预发布',
      'deployment': '部署',
      'build': '构建',
      'release': '发布',
      'version': '版本',
      'update': '更新',
      'upgrade': '升级',
      'migration': '迁移',
      'backup': '备份',
      'restore': '恢复',
      'monitoring': '监控',
      'logging': '日志',
      'debugging': '调试',
      'profiling': '性能分析',
      'analytics': '分析',
      'metrics': '指标',
      'dashboard': '仪表板',
      'admin': '管理',
      'management': '管理',
      'automation': '自动化',
      'workflow': '工作流',
      'pipeline': '管道',
      'integration': '集成',
      'continuous': '持续',
      'devops': 'DevOps',
      'infrastructure': '基础设施',
      'cloud': '云',
      'container': '容器',
      'docker': 'Docker',
      'kubernetes': 'Kubernetes',
      'microservice': '微服务',
      'microservices': '微服务',
      'serverless': '无服务器',
      'edge': '边缘',
      'cdn': 'CDN',
      'cache': '缓存',
      'storage': '存储',
      'file': '文件',
      'files': '文件',
      'folder': '文件夹',
      'folders': '文件夹',
      'directory': '目录',
      'directories': '目录',
      'path': '路径',
      'paths': '路径',
      'url': 'URL',
      'urls': 'URL',
      'link': '链接',
      'links': '链接',
      'image': '图片',
      'images': '图片',
      'video': '视频',
      'videos': '视频',
      'audio': '音频',
      'media': '媒体',
      'content': '内容',
      'data': '数据',
      'json': 'JSON',
      'xml': 'XML',
      'csv': 'CSV',
      'excel': 'Excel',
      'pdf': 'PDF',
      'markdown': 'Markdown',
      'html': 'HTML',
      'css': 'CSS',
      'sass': 'Sass',
      'scss': 'SCSS',
      'less': 'Less',
      'stylus': 'Stylus',
      'responsive': '响应式',
      'mobile-first': '移动优先',
      'progressive': '渐进式',
      'single-page': '单页',
      'multi-page': '多页',
      'static': '静态',
      'dynamic': '动态',
      'interactive': '交互式',
      'real-time': '实时',
      'live': '实时',
      'streaming': '流式',
      'websocket': 'WebSocket',
      'socket': 'Socket',
      'http': 'HTTP',
      'https': 'HTTPS',
      'ssl': 'SSL',
      'tls': 'TLS',
      'oauth': 'OAuth',
      'jwt': 'JWT',
      'session': '会话',
      'cookie': 'Cookie',
      'token': '令牌',
      'encryption': '加密',
      'hash': '哈希',
      'algorithm': '算法',
      'machine learning': '机器学习',
      'artificial intelligence': '人工智能',
      'deep learning': '深度学习',
      'neural network': '神经网络',
      'data science': '数据科学',
      'big data': '大数据',
      'blockchain': '区块链',
      'cryptocurrency': '加密货币',
      'bitcoin': '比特币',
      'ethereum': '以太坊',
      'smart contract': '智能合约',
      'iot': '物联网',
      'internet of things': '物联网',
      'augmented reality': '增强现实',
      'virtual reality': '虚拟现实',
      'mixed reality': '混合现实',
      'game': '游戏',
      'games': '游戏',
      'gaming': '游戏',
      'engine': '引擎',
      'graphics': '图形',
      'rendering': '渲染',
      '3d': '3D',
      '2d': '2D',
      'animation': '动画',
      'physics': '物理',
      'simulation': '模拟',
      'editor': '编辑器',
      'ide': 'IDE',
      'vscode': 'VSCode',
      'vim': 'Vim',
      'emacs': 'Emacs',
      'git': 'Git',
      'github': 'GitHub',
      'gitlab': 'GitLab',
      'bitbucket': 'Bitbucket',
      'npm': 'npm',
      'yarn': 'Yarn',
      'pnpm': 'pnpm',
      'webpack': 'Webpack',
      'vite': 'Vite',
      'rollup': 'Rollup',
      'parcel': 'Parcel',
      'babel': 'Babel',
      'eslint': 'ESLint',
      'prettier': 'Prettier',
      'jest': 'Jest',
      'mocha': 'Mocha',
      'chai': 'Chai',
      'cypress': 'Cypress',
      'selenium': 'Selenium',
      'playwright': 'Playwright',
      'storybook': 'Storybook',
    };

    let result = text;
    let hasTranslation = false;

    // 按长度排序，优先匹配长词组
    const sortedEntries = Object.entries(techTranslations)
      .sort(([a], [b]) => b.length - a.length);

    for (const [english, chinese] of sortedEntries) {
      const regex = new RegExp(`\\b${english.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
      if (regex.test(result)) {
        result = result.replace(regex, chinese);
        hasTranslation = true;
      }
    }

    return hasTranslation ? result : text;
  }

  /**
   * 智能分词翻译
   */
  private smartWordTranslation(text: string, dictionary: Record<string, string>): string {
    // 分割文本为单词
    const words = text.split(/\s+/);
    let translatedWords: string[] = [];
    let hasTranslation = false;

    for (const word of words) {
      // 清理单词（移除标点符号）
      const cleanWord = word.replace(/[^\w\s-]/g, '');
      let translated = false;

      // 尝试翻译单词
      for (const [key, value] of Object.entries(dictionary)) {
        if (cleanWord.toLowerCase() === key.toLowerCase()) {
          // 保持原始标点符号
          const punctuation = word.replace(cleanWord, '');
          translatedWords.push(value + punctuation);
          translated = true;
          hasTranslation = true;
          break;
        }
      }

      // 如果没有找到翻译，保持原词
      if (!translated) {
        translatedWords.push(word);
      }
    }

    return hasTranslation ? translatedWords.join(' ') : text;
  }

  /**
   * 清除翻译缓存（已移除缓存机制）
   */
  public clearCache(): void {
    console.log('缓存机制已移除，使用在线翻译');
  }

  /**
   * 获取缓存大小（已移除缓存机制）
   */
  public getCacheSize(): number {
    return 0;
  }
}

// 导出单例实例
export const translationService = TranslationService.getInstance();
