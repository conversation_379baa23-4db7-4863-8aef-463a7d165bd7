[{"type": "update_check", "status": "completed", "message": "Update check completed successfully", "timestamp": "2025-07-20T12:54:57.860Z"}, {"type": "update_check", "status": "failed", "message": "Update check failed: TypeError: Cannot set properties of undefined (setting 'lastCheck')", "error": "<PERSON><PERSON> set properties of undefined (setting 'lastCheck')", "timestamp": "2025-07-20T12:54:55.950Z"}, {"type": "update_check", "status": "completed", "message": "Update check completed successfully", "timestamp": "2025-07-20T11:54:57.143Z"}, {"type": "update_check", "status": "completed", "message": "Update check completed successfully", "timestamp": "2025-07-20T11:54:57.050Z"}]