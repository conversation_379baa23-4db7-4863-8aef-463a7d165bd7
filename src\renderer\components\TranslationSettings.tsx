import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { translationService, TranslationProvider } from '../services/translationService';

interface TranslationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CustomApiConfig {
  name: string;
  endpoint: string;
  apiKey: string;
  headers?: Record<string, string>;
  requestFormat: 'openai' | 'custom';
  model?: string;
}

export const TranslationSettings: React.FC<TranslationSettingsProps> = ({ isOpen, onClose }) => {
  const [config, setConfig] = useState(translationService.getConfig());
  const [newCustomApi, setNewCustomApi] = useState<CustomApiConfig>({
    name: '',
    endpoint: '',
    apiKey: '',
    requestFormat: 'openai',
    model: ''
  });
  const [testText, setTestText] = useState('Hello, world!');
  const [testResult, setTestResult] = useState('');
  const [isTestingTranslation, setIsTestingTranslation] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setConfig(translationService.getConfig());
    }
  }, [isOpen]);

  const handleSave = () => {
    translationService.setConfig(config);
    onClose();
  };

  const handleProviderChange = (provider: TranslationProvider) => {
    setConfig(prev => ({ ...prev, preferredProvider: provider }));
  };

  const handleApiKeyChange = (field: string, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const addCustomApi = () => {
    if (newCustomApi.name && newCustomApi.endpoint) {
      setConfig(prev => ({
        ...prev,
        customApis: [...prev.customApis, { ...newCustomApi }]
      }));
      setNewCustomApi({
        name: '',
        endpoint: '',
        apiKey: '',
        requestFormat: 'openai',
        model: ''
      });
    }
  };

  const removeCustomApi = (index: number) => {
    setConfig(prev => ({
      ...prev,
      customApis: prev.customApis.filter((_, i) => i !== index)
    }));
  };

  const testTranslation = async () => {
    setIsTestingTranslation(true);
    try {
      // 临时应用配置进行测试
      translationService.setConfig(config);
      const result = await translationService.translateText(testText, 'zh');
      setTestResult(result);
    } catch (error) {
      setTestResult(`测试失败: ${error.message}`);
    } finally {
      setIsTestingTranslation(false);
    }
  };

  if (!isOpen) {
    console.log('TranslationSettings: isOpen为false，不渲染');
    return null;
  }

  console.log('TranslationSettings: 开始渲染模态框');

  const modalContent = (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{
        zIndex: 999999,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto shadow-2xl"
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '24px',
          width: '100%',
          maxWidth: '1024px',
          maxHeight: '90vh',
          overflowY: 'auto',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">翻译设置</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        <div className="space-y-6">
          {/* 翻译提供商选择 */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">翻译提供商</h3>
            <div className="grid grid-cols-2 gap-3">
              {Object.values(TranslationProvider).map(provider => (
                <label key={provider} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="provider"
                    value={provider}
                    checked={config.preferredProvider === provider}
                    onChange={() => handleProviderChange(provider)}
                    className="text-blue-600"
                  />
                  <span className="text-gray-700 dark:text-gray-300">
                    {provider === TranslationProvider.SILICONFLOW && '硅基流动'}
                    {provider === TranslationProvider.GLM && '智谱清言'}
                    {provider === TranslationProvider.GOOGLE && 'Google翻译'}
                    {provider === TranslationProvider.BAIDU && '百度翻译'}
                    {provider === TranslationProvider.TENCENT && '腾讯翻译'}
                    {provider === TranslationProvider.ALIBABA && '阿里翻译'}
                    {provider === TranslationProvider.CUSTOM && '自定义API'}
                    {provider === TranslationProvider.MYMEMORY && 'MyMemory'}
                    {provider === TranslationProvider.LOCAL && '本地翻译'}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* API密钥配置 */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">API密钥配置</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  硅基流动 API Key
                </label>
                <input
                  type="password"
                  value={config.siliconflowApiKey || ''}
                  onChange={(e) => handleApiKeyChange('siliconflowApiKey', e.target.value)}
                  placeholder="sk-..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  获取地址: <a href="https://cloud.siliconflow.cn" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">https://cloud.siliconflow.cn</a>
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  智谱清言 API Key
                </label>
                <input
                  type="password"
                  value={config.glmApiKey || ''}
                  onChange={(e) => handleApiKeyChange('glmApiKey', e.target.value)}
                  placeholder="..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  获取地址: <a href="https://open.bigmodel.cn" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">https://open.bigmodel.cn</a>
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Google翻译 API Key
                </label>
                <input
                  type="password"
                  value={config.googleApiKey || ''}
                  onChange={(e) => handleApiKeyChange('googleApiKey', e.target.value)}
                  placeholder="AIza..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  获取地址: <a href="https://console.cloud.google.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Cloud Console</a>
                </p>
              </div>
            </div>
          </div>

          {/* 翻译测试 */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">翻译测试</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  测试文本
                </label>
                <input
                  type="text"
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <button
                onClick={testTranslation}
                disabled={isTestingTranslation}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isTestingTranslation ? '测试中...' : '测试翻译'}
              </button>
              
              {testResult && (
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <span className="text-gray-900 dark:text-white">{testResult}</span>
                </div>
              )}
            </div>
          </div>

          {/* 其他设置 */}
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.enableFallback}
                onChange={(e) => setConfig(prev => ({ ...prev, enableFallback: e.target.checked }))}
                className="text-blue-600"
              />
              <span className="text-gray-700 dark:text-gray-300">启用降级翻译（翻译失败时自动尝试其他方式）</span>
            </label>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            保存设置
          </button>
        </div>
      </div>
    </div>
  );

  // 使用 Portal 确保模态框渲染在 body 下，避免被父容器样式影响
  return createPortal(modalContent, document.body);
};
