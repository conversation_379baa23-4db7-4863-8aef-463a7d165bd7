import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { translationService, TranslationProvider } from '../services/translationService';

interface TranslationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}



export const TranslationSettings: React.FC<TranslationSettingsProps> = ({ isOpen, onClose }) => {
  const [config, setConfig] = useState(translationService.getConfig());
  const [testText, setTestText] = useState('Hello, world!');
  const [testResult, setTestResult] = useState('');
  const [isTestingTranslation, setIsTestingTranslation] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setConfig(translationService.getConfig());
    }
  }, [isOpen]);

  const handleSave = () => {
    translationService.setConfig(config);
    onClose();
  };

  const handleProviderChange = (provider: TranslationProvider) => {
    setConfig(prev => ({ ...prev, preferredProvider: provider }));
  };

  const handleApiKeyChange = (field: string, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };



  const testTranslation = async () => {
    setIsTestingTranslation(true);
    try {
      // 临时应用配置进行测试
      translationService.setConfig(config);
      const result = await translationService.translateText(testText, 'zh');
      setTestResult(result);
    } catch (error) {
      setTestResult(`测试失败: ${error.message}`);
    } finally {
      setIsTestingTranslation(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  const modalContent = (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 999999,
        padding: '20px'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        style={{
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          padding: '32px',
          width: '100%',
          maxWidth: '900px',
          maxHeight: '85vh',
          overflowY: 'auto',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
          position: 'relative',
          border: '1px solid #e5e7eb'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px', borderBottom: '2px solid #f3f4f6', paddingBottom: '16px' }}>
          <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>🌐 翻译设置</h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              color: '#6b7280',
              cursor: 'pointer',
              padding: '8px',
              borderRadius: '6px',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
              e.currentTarget.style.color = '#374151';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = '#6b7280';
            }}
          >
            ✕
          </button>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
          {/* 翻译提供商选择 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: '#1f2937', display: 'flex', alignItems: 'center', gap: '8px' }}>
              🔧 翻译提供商
            </h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
              {Object.values(TranslationProvider).map(provider => (
                <label key={provider} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer',
                  padding: '12px',
                  border: '2px solid',
                  borderColor: config.preferredProvider === provider ? '#3b82f6' : '#e5e7eb',
                  borderRadius: '8px',
                  backgroundColor: config.preferredProvider === provider ? '#eff6ff' : '#ffffff',
                  transition: 'all 0.2s'
                }}>
                  <input
                    type="radio"
                    name="provider"
                    value={provider}
                    checked={config.preferredProvider === provider}
                    onChange={() => handleProviderChange(provider)}
                    style={{ accentColor: '#3b82f6' }}
                  />
                  <span style={{ color: '#374151', fontWeight: '500' }}>
                    {provider === TranslationProvider.GLM && '🤖 智谱清言 (GLM-4-FlashX)'}
                    {provider === TranslationProvider.GOOGLE && '🌍 Google翻译 (免费)'}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* API密钥配置 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: '#1f2937', display: 'flex', alignItems: 'center', gap: '8px' }}>
              🔑 API密钥配置
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {config.preferredProvider === TranslationProvider.GLM && (
                <div style={{ padding: '20px', border: '2px solid #3b82f6', borderRadius: '12px', backgroundColor: '#eff6ff' }}>
                  <label style={{ display: 'block', fontSize: '16px', fontWeight: '600', color: '#1e40af', marginBottom: '12px' }}>
                    🤖 智谱清言 GLM-4-FlashX API Key
                  </label>
                  <input
                    type="password"
                    value={config.glmApiKey || ''}
                    onChange={(e) => handleApiKeyChange('glmApiKey', e.target.value)}
                    placeholder="请输入智谱清言 API Key..."
                    style={{
                      width: '100%',
                      padding: '14px',
                      border: '2px solid #3b82f6',
                      borderRadius: '8px',
                      fontSize: '14px',
                      backgroundColor: '#ffffff',
                      color: '#1f2937',
                      outline: 'none',
                      transition: 'border-color 0.2s'
                    }}
                    onFocus={(e) => e.target.style.borderColor = '#1d4ed8'}
                    onBlur={(e) => e.target.style.borderColor = '#3b82f6'}
                  />
                  <div style={{ marginTop: '12px', padding: '12px', backgroundColor: '#dbeafe', borderRadius: '6px' }}>
                    <p style={{ fontSize: '13px', color: '#1e40af', margin: '0 0 8px 0', fontWeight: '500' }}>
                      📝 获取API Key步骤：
                    </p>
                    <ol style={{ fontSize: '12px', color: '#1e40af', margin: 0, paddingLeft: '16px' }}>
                      <li>访问 <a href="https://open.bigmodel.cn" target="_blank" rel="noopener noreferrer" style={{ color: '#1d4ed8', textDecoration: 'underline' }}>智谱AI开放平台</a></li>
                      <li>注册并登录账户</li>
                      <li>在控制台创建API Key</li>
                      <li>复制API Key并粘贴到上方输入框</li>
                    </ol>
                  </div>
                </div>
              )}

              {config.preferredProvider === TranslationProvider.GOOGLE && (
                <div style={{ padding: '20px', border: '2px solid #10b981', borderRadius: '12px', backgroundColor: '#f0fdf4' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                    <span style={{ fontSize: '16px', fontWeight: '600', color: '#059669' }}>
                      🌍 Google翻译 (免费服务)
                    </span>
                    <span style={{ fontSize: '12px', backgroundColor: '#10b981', color: 'white', padding: '2px 8px', borderRadius: '12px' }}>
                      免费
                    </span>
                  </div>
                  <div style={{ padding: '12px', backgroundColor: '#dcfce7', borderRadius: '6px' }}>
                    <p style={{ fontSize: '13px', color: '#059669', margin: 0, fontWeight: '500' }}>
                      ✅ Google翻译使用免费服务，无需配置API Key即可使用
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 翻译测试 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: '#1f2937', display: 'flex', alignItems: 'center', gap: '8px' }}>
              🧪 翻译测试
            </h3>
            <div style={{ padding: '20px', border: '2px solid #e5e7eb', borderRadius: '12px', backgroundColor: '#f8fafc' }}>
              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '8px' }}>
                  测试文本
                </label>
                <input
                  type="text"
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: '#ffffff',
                    color: '#1f2937',
                    outline: 'none',
                    transition: 'border-color 0.2s'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>

              <button
                onClick={testTranslation}
                disabled={isTestingTranslation}
                style={{
                  padding: '12px 24px',
                  backgroundColor: isTestingTranslation ? '#9ca3af' : '#10b981',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: isTestingTranslation ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s',
                  marginBottom: '16px'
                }}
                onMouseEnter={(e) => {
                  if (!isTestingTranslation) {
                    e.currentTarget.style.backgroundColor = '#059669';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isTestingTranslation) {
                    e.currentTarget.style.backgroundColor = '#10b981';
                  }
                }}
              >
                {isTestingTranslation ? '🔄 测试中...' : '🚀 测试翻译'}
              </button>

              {testResult && (
                <div style={{
                  padding: '16px',
                  backgroundColor: testResult.includes('测试失败') ? '#fef2f2' : '#f0fdf4',
                  border: `2px solid ${testResult.includes('测试失败') ? '#fca5a5' : '#86efac'}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  color: testResult.includes('测试失败') ? '#dc2626' : '#166534'
                }}>
                  <strong>{testResult.includes('测试失败') ? '❌ ' : '✅ '}</strong>
                  {testResult}
                </div>
              )}
            </div>
          </div>

          {/* 其他设置 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: '#1f2937', display: 'flex', alignItems: 'center', gap: '8px' }}>
              ⚙️ 其他设置
            </h3>
            <label style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              cursor: 'pointer',
              padding: '16px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              backgroundColor: '#f9fafb',
              transition: 'all 0.2s'
            }}>
              <input
                type="checkbox"
                checked={config.enableFallback}
                onChange={(e) => setConfig(prev => ({ ...prev, enableFallback: e.target.checked }))}
                style={{ accentColor: '#3b82f6', transform: 'scale(1.2)' }}
              />
              <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>
                🔄 启用降级翻译（翻译失败时自动尝试其他方式）
              </span>
            </label>
          </div>
        </div>

        {/* 底部按钮 */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px',
          marginTop: '32px',
          paddingTop: '24px',
          borderTop: '2px solid #e5e7eb'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 24px',
              color: '#6b7280',
              border: '2px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: '#ffffff',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
              e.currentTarget.style.borderColor = '#9ca3af';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#ffffff';
              e.currentTarget.style.borderColor = '#d1d5db';
            }}
          >
            ❌ 取消
          </button>
          <button
            onClick={handleSave}
            style={{
              padding: '12px 24px',
              backgroundColor: '#3b82f6',
              color: '#ffffff',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
            }}
          >
            💾 保存设置
          </button>
        </div>
      </div>
    </div>
  );

  // 使用 Portal 确保模态框渲染在 body 下，避免被父容器样式影响
  return createPortal(modalContent, document.body);
};
