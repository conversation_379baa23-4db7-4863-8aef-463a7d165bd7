import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { translationService, TranslationProvider } from '../services/translationService';

interface TranslationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}



export const TranslationSettings: React.FC<TranslationSettingsProps> = ({ isOpen, onClose }) => {
  const [config, setConfig] = useState(translationService.getConfig());
  const [testText, setTestText] = useState('Hello, world!');
  const [testResult, setTestResult] = useState('');
  const [isTestingTranslation, setIsTestingTranslation] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setConfig(translationService.getConfig());
    }
  }, [isOpen]);

  const handleSave = () => {
    translationService.setConfig(config);
    onClose();
  };

  const handleProviderChange = (provider: TranslationProvider) => {
    setConfig(prev => ({ ...prev, preferredProvider: provider }));
  };

  const handleApiKeyChange = (field: string, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };



  const testTranslation = async () => {
    setIsTestingTranslation(true);
    try {
      // 临时应用配置进行测试
      translationService.setConfig(config);
      const result = await translationService.translateText(testText, 'zh');
      setTestResult(result);
    } catch (error) {
      setTestResult(`测试失败: ${error.message}`);
    } finally {
      setIsTestingTranslation(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  const modalContent = (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 999999,
        padding: '20px'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        style={{
          backgroundColor: 'var(--card-bg)',
          borderRadius: '12px',
          padding: '32px',
          width: '100%',
          maxWidth: '900px',
          maxHeight: '85vh',
          overflowY: 'auto',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
          position: 'relative',
          border: '1px solid var(--border-color)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px', borderBottom: '2px solid var(--border-color)', paddingBottom: '16px' }}>
          <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: 'var(--text-color)', margin: 0 }}>🌐 翻译设置</h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              color: 'var(--text-color)',
              cursor: 'pointer',
              padding: '8px',
              borderRadius: '6px',
              transition: 'all 0.2s',
              opacity: 0.7
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--border-color)';
              e.currentTarget.style.opacity = '1';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.opacity = '0.7';
            }}
          >
            ✕
          </button>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
          {/* 翻译提供商选择 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: 'var(--text-color)', display: 'flex', alignItems: 'center', gap: '8px' }}>
              🔧 翻译提供商
            </h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
              {Object.values(TranslationProvider).map(provider => (
                <label key={provider} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer',
                  padding: '12px',
                  border: '2px solid',
                  borderColor: config.preferredProvider === provider ? 'var(--primary-color)' : 'var(--border-color)',
                  borderRadius: '8px',
                  backgroundColor: config.preferredProvider === provider ? 'var(--primary-color)20' : 'var(--card-bg)',
                  transition: 'all 0.2s'
                }}>
                  <input
                    type="radio"
                    name="provider"
                    value={provider}
                    checked={config.preferredProvider === provider}
                    onChange={() => handleProviderChange(provider)}
                    style={{ accentColor: 'var(--primary-color)' }}
                  />
                  <span style={{ color: 'var(--text-color)', fontWeight: '500' }}>
                    {provider === TranslationProvider.GLM && '🤖 智谱清言 (GLM-4-FlashX)'}
                    {provider === TranslationProvider.GOOGLE && '🌍 Google翻译 (免费)'}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* API密钥配置 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: 'var(--text-color)', display: 'flex', alignItems: 'center', gap: '8px' }}>
              🔑 API密钥配置
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {config.preferredProvider === TranslationProvider.GLM && (
                <div style={{ padding: '20px', border: '2px solid var(--primary-color)', borderRadius: '12px', backgroundColor: 'var(--primary-color)10' }}>
                  <label style={{ display: 'block', fontSize: '16px', fontWeight: '600', color: 'var(--primary-color)', marginBottom: '12px' }}>
                    🤖 智谱清言 GLM-4-FlashX API Key
                  </label>
                  <input
                    type="password"
                    value={config.glmApiKey || ''}
                    onChange={(e) => handleApiKeyChange('glmApiKey', e.target.value)}
                    placeholder="请输入智谱清言 API Key..."
                    style={{
                      width: '100%',
                      padding: '14px',
                      border: '2px solid var(--primary-color)',
                      borderRadius: '8px',
                      fontSize: '14px',
                      backgroundColor: 'var(--card-bg)',
                      color: 'var(--text-color)',
                      outline: 'none',
                      transition: 'border-color 0.2s'
                    }}
                    onFocus={(e) => e.target.style.borderColor = 'var(--primary-color)'}
                    onBlur={(e) => e.target.style.borderColor = 'var(--primary-color)'}
                  />
                  <div style={{ marginTop: '12px', padding: '12px', backgroundColor: 'var(--primary-color)15', borderRadius: '6px' }}>
                    <p style={{ fontSize: '13px', color: 'var(--primary-color)', margin: '0 0 8px 0', fontWeight: '500' }}>
                      📝 获取API Key步骤：
                    </p>
                    <ol style={{ fontSize: '12px', color: 'var(--primary-color)', margin: 0, paddingLeft: '16px' }}>
                      <li>访问 <a href="https://open.bigmodel.cn" target="_blank" rel="noopener noreferrer" style={{ color: 'var(--primary-color)', textDecoration: 'underline' }}>智谱AI开放平台</a></li>
                      <li>注册并登录账户</li>
                      <li>在控制台创建API Key</li>
                      <li>复制API Key并粘贴到上方输入框</li>
                    </ol>
                  </div>
                </div>
              )}

              {config.preferredProvider === TranslationProvider.GOOGLE && (
                <div style={{ padding: '20px', border: '2px solid var(--accent-color)', borderRadius: '12px', backgroundColor: 'var(--accent-color)10' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                    <span style={{ fontSize: '16px', fontWeight: '600', color: 'var(--accent-color)' }}>
                      🌍 Google翻译 (免费服务)
                    </span>
                    <span style={{ fontSize: '12px', backgroundColor: 'var(--accent-color)', color: 'white', padding: '2px 8px', borderRadius: '12px' }}>
                      免费
                    </span>
                  </div>
                  <div style={{ padding: '12px', backgroundColor: 'var(--accent-color)15', borderRadius: '6px' }}>
                    <p style={{ fontSize: '13px', color: 'var(--accent-color)', margin: 0, fontWeight: '500' }}>
                      ✅ Google翻译使用免费服务，无需配置API Key即可使用
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 翻译测试 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: 'var(--text-color)', display: 'flex', alignItems: 'center', gap: '8px' }}>
              🧪 翻译测试
            </h3>
            <div style={{ padding: '20px', border: '2px solid var(--border-color)', borderRadius: '12px', backgroundColor: 'var(--card-bg)' }}>
              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: 'var(--text-color)', marginBottom: '8px' }}>
                  测试文本
                </label>
                <input
                  type="text"
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid var(--border-color)',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: 'var(--card-bg)',
                    color: 'var(--text-color)',
                    outline: 'none',
                    transition: 'border-color 0.2s'
                  }}
                  onFocus={(e) => e.target.style.borderColor = 'var(--primary-color)'}
                  onBlur={(e) => e.target.style.borderColor = 'var(--border-color)'}
                />
              </div>

              <button
                onClick={testTranslation}
                disabled={isTestingTranslation}
                style={{
                  padding: '12px 24px',
                  backgroundColor: isTestingTranslation ? 'var(--border-color)' : 'var(--accent-color)',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: isTestingTranslation ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s',
                  marginBottom: '16px'
                }}
                onMouseEnter={(e) => {
                  if (!isTestingTranslation) {
                    e.currentTarget.style.opacity = '0.8';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isTestingTranslation) {
                    e.currentTarget.style.opacity = '1';
                  }
                }}
              >
                {isTestingTranslation ? '🔄 测试中...' : '🚀 测试翻译'}
              </button>

              {testResult && (
                <div style={{
                  padding: '16px',
                  backgroundColor: testResult.includes('测试失败') ? '#ff4d4f20' : 'var(--accent-color)20',
                  border: `2px solid ${testResult.includes('测试失败') ? '#ff4d4f' : 'var(--accent-color)'}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  color: testResult.includes('测试失败') ? '#ff4d4f' : 'var(--accent-color)'
                }}>
                  <strong>{testResult.includes('测试失败') ? '❌ ' : '✅ '}</strong>
                  {testResult}
                </div>
              )}
            </div>
          </div>

          {/* 其他设置 */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px', color: 'var(--text-color)', display: 'flex', alignItems: 'center', gap: '8px' }}>
              ⚙️ 其他设置
            </h3>
            <label style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              cursor: 'pointer',
              padding: '16px',
              border: '2px solid var(--border-color)',
              borderRadius: '8px',
              backgroundColor: 'var(--card-bg)',
              transition: 'all 0.2s'
            }}>
              <input
                type="checkbox"
                checked={config.enableFallback}
                onChange={(e) => setConfig(prev => ({ ...prev, enableFallback: e.target.checked }))}
                style={{ accentColor: 'var(--primary-color)', transform: 'scale(1.2)' }}
              />
              <span style={{ color: 'var(--text-color)', fontSize: '14px', fontWeight: '500' }}>
                🔄 启用降级翻译（翻译失败时自动尝试其他方式）
              </span>
            </label>
          </div>
        </div>

        {/* 底部按钮 */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px',
          marginTop: '32px',
          paddingTop: '24px',
          borderTop: '2px solid var(--border-color)'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 24px',
              color: 'var(--text-color)',
              border: '2px solid var(--border-color)',
              borderRadius: '8px',
              backgroundColor: 'var(--card-bg)',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.8';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
            }}
          >
            ❌ 取消
          </button>
          <button
            onClick={handleSave}
            style={{
              padding: '12px 24px',
              backgroundColor: 'var(--primary-color)',
              color: '#ffffff',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.8';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
            }}
          >
            💾 保存设置
          </button>
        </div>
      </div>
    </div>
  );

  // 使用 Portal 确保模态框渲染在 body 下，避免被父容器样式影响
  return createPortal(modalContent, document.body);
};
