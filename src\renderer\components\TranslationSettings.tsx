/**
 * 翻译设置组件
 */

import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Switch, Button, Space, Typography, Alert } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { translationService, TranslationProvider } from '../services/translationService';

const { Title, Text } = Typography;
const { Option } = Select;

interface TranslationSettingsProps {
  visible: boolean;
  onClose: () => void;
}

const TranslationSettings: React.FC<TranslationSettingsProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      // 加载当前配置
      const config = translationService.getConfig();
      form.setFieldsValue({
        googleApiKey: config.googleApiKey || '',
        preferredProvider: config.preferredProvider,
        enableFallback: config.enableFallback,
      });
    }
  }, [visible, form]);

  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // 保存配置
      translationService.setConfig({
        googleApiKey: values.googleApiKey || undefined,
        preferredProvider: values.preferredProvider,
        enableFallback: values.enableFallback,
      });

      onClose();
    } catch (error) {
      console.error('Failed to save translation settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // 临时设置配置进行测试
      const originalConfig = translationService.getConfig();
      translationService.setConfig({
        googleApiKey: values.googleApiKey || undefined,
        preferredProvider: values.preferredProvider,
        enableFallback: values.enableFallback,
      });

      // 测试翻译
      const testText = '这是一个测试文本';
      const result = await translationService.translateText(testText, 'en');
      
      // 恢复原配置
      translationService.setConfig(originalConfig);
      
      Modal.success({
        title: '翻译测试成功',
        content: (
          <div>
            <p><strong>原文：</strong>{testText}</p>
            <p><strong>译文：</strong>{result}</p>
          </div>
        ),
      });
    } catch (error) {
      Modal.error({
        title: '翻译测试失败',
        content: `错误信息：${error instanceof Error ? error.message : '未知错误'}`,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          翻译设置
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button onClick={handleTest} loading={loading}>
            测试翻译
          </Button>
          <Button type="primary" onClick={handleSave} loading={loading}>
            保存设置
          </Button>
        </Space>
      }
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          preferredProvider: TranslationProvider.MYMEMORY,
          enableFallback: true,
        }}
      >
        <Alert
          message="翻译服务说明"
          description={
            <div>
              <p><strong>Google翻译：</strong>质量最高，需要API密钥，可能产生费用</p>
              <p><strong>MyMemory：</strong>免费服务，质量较好，有使用限制</p>
              <p><strong>本地词汇：</strong>离线翻译，仅支持预定义词汇</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form.Item
          label="首选翻译服务"
          name="preferredProvider"
          tooltip="选择主要使用的翻译服务"
        >
          <Select>
            <Option value={TranslationProvider.GOOGLE}>Google翻译</Option>
            <Option value={TranslationProvider.MYMEMORY}>MyMemory（免费）</Option>
            <Option value={TranslationProvider.LOCAL}>本地词汇</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="Google翻译API密钥"
          name="googleApiKey"
          tooltip="从Google Cloud Console获取API密钥"
        >
          <Input.Password
            placeholder="输入Google Translate API密钥（可选）"
            visibilityToggle
          />
        </Form.Item>

        <Form.Item
          label="启用降级翻译"
          name="enableFallback"
          valuePropName="checked"
          tooltip="当首选服务失败时，自动尝试其他翻译服务"
        >
          <Switch />
        </Form.Item>

        <Alert
          message="获取Google翻译API密钥"
          description={
            <div>
              <p>1. 访问 <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer">Google Cloud Console</a></p>
              <p>2. 创建项目并启用 Cloud Translation API</p>
              <p>3. 创建API密钥并复制到上方输入框</p>
              <p>4. 注意：Google翻译API可能产生费用，请查看定价信息</p>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      </Form>
    </Modal>
  );
};

export default TranslationSettings;
