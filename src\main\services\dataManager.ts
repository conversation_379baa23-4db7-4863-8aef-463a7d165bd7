/**
 * 数据管理服务
 * 负责处理JSON文件的读写操作和数据管理
 */

import * as fs from 'fs';
import * as path from 'path';
import { Category, Author, Project } from '@/shared/types';

// 数据文件路径
const DATA_DIR = path.join(process.cwd(), 'data');
const CONFIG_FILE = path.join(DATA_DIR, 'config.json');
const CATEGORIES_FILE = path.join(DATA_DIR, 'categories.json');
const AUTHORS_FILE = path.join(DATA_DIR, 'authors.json');
const PROJECTS_FILE = path.join(DATA_DIR, 'projects.json');
const CACHE_FILE = path.join(DATA_DIR, 'cache.json');
const HISTORY_FILE = path.join(DATA_DIR, 'history.json');
const STATISTICS_FILE = path.join(DATA_DIR, 'statistics.json');

/**
 * 数据管理器类
 */
export class DataManager {
  private static instance: DataManager;

  private constructor() {
    this.ensureDataDirectory();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  /**
   * 确保数据目录存在
   */
  private ensureDataDirectory(): void {
    if (!fs.existsSync(DATA_DIR)) {
      fs.mkdirSync(DATA_DIR, { recursive: true });
    }
  }

  /**
   * 读取JSON文件
   */
  private readJSONFile<T>(filePath: string, defaultValue: T): T {
    try {
      if (!fs.existsSync(filePath)) {
        this.writeJSONFile(filePath, defaultValue);
        return defaultValue;
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const data = JSON.parse(content);
      return data;
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      return defaultValue;
    }
  }

  /**
   * 写入JSON文件
   */
  private writeJSONFile<T>(filePath: string, data: T): void {
    try {
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
      console.error(`Error writing file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): any {
    return {
      version: '1.0.0',
      theme: 'dark',
      language: 'zh',
      github: {
        token: '',
        rateLimit: 5000,
        concurrent: 3,
      },
      update: {
        interval: 3600000,
        autoCheck: true,
      },
    };
  }

  // 配置相关方法
  public getConfig(): any {
    return this.readJSONFile(CONFIG_FILE, this.getDefaultConfig());
  }

  public setConfig(config: any): void {
    this.writeJSONFile(CONFIG_FILE, config);
  }

  // 分类相关方法
  public getCategories(): Category[] {
    return this.readJSONFile<Category[]>(CATEGORIES_FILE, []);
  }

  public setCategories(categories: Category[]): void {
    this.writeJSONFile(CATEGORIES_FILE, categories);
  }

  public addCategory(category: Category): void {
    const categories = this.getCategories();
    categories.push(category);
    this.setCategories(categories);
  }

  public updateCategory(categoryId: string, updates: Partial<Category>): void {
    const categories = this.getCategories();
    const index = categories.findIndex(c => c.id === categoryId);
    if (index !== -1) {
      categories[index] = { ...categories[index], ...updates, updatedAt: new Date().toISOString() };
      this.setCategories(categories);
    }
  }

  public deleteCategory(categoryId: string): void {
    const categories = this.getCategories();
    const filtered = categories.filter(c => c.id !== categoryId);
    this.setCategories(filtered);
  }

  // 作者相关方法
  public getAuthors(): Author[] {
    return this.readJSONFile<Author[]>(AUTHORS_FILE, []);
  }

  public setAuthors(authors: Author[]): void {
    this.writeJSONFile(AUTHORS_FILE, authors);
  }

  public addAuthor(author: Author): void {
    const authors = this.getAuthors();
    authors.push(author);
    this.setAuthors(authors);
  }

  public updateAuthor(authorId: string, updates: Partial<Author>): void {
    const authors = this.getAuthors();
    const index = authors.findIndex(a => a.id === authorId);
    if (index !== -1) {
      authors[index] = { ...authors[index], ...updates, updatedAt: new Date().toISOString() };
      this.setAuthors(authors);
    }
  }

  public deleteAuthor(authorId: string): void {
    const authors = this.getAuthors();
    const filtered = authors.filter(a => a.id !== authorId);
    this.setAuthors(filtered);
  }

  // 项目相关方法
  public getProjects(): Project[] {
    return this.readJSONFile<Project[]>(PROJECTS_FILE, []);
  }

  public setProjects(projects: Project[]): void {
    this.writeJSONFile(PROJECTS_FILE, projects);
  }

  public addProject(project: Project): void {
    const projects = this.getProjects();
    projects.push(project);
    this.setProjects(projects);
  }

  public updateProject(projectId: string, updates: Partial<Project>): void {
    const projects = this.getProjects();
    const index = projects.findIndex(p => p.id === projectId);
    if (index !== -1) {
      projects[index] = { ...projects[index], ...updates, updatedAt: new Date().toISOString() };
      this.setProjects(projects);
    }
  }

  public deleteProject(projectId: string): void {
    const projects = this.getProjects();
    const filtered = projects.filter(p => p.id !== projectId);
    this.setProjects(filtered);
  }

  // 缓存相关方法
  public getCache(): any {
    return this.readJSONFile(CACHE_FILE, {});
  }

  public setCache(cache: any): void {
    this.writeJSONFile(CACHE_FILE, cache);
  }

  public getCacheItem(key: string): any {
    const cache = this.getCache();
    return cache[key];
  }

  public setCacheItem(key: string, value: any, ttl?: number): void {
    const cache = this.getCache();
    cache[key] = {
      value,
      timestamp: Date.now(),
      ttl: ttl || 3600000, // 默认1小时
    };
    this.setCache(cache);
  }

  public clearExpiredCache(): void {
    const cache = this.getCache();
    const now = Date.now();
    const cleaned: any = {};

    for (const [key, item] of Object.entries(cache)) {
      const cacheItem = item as any;
      if (cacheItem.timestamp + cacheItem.ttl > now) {
        cleaned[key] = item;
      }
    }

    this.setCache(cleaned);
  }

  // 历史记录相关方法
  public getHistory(): any[] {
    return this.readJSONFile<any[]>(HISTORY_FILE, []);
  }

  public addHistoryItem(item: any): void {
    const history = this.getHistory();
    history.unshift({
      ...item,
      timestamp: new Date().toISOString(),
    });

    // 保持最近1000条记录
    if (history.length > 1000) {
      history.splice(1000);
    }

    this.writeJSONFile(HISTORY_FILE, history);
  }

  // 统计数据相关方法
  public getStatistics(): any {
    return this.readJSONFile(STATISTICS_FILE, {});
  }

  public setStatistics(statistics: any): void {
    this.writeJSONFile(STATISTICS_FILE, statistics);
  }

  // 数据备份和恢复
  public backupData(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(DATA_DIR, 'backups');
    
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const backupFile = path.join(backupDir, `backup-${timestamp}.json`);
    const backupData = {
      timestamp,
      config: this.getConfig(),
      categories: this.getCategories(),
      authors: this.getAuthors(),
      projects: this.getProjects(),
      statistics: this.getStatistics(),
    };

    this.writeJSONFile(backupFile, backupData);
    return backupFile;
  }

  public restoreData(backupFile: string): void {
    const backupData = this.readJSONFile<any>(backupFile, null);
    if (!backupData) {
      throw new Error('Invalid backup file');
    }

    if (backupData.config) this.setConfig(backupData.config);
    if (backupData.categories) this.setCategories(backupData.categories);
    if (backupData.authors) this.setAuthors(backupData.authors);
    if (backupData.projects) this.setProjects(backupData.projects);
    if (backupData.statistics) this.setStatistics(backupData.statistics);
  }
}
