/**
 * 项目相关类型定义
 */

// 项目接口
export interface Project {
  id: string;
  name: string; // 项目名称
  fullName: string; // 完整名称 (owner/repo)
  owner: string; // 项目所有者
  description?: string;
  url: string; // GitHub URL
  homepage?: string; // 项目主页
  language?: string; // 主要编程语言
  categoryId: string; // 所属分类ID
  isActive: boolean;
  isWatching: boolean; // 是否监控该项目
  isFavorite: boolean; // 是否收藏
  createdAt: string;
  updatedAt: string;
  lastCheckedAt?: string; // 最后检查时间
  metadata?: ProjectMetadata;
  githubData?: GitHubProjectData;
}

// 项目元数据接口
export interface ProjectMetadata {
  stars?: number; // 星标数
  forks?: number; // 分叉数
  watchers?: number; // 观察者数
  issues?: number; // 问题数
  pullRequests?: number; // 拉取请求数
  size?: number; // 仓库大小 (KB)
  topics?: string[]; // 主题标签
  license?: string; // 许可证
  tags?: string[]; // 自定义标签
  notes?: string; // 备注
  priority?: number; // 优先级 (1-5)
  lastCommitAt?: string; // 最后提交时间
  lastReleaseAt?: string; // 最后发布时间
  hasUpdate?: boolean; // 是否有更新
  lastUpdateCheck?: string; // 最后更新检查时间
}

// GitHub项目数据接口
export interface GitHubProjectData {
  id: number;
  nodeId: string;
  private: boolean;
  fork: boolean;
  archived: boolean;
  disabled: boolean;
  pushedAt: string;
  gitUrl: string;
  sshUrl: string;
  cloneUrl: string;
  svnUrl: string;
  mirrorUrl?: string;
  hasIssues: boolean;
  hasProjects: boolean;
  hasWiki: boolean;
  hasPages: boolean;
  hasDownloads: boolean;
  defaultBranch: string;
  openIssuesCount: number;
  allowForking: boolean;
  isTemplate: boolean;
  visibility: 'public' | 'private';
}

// 项目创建请求接口
export interface CreateProjectRequest {
  name: string;
  owner: string;
  categoryId: string;
  isWatching?: boolean;
  isFavorite?: boolean;
  priority?: number;
  tags?: string[];
  notes?: string;
}

// 项目更新请求接口
export interface UpdateProjectRequest {
  id: string;
  categoryId?: string;
  isActive?: boolean;
  isWatching?: boolean;
  isFavorite?: boolean;
  priority?: number;
  tags?: string[];
  notes?: string;
}

// 项目统计接口
export interface ProjectStats {
  id: string;
  name: string;
  fullName: string;
  stars: number;
  forks: number;
  watchers: number;
  issues: number;
  pullRequests: number;
  commits: number;
  contributors: number;
  releases: number;
  lastCommitAt: string;
  lastReleaseAt?: string;
  activityScore: number; // 活跃度评分
  trendingScore: number; // 趋势评分
}

// 项目活动记录接口
export interface ProjectActivity {
  id: string;
  projectId: string;
  type: ProjectActivityType;
  title: string;
  description?: string;
  url?: string;
  author?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

// 项目活动类型
export type ProjectActivityType = 
  | 'commit_pushed'
  | 'release_published'
  | 'issue_opened'
  | 'issue_closed'
  | 'pull_request_opened'
  | 'pull_request_merged'
  | 'pull_request_closed'
  | 'star_added'
  | 'fork_created'
  | 'branch_created'
  | 'branch_deleted'
  | 'tag_created'
  | 'wiki_updated';

// 项目发布信息接口
export interface ProjectRelease {
  id: string;
  projectId: string;
  tagName: string;
  name: string;
  body?: string;
  isDraft: boolean;
  isPrerelease: boolean;
  publishedAt: string;
  author: string;
  assets: ReleaseAsset[];
  downloadCount: number;
}

// 发布资源接口
export interface ReleaseAsset {
  id: string;
  name: string;
  label?: string;
  contentType: string;
  size: number;
  downloadCount: number;
  browserDownloadUrl: string;
}

// 项目排序选项
export type ProjectSortField = 
  | 'name'
  | 'fullName'
  | 'createdAt'
  | 'updatedAt'
  | 'lastCheckedAt'
  | 'lastCommitAt'
  | 'stars'
  | 'forks'
  | 'watchers'
  | 'activityScore'
  | 'trendingScore';

// 项目过滤选项接口
export interface ProjectFilter {
  categoryId?: string;
  owner?: string;
  language?: string;
  isActive?: boolean;
  isWatching?: boolean;
  isFavorite?: boolean;
  priority?: number;
  topics?: string[];
  tags?: string[];
  search?: string;
  hasActivity?: boolean;
  lastActivityDays?: number;
  minStars?: number;
  maxStars?: number;
  minForks?: number;
  maxForks?: number;
}

// 项目批量操作接口
export interface ProjectBatchOperation {
  action: 'activate' | 'deactivate' | 'watch' | 'unwatch' | 'favorite' | 'unfavorite' | 'delete' | 'move' | 'update';
  projectIds: string[];
  data?: Partial<Project>;
}

// 项目搜索结果接口
export interface ProjectSearchResult {
  projects: Project[];
  total: number;
  hasMore: boolean;
  searchTime: number;
  suggestions?: string[];
  facets?: ProjectSearchFacets;
}

// 项目搜索分面接口
export interface ProjectSearchFacets {
  languages: { name: string; count: number }[];
  topics: { name: string; count: number }[];
  owners: { name: string; count: number }[];
  categories: { id: string; name: string; count: number }[];
}

// 项目导入/导出接口
export interface ProjectExportData {
  projects: Project[];
  metadata: {
    exportedAt: string;
    version: string;
    totalCount: number;
    categories: { id: string; name: string }[];
  };
}

// 项目同步状态接口
export interface ProjectSyncStatus {
  projectId: string;
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  progress: number; // 0-100
  message?: string;
  startedAt: string;
  completedAt?: string;
  error?: string;
}

// 项目推荐接口
export interface ProjectRecommendation {
  fullName: string;
  name: string;
  description?: string;
  language?: string;
  stars: number;
  forks: number;
  topics: string[];
  reason: string; // 推荐理由
  score: number; // 推荐分数
  similarProjects: string[]; // 相似项目
}
