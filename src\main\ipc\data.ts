/**
 * 数据操作IPC处理器
 * 负责处理数据相关的IPC通信
 */

import { ipcMain } from 'electron';
import { handleIPCError, handleIPCSuccess } from './index';
import { DataManager } from '../services/dataManager';
import { ConfigManager } from '../services/configManager';

// 获取服务实例
const dataManager = DataManager.getInstance();
const configManager = ConfigManager.getInstance();



/**
 * 注册数据操作IPC处理器
 */
export function registerDataHandlers(): void {
  // 配置相关
  ipcMain.handle('data:getConfig', async () => {
    try {
      const config = configManager.loadConfig();
      return handleIPCSuccess(config);
    } catch (error) {
      return handleIPCError('data:getConfig', error);
    }
  });

  ipcMain.handle('data:setConfig', async (_, config) => {
    try {
      configManager.saveConfig(config);
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('data:setConfig', error);
    }
  });

  // 分类相关
  ipcMain.handle('data:getCategories', async () => {
    try {
      const categories = dataManager.getCategories();
      return handleIPCSuccess(categories);
    } catch (error) {
      return handleIPCError('data:getCategories', error);
    }
  });

  ipcMain.handle('data:setCategories', async (_, categories) => {
    try {
      dataManager.setCategories(categories);
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('data:setCategories', error);
    }
  });

  // 作者相关
  ipcMain.handle('data:getAuthors', async () => {
    try {
      const authors = dataManager.getAuthors();
      return handleIPCSuccess(authors);
    } catch (error) {
      return handleIPCError('data:getAuthors', error);
    }
  });

  ipcMain.handle('data:setAuthors', async (_, authors) => {
    try {
      dataManager.setAuthors(authors);
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('data:setAuthors', error);
    }
  });

  // 项目相关
  ipcMain.handle('data:getProjects', async () => {
    try {
      const projects = dataManager.getProjects();
      return handleIPCSuccess(projects);
    } catch (error) {
      return handleIPCError('data:getProjects', error);
    }
  });

  ipcMain.handle('data:setProjects', async (_, projects) => {
    try {
      dataManager.setProjects(projects);
      return handleIPCSuccess();
    } catch (error) {
      return handleIPCError('data:setProjects', error);
    }
  });

  console.log('✓ Data IPC handlers registered');
}
